<template>
  <v-container fluid class="dashboard-page">
    <v-container class="py-8">
      <!-- Performance Metrics -->
      <v-row class="mb-8">
        <v-col cols="12">
          <h2 class="section-title mb-6">
            <v-icon color="primary" class="mr-3">mdi-chart-line</v-icon>
            Performance Metrics
          </h2>
        </v-col>

        <!-- Enhanced Metric Cards -->
        <v-col cols="12" sm="6" md="3">
          <div class="metric-card metric-card--primary">
            <div class="metric-header">
              <div class="metric-icon">
                <v-icon size="32" color="white">mdi-currency-usd</v-icon>
              </div>
              <div class="metric-trend metric-trend--up">
                <v-icon size="16">mdi-trending-up</v-icon>
                +24.5%
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-value">$127,450</div>
              <div class="metric-label">Total Commissions</div>
              <div class="metric-sublabel">This month</div>
            </div>
            <div class="metric-chart">
              <MiniChart
                title=""
                :value="127450"
                :change="24.5"
                :data="commissionTrendData"
                color="rgba(255, 255, 255, 0.3)"
                :show-details="false"
              />
            </div>
          </div>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <div class="metric-card metric-card--success">
            <div class="metric-header">
              <div class="metric-icon">
                <v-icon size="32" color="white">mdi-account-group</v-icon>
              </div>
              <div class="metric-trend metric-trend--up">
                <v-icon size="16">mdi-trending-up</v-icon>
                +12.0%
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-value">2,847</div>
              <div class="metric-label">Active Users</div>
              <div class="metric-sublabel">Currently online</div>
            </div>
            <div class="metric-chart">
              <MiniChart
                title=""
                :value="2847"
                :change="12.0"
                :data="userTrendData"
                color="rgba(255, 255, 255, 0.3)"
                :show-details="false"
              />
            </div>
          </div>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <div class="metric-card metric-card--info">
            <div class="metric-header">
              <div class="metric-icon">
                <v-icon size="32" color="white">mdi-calculator-variant</v-icon>
              </div>
              <div class="metric-trend metric-trend--up">
                <v-icon size="16">mdi-trending-up</v-icon>
                +8.3%
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-value">1,456</div>
              <div class="metric-label">Calculations</div>
              <div class="metric-sublabel">This week</div>
            </div>
            <div class="metric-chart">
              <MiniChart
                title=""
                :value="1456"
                :change="8.3"
                :data="calculationTrendData"
                color="rgba(255, 255, 255, 0.3)"
                :show-details="false"
              />
            </div>
          </div>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <div class="metric-card metric-card--warning">
            <div class="metric-header">
              <div class="metric-icon">
                <v-icon size="32" color="white">mdi-clock-outline</v-icon>
              </div>
              <div class="metric-trend metric-trend--down">
                <v-icon size="16">mdi-trending-down</v-icon>
                -2.1%
              </div>
            </div>
            <div class="metric-content">
              <div class="metric-value">34</div>
              <div class="metric-label">Pending Reviews</div>
              <div class="metric-sublabel">Awaiting approval</div>
            </div>
            <div class="metric-chart">
              <MiniChart
                title=""
                :value="34"
                :change="-2.1"
                :data="pendingTrendData"
                color="rgba(255, 255, 255, 0.3)"
                :show-details="false"
              />
            </div>
          </div>
        </v-col>
      </v-row>

      <!-- Charts Section -->
      <v-row class="mb-8">
        <v-col cols="12">
          <h2 class="section-title mb-6">
            <v-icon color="primary" class="mr-3">mdi-chart-areaspline</v-icon>
            Commission Trends & Analytics
          </h2>
        </v-col>

        <!-- Commission Trends Chart -->
        <v-col cols="12" lg="8">
          <v-card class="chart-card" elevation="3">
            <v-card-title class="chart-card-header">
              <div class="d-flex align-center justify-space-between w-100">
                <div>
                  <h3 class="text-h6 mb-1">Commission Performance</h3>
                  <p class="text-body-2 text-medium-emphasis">
                    Monthly trends and projections
                  </p>
                </div>
                <div class="chart-controls">
                  <v-btn-toggle
                    v-model="selectedPeriod"
                    variant="outlined"
                    density="compact"
                    size="small"
                  >
                    <v-btn value="6m">6M</v-btn>
                    <v-btn value="1y">1Y</v-btn>
                    <v-btn value="2y">2Y</v-btn>
                  </v-btn-toggle>
                </div>
              </div>
            </v-card-title>
            <v-card-text class="pa-6">
              <CommissionTrendsChart
                :loading="chartsLoading"
                :data="commissionChartData"
                :labels="commissionChartLabels"
              />
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Commission Distribution -->
        <v-col cols="12" lg="4">
          <v-card class="chart-card" elevation="3">
            <v-card-title class="chart-card-header">
              <div>
                <h3 class="text-h6 mb-1">Commission Distribution</h3>
                <p class="text-body-2 text-medium-emphasis">By department</p>
              </div>
            </v-card-title>
            <v-card-text class="pa-6">
              <DonutChart
                title=""
                :data="commissionDistributionData"
                :colors="[
                  '#1976D2',
                  '#4CAF50',
                  '#FF9800',
                  '#F44336',
                  '#9C27B0',
                ]"
                center-label="Total"
              />
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- Activity and Quick Actions -->
      <v-row>
        <!-- Recent Activity -->
        <v-col cols="12" md="8">
          <v-card class="activity-card" elevation="3">
            <v-card-title class="activity-card-header">
              <div class="d-flex align-center justify-space-between w-100">
                <div>
                  <h3 class="text-h6 mb-1">
                    <v-icon color="primary" class="mr-2"
                      >mdi-timeline-clock</v-icon
                    >
                    Recent Activity
                  </h3>
                  <p class="text-body-2 text-medium-emphasis">
                    Latest system updates and transactions
                  </p>
                </div>
                <v-btn
                  icon
                  size="small"
                  variant="text"
                  @click="refreshActivities"
                >
                  <v-icon>mdi-refresh</v-icon>
                </v-btn>
              </div>
            </v-card-title>

            <v-card-text class="pa-0">
              <v-list class="activity-list">
                <v-list-item
                  v-for="(activity, index) in recentActivities"
                  :key="index"
                  class="activity-item"
                >
                  <template #prepend>
                    <div class="activity-avatar">
                      <v-avatar
                        :color="activity.color"
                        size="44"
                        variant="tonal"
                      >
                        <v-icon :color="activity.color" size="20">{{
                          activity.icon
                        }}</v-icon>
                      </v-avatar>
                    </div>
                  </template>

                  <div class="activity-content">
                    <v-list-item-title class="activity-title">{{
                      activity.title
                    }}</v-list-item-title>
                    <v-list-item-subtitle class="activity-description">{{
                      activity.description
                    }}</v-list-item-subtitle>
                  </div>

                  <template #append>
                    <div class="activity-time">
                      <span class="text-caption text-medium-emphasis">{{
                        activity.time
                      }}</span>
                      <v-icon size="16" class="ml-2 text-medium-emphasis"
                        >mdi-chevron-right</v-icon
                      >
                    </div>
                  </template>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </v-col>

        <!-- Enhanced Quick Actions -->
        <v-col cols="12" md="4">
          <v-card class="quick-actions-card" elevation="3">
            <v-card-title class="quick-actions-header">
              <div>
                <h3 class="text-h6 mb-1">
                  <v-icon color="primary" class="mr-2"
                    >mdi-lightning-bolt</v-icon
                  >
                  Quick Actions
                </h3>
                <p class="text-body-2 text-medium-emphasis">
                  Common tasks and shortcuts
                </p>
              </div>
            </v-card-title>

            <v-card-text class="pa-4">
              <div class="quick-actions-grid">
                <div
                  v-for="action in quickActions"
                  :key="action.id"
                  class="quick-action-item"
                  @click="handleQuickAction(action.action)"
                >
                  <div class="quick-action-icon">
                    <v-icon :color="action.color" size="24">{{
                      action.icon
                    }}</v-icon>
                  </div>
                  <div class="quick-action-content">
                    <div class="quick-action-title">{{ action.title }}</div>
                    <div class="quick-action-description">
                      {{ action.description }}
                    </div>
                  </div>
                  <v-icon size="16" class="quick-action-arrow"
                    >mdi-chevron-right</v-icon
                  >
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import MiniChart from "@/components/charts/MiniChart.vue";
import CommissionTrendsChart from "@/components/charts/CommissionTrendsChart.vue";
import DonutChart from "@/components/charts/DonutChart.vue";

// Composables
const router = useRouter();

// Reactive data
const selectedPeriod = ref("1y");
const chartsLoading = ref(false);

// Chart data
const commissionTrendData = ref([
  42000, 45000, 38000, 51000, 47000, 53000, 58000, 67000,
]);
const userTrendData = ref([2100, 2250, 2180, 2400, 2350, 2500, 2650, 2847]);
const calculationTrendData = ref([1200, 1300, 1250, 1400, 1350, 1450, 1456]);
const pendingTrendData = ref([45, 38, 42, 35, 40, 36, 34]);

// Commission chart data
const commissionChartData = ref([
  42000, 45000, 38000, 51000, 47000, 53000, 58000, 67000, 72000, 68000, 75000,
  82000,
]);
const commissionChartLabels = ref([
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
]);

// Commission distribution data
const commissionDistributionData = ref([
  { label: "Sales", value: 45000, color: "#1976D2" },
  { label: "Marketing", value: 32000, color: "#4CAF50" },
  { label: "Support", value: 28000, color: "#FF9800" },
  { label: "Development", value: 22000, color: "#F44336" },
  { label: "Operations", value: 18000, color: "#9C27B0" },
]);

const recentActivities = ref([
  {
    title: "Commission Calculated",
    description: "Sarah Johnson calculated Q4 sales commission",
    time: "2 min ago",
    icon: "mdi-calculator-variant",
    color: "success",
  },
  {
    title: "New Client Added",
    description: "TechCorp Solutions joined the system",
    time: "15 min ago",
    icon: "mdi-domain",
    color: "primary",
  },
  {
    title: "Report Generated",
    description: "Monthly performance report created",
    time: "1 hour ago",
    icon: "mdi-file-chart",
    color: "info",
  },
  {
    title: "Review Pending",
    description: "3 transactions awaiting approval",
    time: "2 hours ago",
    icon: "mdi-clock-outline",
    color: "warning",
  },
]);

const quickActions = ref([
  {
    id: 1,
    title: "Calculate Commission",
    description: "Start new calculation",
    icon: "mdi-calculator-variant",
    color: "primary",
    action: "calculate",
  },
  {
    id: 2,
    title: "Add Client",
    description: "Register new client",
    icon: "mdi-domain-plus",
    color: "success",
    action: "clients",
  },
  {
    id: 3,
    title: "View Reports",
    description: "Access analytics",
    icon: "mdi-chart-line",
    color: "info",
    action: "report",
  },
  {
    id: 4,
    title: "Manage Users",
    description: "User administration",
    icon: "mdi-account-cog",
    color: "accent",
    action: "add-user",
  },
]);

// Methods
const handleQuickAction = (action: string) => {
  switch (action) {
    case "add-user":
      router.push("/users");
      break;
    case "clients":
      router.push("/clients");
      break;
    case "calculate":
      router.push("/calculator");
      break;
    case "report":
      router.push("/reports");
      break;
  }
};

const refreshActivities = () => {
  // Simulate refresh
  chartsLoading.value = true;
  setTimeout(() => {
    chartsLoading.value = false;
  }, 1000);
};
</script>

<style scoped>
.dashboard-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* Section Titles */
.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
}

/* Enhanced Metric Cards */
.metric-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.metric-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--card-color, #1976d2),
    var(--card-color-light, #42a5f5)
  );
}

.metric-card--primary {
  --card-color: #1976d2;
  --card-color-light: #42a5f5;
}

.metric-card--success {
  --card-color: #4caf50;
  --card-color-light: #66bb6a;
}

.metric-card--info {
  --card-color: #2196f3;
  --card-color-light: #42a5f5;
}

.metric-card--warning {
  --card-color: #ff9800;
  --card-color-light: #ffb74d;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(
    135deg,
    var(--card-color),
    var(--card-color-light)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.metric-trend--up {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.metric-trend--down {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.metric-content {
  margin-bottom: 16px;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
}

.metric-label {
  font-size: 1rem;
  font-weight: 500;
  color: #64748b;
  margin-top: 4px;
}

.metric-sublabel {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 2px;
}

.metric-chart {
  height: 60px;
  opacity: 0.7;
}

/* Chart Cards */
.chart-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.chart-card-header {
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%
  );
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chart-controls {
  display: flex;
  gap: 8px;
}

/* Activity Card */
.activity-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.activity-card-header {
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%
  );
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-list {
  background: transparent;
}

.activity-item {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: rgba(25, 118, 210, 0.02);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-avatar {
  margin-right: 16px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.activity-description {
  color: #64748b;
  font-size: 0.875rem;
}

.activity-time {
  display: flex;
  align-items: center;
  text-align: right;
}

/* Quick Actions Card */
.quick-actions-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.quick-actions-header {
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%
  );
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;
}

.quick-action-item:hover {
  background: rgba(25, 118, 210, 0.05);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-action-content {
  flex: 1;
}

.quick-action-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 2px;
}

.quick-action-description {
  color: #64748b;
  font-size: 0.75rem;
}

.quick-action-arrow {
  color: #94a3b8;
  transition: all 0.2s ease;
}

.quick-action-item:hover .quick-action-arrow {
  color: #1976d2;
  transform: translateX(2px);
}

/* Responsive Design */
@media (max-width: 960px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .metric-card {
    padding: 20px;
  }

  .metric-value {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 600px) {
  .hero-title {
    font-size: 1.75rem;
  }

  .metric-card {
    padding: 16px;
  }

  .metric-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .activity-item {
    padding: 12px 16px;
  }

  .quick-action-item {
    padding: 12px;
  }
}
</style>
