<template>
    <v-card>
        <v-data-table
            :headers="headers"
            :items="clients"
            :loading="loading"
            :items-per-page="itemsPerPage"
            :items-length="totalItems"
            @update:page="$emit('update:currentPage', $event)"
            @update:options="$emit('update:options', $event)"
        />
    </v-card>
</template>

<script setup lang="ts">
import type { ClientEntity } from '@/types/client.types'

const props = defineProps<{
    clients: ClientEntity[]
    loading: boolean
    itemsPerPage: number
    totalItems: number
}>()

const headers = [
    { title: 'ID', key: 'id', width: '70px' },
    { title: 'First Name', key: 'firstName' },
    { title: 'Last Name', key: 'lastName' },
    { title: 'Email', key: 'email' },
    { title: 'Phone', key: 'mobilePhone' },
    { title: 'Account State', key: 'accountState' },
    { title: 'Verified', key: 'verified', width: '100px' },
    { title: 'County', key: 'county' },
    { title: 'Sub County', key: 'subCounty' },
    { title: 'Creation Date', key: 'creationDate' },
    { title: 'Actions', key: 'actions', sortable: false, width: '100px' }
]
</script> 