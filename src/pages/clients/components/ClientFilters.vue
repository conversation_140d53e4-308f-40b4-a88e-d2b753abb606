<template>
    <v-card class="mb-6" elevation="1">
        <v-card-text>
            <v-form @submit.prevent="handleFilter">
                <v-row>
                    <v-col cols="12" sm="6" md="3">
                        <v-text-field
                            v-model="filters.email"
                            label="Email"
                            placeholder="Search by email"
                            variant="outlined"
                            density="comfortable"
                            hide-details
                        />
                    </v-col>

                    <v-col cols="12" sm="6" md="3">
                        <v-text-field
                            v-model="filters.mobilePhone"
                            label="Phone Number"
                            placeholder="Search by phone"
                            variant="outlined"
                            density="comfortable"
                            hide-details
                        />
                    </v-col>

                    <v-col cols="12" sm="6" md="3">
                        <v-text-field
                            v-model="filters.county"
                            label="County"
                            placeholder="Search by county"
                            variant="outlined"
                            density="comfortable"
                            hide-details
                        />
                    </v-col>

                    <v-col cols="12" sm="6" md="3">
                        <v-text-field
                            v-model="filters.subCounty"
                            label="Sub County"
                            placeholder="Search by sub county"
                            variant="outlined"
                            density="comfortable"
                            hide-details
                        />
                    </v-col>

                    <v-col cols="12" sm="6" md="3">
                        <v-btn color="primary" class="mr-2" @click="handleFilter">
                            Filter
                        </v-btn>
                        <v-btn variant="outlined" @click="resetFilters">
                            Reset
                        </v-btn>
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
    </v-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ClientQueryDto } from '@/types/client.types'

const emit = defineEmits(['filter', 'reset'])

const filters = ref<Partial<ClientQueryDto>>({
    email: '',
    mobilePhone: '',
    county: '',
    subCounty: ''
})

const handleFilter = () => {
    emit('filter', filters.value)
}

const resetFilters = () => {
    filters.value = {  email: '', mobilePhone: '', county: '', subCounty: '' }
    emit('reset')
}
</script> 