<template>
    <v-dialog v-model="dialog" max-width="800px" persistent>
        <v-card>
            <v-card-title class="text-h5 pa-4">
                Create New Client
                <v-btn icon variant="text" position="absolute" right="8" @click="handleClose">
                    <v-icon>mdi-close</v-icon>
                </v-btn>
            </v-card-title>

            <v-card-text>
                <v-form ref="form" @submit.prevent="handleSubmit">
                    <v-container>
                        <v-row>
                            <v-col cols="12" md="4">
                                <v-text-field
                                    v-model="formData.firstName"
                                    label="First Name"
                                    :rules="[v => !!v || 'First name is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="4">
                                <v-text-field
                                    v-model="formData.middleName"
                                    label="Middle Name"
                                    variant="outlined"
                                />
                            </v-col>

                            <v-col cols="12" md="4">
                                <v-text-field
                                    v-model="formData.lastName"
                                    label="Last Name"
                                    :rules="[v => !!v || 'Last name is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.email"
                                    label="Email"
                                    type="email"
                                    :rules="[
                                        v => !!v || 'Email is required',
                                        v => /.+@.+\..+/.test(v) || 'Email must be valid'
                                    ]"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.mobilePhone"
                                    label="Phone Number"
                                    :rules="[
                                        v => !!v || 'Phone number is required',
                                        v => /^\+?[1-9]\d{1,14}$/.test(v) || 'Invalid phone number'
                                    ]"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.password"
                                    label="Password"
                                    type="password"
                                    :rules="[v => !!v || 'Password is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-select
                                    v-model="formData.maritalStatus"
                                    label="Marital Status"
                                    :items="['Single', 'Married', 'Divorced', 'Widowed']"
                                    :rules="[v => !!v || 'Marital status is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.residence"
                                    label="Residence"
                                    :rules="[v => !!v || 'Residence is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.county"
                                    label="County"
                                    :rules="[v => !!v || 'County is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.subCounty"
                                    label="Sub County"
                                    :rules="[v => !!v || 'Sub County is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.ward"
                                    label="Ward"
                                    variant="outlined"
                                />
                            </v-col>

                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="formData.birthDate"
                                    label="Birth Date"
                                    type="date"
                                    :rules="[v => !!v || 'Birth date is required']"
                                    variant="outlined"
                                    required
                                />
                            </v-col>

                            <v-col cols="12">
                                <v-checkbox
                                    v-model="formData.acceptTerms"
                                    label="I accept the terms and conditions"
                                    :rules="[v => !!v || 'You must accept the terms']"
                                    required
                                />
                            </v-col>
                        </v-row>
                    </v-container>
                </v-form>
            </v-card-text>

            <v-card-actions class="pa-4">
                <v-spacer />
                <v-btn
                    color="grey-darken-1"
                    variant="text"
                    :disabled="loading"
                    @click="handleClose"
                >
                    Cancel
                </v-btn>
                <v-btn
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    @click="handleSubmit"
                >
                    Create Client
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { CreateClientDto } from '@/types/client.types'
import { AccountState, EntityType } from '@/types/client.types'

const props = defineProps<{
    modelValue: boolean
    loading?: boolean
}>()

const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'submit': [data: CreateClientDto]
}>()

const dialog = ref(props.modelValue)
const form = ref<any>(null)

const formData = ref<CreateClientDto>({
    firstName: '',
    lastName: '',
    middleName: '',
    birthDate: '',
    maritalStatus: '',
    accountState: AccountState.PARTIAL_APPLICATION,
    acceptTerms: false,
    entityType: EntityType.USER,
    email: '',
    emailVerified: false,
    verified: false,
    userState: 'active',
    mobilePhone: '',
    residence: '',
    county: '',
    subCounty: '',
    ward: '',
    password: ''
})

watch(() => props.modelValue, (newVal) => {
    dialog.value = newVal
})

watch(dialog, (newVal) => {
    emit('update:modelValue', newVal)
})

const handleSubmit = async () => {
    const { valid } = await form.value.validate()
    
    if (valid) {
        emit('submit', formData.value)
    }
}

const handleClose = () => {
    dialog.value = false
    form.value?.reset()
    formData.value = {
        firstName: '',
        lastName: '',
        middleName: '',
        birthDate: '',
        maritalStatus: '',
        accountState: AccountState.PARTIAL_APPLICATION,
        acceptTerms: false,
        entityType: EntityType.USER,
        email: '',
        emailVerified: false,
        verified: false,
        userState: 'active',
        mobilePhone: '',
        residence: '',
        county: '',
        subCounty: '',
        ward: '',
        password: ''
    }
}
</script> 