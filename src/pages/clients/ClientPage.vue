<template>
    <v-container fluid>
        <ThePageHeader title="Clients Management" subtitle="Manage and monitor client accounts">
            <template #default>
                <v-spacer></v-spacer>
                <v-btn color="primary" prepend-icon="mdi-plus" @click="showCreateClientDialog = true">
                    Create Client
                </v-btn>
            </template>
        </ThePageHeader>

        <ClientFilters @filter="handleFilter" @reset="resetFilters" />

        <ClientsTable 
            :clients="clients"
            :loading="isLoading"
            :current-page="currentPage"
            :items-per-page="filters.limit!"
            :total-items="totalItems"
            @update:current-page="handlePageChange"
            @update:options="handleTableUpdate"
        />

        <CreateClientDialog 
            v-model="showCreateClientDialog" 
            :loading="isCreatingClient" 
            @submit="handleCreateClient" 
        />
    </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useClientStore } from '@/stores/client.store'
import ClientFilters from './components/ClientFilters.vue'
import ClientsTable from './components/ClientsTable.vue'
import CreateClientDialog from './components/CreateClientDialog.vue'
import type { CreateClientDto } from '@/types/client.types'

const clientStore = useClientStore()
const { clients, totalItems, currentPage, filters, isLoading } = storeToRefs(clientStore)
const showCreateClientDialog = ref(false)
const isCreatingClient = ref(false)

const handleFilter = (filterData) => {
    clientStore.setFilters(filterData)
    clientStore.fetchClients()
}

const resetFilters = () => {
    clientStore.setFilters({})
    clientStore.fetchClients()
}

const handleTableUpdate = (options: any) => {
    const { page, itemsPerPage } = options
    clientStore.setFilters({ page, limit: itemsPerPage })
    clientStore.fetchClients()
}

const handleCreateClient = async (clientData: CreateClientDto) => {
    try {
        isCreatingClient.value = true
        await clientStore.createClient(clientData)
        showCreateClientDialog.value = false
        clientStore.fetchClients()
    } catch (error) {
        console.error('Error creating client:', error)
    } finally {
        isCreatingClient.value = false
    }
}

const handlePageChange = (page: number) => {
    clientStore.setFilters({ ...filters.value, page })
    clientStore.fetchClients()
}
</script> 