import type { Rule, Condition, Calculation, SalesData, CommissionResult } from '../types';

// Rule Engine
/**
 * The baseField tells the calculation engine:

1. Which piece of sales data to use as the basis for the commission calculation
2. What amount to apply the percentage/formula to (for percentage and formula calculations)
3. What value to compare against tier ranges (for tiered calculations)
 */
export class RuleEngine {
  // Validate and fix invalid condition operator/field combinations
  validateAndFixCondition(condition: Condition): Condition {
    const stringFields = ['loanType', 'clientType', 'employerType'];
    const isStringField = stringFields.includes(condition.field);

    if (isStringField && condition.operator !== 'eq') {
      console.warn(`Fixed invalid operator '${condition.operator}' for string field '${condition.field}' to 'eq'`);
      return { ...condition, operator: 'eq' };
    }

    return condition;
  }

  evaluateConditions(conditions: Condition[], data: SalesData): boolean {
    if (conditions.length === 0) return true;

    return conditions.every(originalCondition => {
      // Validate and fix the condition first
      const condition = this.validateAndFixCondition(originalCondition);
      const fieldValue = data[condition.field as keyof SalesData];

      switch (condition.operator) {
        case 'eq': return fieldValue === condition.value;
        case 'gt': return Number(fieldValue) > condition.value;
        case 'gte': return Number(fieldValue) >= condition.value;
        case 'lt': return Number(fieldValue) < condition.value;
        case 'lte': return Number(fieldValue) <= condition.value;
        case 'in': return Array.isArray(condition.value) && condition.value.includes(fieldValue);
        case 'between':
          return Array.isArray(condition.value) &&
                 Number(fieldValue) >= condition.value[0] &&
                 Number(fieldValue) <= condition.value[1];
        default: return false;
      }
    });
  }
  
  calculateCommission(calculation: Calculation, data: SalesData): number {
    const baseValue = Number(data[calculation.baseField as keyof SalesData]) || 0;
    
    switch (calculation.type) {
      case 'percentage':
        return baseValue * ((calculation.value || 0) / 100);

      case 'fixed':
        return calculation.value || 0;

      case 'per-unit':
        return baseValue * (calculation.unitAmount || 0);

      case 'tiered':
        if (!calculation.tiers) return 0;

        // Use explicit comparisonField, fallback to baseField
        const comparisonField = calculation.comparisonField || calculation.baseField;
        const comparisonValue = Number(data[comparisonField as keyof SalesData]) || 0;

        const tier = calculation.tiers.find(t =>
          comparisonValue >= t.min && comparisonValue < t.max
        );

        if (!tier) return 0;

        // Use explicit tierValueType, fallback to smart detection for backward compatibility
        const isPercentage = calculation.tierValueType === 'percentage' ||
                            (calculation.tierValueType === undefined && tier.value < 10);

        return isPercentage ? baseValue * (tier.value / 100) : tier.value;
        
      case 'formula':
        return this.evaluateFormula(calculation.formula || '', data);
        
      default:
        return 0;
    }
  }
  
  private evaluateFormula(formula: string, data: SalesData): number {
    try {
      // Create a safe evaluation function
      const func = new Function('data', 'Math', `return ${formula}`);
      const result = func(data, Math);
      return Number(result) || 0;
    } catch (error) {
      console.error('Formula evaluation error:', error);
      return 0;
    }
  }
  
  calculateTotalCommission(rules: Rule[], data: SalesData): CommissionResult {
    const breakdown: CommissionResult['breakdown'] = [];
    let total = 0;
    
    // Sort rules by priority
    const sortedRules = [...rules].sort((a, b) => a.priority - b.priority);
    
    for (const rule of sortedRules) {
      if (!rule.enabled) continue;
      
      const conditionsMet = this.evaluateConditions(rule.conditions, data);
      if (!conditionsMet) continue;
      
      const amount = this.calculateCommission(rule.calculation, data);
      if (amount > 0) {
        breakdown.push({
          ruleName: rule.name,
          category: rule.category,
          amount,
          calculation: this.getCalculationDescription(rule.calculation, data, amount)
        });
        total += amount;
      }
    }
    
    return { total, breakdown };
  }
  
  private getCalculationDescription(calculation: Calculation, data: SalesData, result: number): string {
    const baseValue = Number(data[calculation.baseField as keyof SalesData]) || 0;
    
    switch (calculation.type) {
      case 'percentage':
        return `${baseValue.toLocaleString()} × ${calculation.value}% = ${result.toLocaleString()}`;
      case 'fixed':
        return `Fixed amount: ${result.toLocaleString()}`;
      case 'per-unit':
        return `${baseValue.toLocaleString()} units × ${(calculation.unitAmount || 0).toLocaleString()} = ${result.toLocaleString()}`;
      case 'tiered':
        return `Tier-based: ${result.toLocaleString()}`;
      case 'formula':
        return `Formula result: ${result.toLocaleString()}`;
      default:
        return `${result.toLocaleString()}`;
    }
  }
}
