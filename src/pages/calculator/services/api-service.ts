// API Service for Commission Calculator Backend Communication
import type { 
  Rule, 
  FieldConfig, 
  FormulaVariable, 
  DropdownOptions, 
  CalculationType, 
  FormulaExample,
  ApiError as ApiErrorType
} from '../types';

// Re-export ApiError class from types
export { ApiError } from '../types';

// API Configuration
const API_BASE_URL = 'http://localhost:3001';

// Generic API Request Function
async function apiRequest<T>(endpoint: string): Promise<T> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`);
    
    if (!response.ok) {
      const { ApiError } = await import('../types');
      throw new ApiError(
        `HTTP error! status: ${response.status}`,
        response.status,
        endpoint
      );
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    const { ApiError } = await import('../types');
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      undefined,
      endpoint
    );
  }
}

// API Service Class
export class ApiService {
  // Fetch all commission rules
  static async getRules(): Promise<Rule[]> {
    try {
      const rules = await apiRequest<Rule[]>('/rules');
      console.log('✅ Fetched rules from API:', rules.length, 'rules');
      return rules;
    } catch (error) {
      console.error('❌ Failed to fetch rules:', error);
      const { ApiError } = await import('../types');
      throw new ApiError('Failed to fetch commission rules');
    }
  }

  // Fetch field configurations
  static async getFieldConfig(): Promise<FieldConfig[]> {
    try {
      const fieldConfig = await apiRequest<FieldConfig[]>('/fieldConfig');
      console.log('✅ Fetched field config from API:', fieldConfig.length, 'fields');
      return fieldConfig;
    } catch (error) {
      console.error('❌ Failed to fetch field config:', error);
      const { ApiError } = await import('../types');
      throw new ApiError('Failed to fetch field configuration');
    }
  }

  // Fetch formula variables
  static async getFormulaVariables(): Promise<FormulaVariable[]> {
    try {
      const variables = await apiRequest<FormulaVariable[]>('/formulaVariables');
      console.log('✅ Fetched formula variables from API:', variables.length, 'variables');
      return variables;
    } catch (error) {
      console.error('❌ Failed to fetch formula variables:', error);
      const { ApiError } = await import('../types');
      throw new ApiError('Failed to fetch formula variables');
    }
  }

  // Fetch dropdown options
  static async getDropdownOptions(): Promise<DropdownOptions> {
    try {
      const options = await apiRequest<DropdownOptions>('/dropdownOptions');
      console.log('✅ Fetched dropdown options from API');
      return options;
    } catch (error) {
      console.error('❌ Failed to fetch dropdown options:', error);
      const { ApiError } = await import('../types');
      throw new ApiError('Failed to fetch dropdown options');
    }
  }

  // Fetch calculation types
  static async getCalculationTypes(): Promise<CalculationType[]> {
    try {
      const types = await apiRequest<CalculationType[]>('/calculationTypes');
      console.log('✅ Fetched calculation types from API:', types.length, 'types');
      return types;
    } catch (error) {
      console.error('❌ Failed to fetch calculation types:', error);
      const { ApiError } = await import('../types');
      throw new ApiError('Failed to fetch calculation types');
    }
  }

  // Fetch formula examples
  static async getFormulaExamples(): Promise<FormulaExample[]> {
    try {
      const examples = await apiRequest<FormulaExample[]>('/formulaExamples');
      console.log('✅ Fetched formula examples from API:', examples.length, 'examples');
      return examples;
    } catch (error) {
      console.error('❌ Failed to fetch formula examples:', error);
      const { ApiError } = await import('../types');
      throw new ApiError('Failed to fetch formula examples');
    }
  }

  // Fetch all configuration data at once
  static async getAllConfig(): Promise<{
    rules: Rule[];
    fieldConfig: FieldConfig[];
    formulaVariables: FormulaVariable[];
    dropdownOptions: DropdownOptions;
    calculationTypes: CalculationType[];
    formulaExamples: FormulaExample[];
  }> {
    try {
      console.log('🔄 Fetching all configuration data from API...');
      
      const [
        rules,
        fieldConfig,
        formulaVariables,
        dropdownOptions,
        calculationTypes,
        formulaExamples
      ] = await Promise.all([
        this.getRules(),
        this.getFieldConfig(),
        this.getFormulaVariables(),
        this.getDropdownOptions(),
        this.getCalculationTypes(),
        this.getFormulaExamples()
      ]);

      console.log('✅ Successfully fetched all configuration data');
      
      return {
        rules,
        fieldConfig,
        formulaVariables,
        dropdownOptions,
        calculationTypes,
        formulaExamples
      };
    } catch (error) {
      console.error('❌ Failed to fetch configuration data:', error);
      const { ApiError } = await import('../types');
      throw new ApiError('Failed to fetch application configuration');
    }
  }

  // Create a new rule
  static async createRule(rule: Omit<Rule, 'id'>): Promise<Rule> {
    try {
      // Generate a unique ID for the new rule
      const ruleWithId = {
        ...rule,
        id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };

      const response = await fetch(`${API_BASE_URL}/rules`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ruleWithId),
      });

      if (!response.ok) {
        const { ApiError } = await import('../types');
        throw new ApiError(`HTTP error! status: ${response.status}`, response.status, '/rules');
      }

      const newRule = await response.json();
      console.log('✅ Created new rule:', newRule.id);
      return newRule;
    } catch (error) {
      console.error('❌ Failed to create rule:', error);
      const { ApiError } = await import('../types');
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Failed to create new rule');
    }
  }

  // Update an existing rule
  static async updateRule(id: string, rule: Rule): Promise<Rule> {
    try {
      const response = await fetch(`${API_BASE_URL}/rules/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(rule),
      });

      if (!response.ok) {
        const { ApiError } = await import('../types');
        throw new ApiError(`HTTP error! status: ${response.status}`, response.status, `/rules/${id}`);
      }

      const updatedRule = await response.json();
      console.log('✅ Updated rule:', updatedRule.id);
      return updatedRule;
    } catch (error) {
      console.error('❌ Failed to update rule:', error);
      const { ApiError } = await import('../types');
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Failed to update rule');
    }
  }

  // Delete a rule
  static async deleteRule(id: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/rules/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const { ApiError } = await import('../types');
        throw new ApiError(`HTTP error! status: ${response.status}`, response.status, `/rules/${id}`);
      }

      console.log('✅ Deleted rule:', id);
    } catch (error) {
      console.error('❌ Failed to delete rule:', error);
      const { ApiError } = await import('../types');
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Failed to delete rule');
    }
  }

  // Health check
  static async healthCheck(): Promise<boolean> {
    try {
      await apiRequest('/rules');
      console.log('✅ API health check passed');
      return true;
    } catch (error) {
      console.error('❌ API health check failed:', error);
      return false;
    }
  }
}

export default ApiService;
