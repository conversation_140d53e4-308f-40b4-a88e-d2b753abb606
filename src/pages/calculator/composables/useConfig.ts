// Vue 3 Composable for Configuration Management
import { reactive, computed, readonly } from "vue";
import { ApiService } from "../services/api-service";
import type {
  Rule,
  FieldConfig,
  FormulaVariable,
  DropdownOptions,
  CalculationType,
  FormulaExample,
} from "../types";

// Configuration State Interface
export interface ConfigState {
  rules: Rule[];
  fieldConfig: FieldConfig[];
  formulaVariables: FormulaVariable[];
  dropdownOptions: DropdownOptions | null;
  calculationTypes: CalculationType[];
  formulaExamples: FormulaExample[];
  isLoading: boolean;
  error: string | null;
  isApiConnected: boolean;
}

// Default dropdown options (fallback)
const defaultDropdownOptions: DropdownOptions = {
  loanTypes: ["New", "Repeat", "Buyoff", "Top-up", "Partnership"],
  clientTypes: ["New", "Repeat"],
  employerTypes: ["Government", "Private", "NGO", "Self-Employed"],
  frequencies: ["daily", "weekly", "monthly"],
  categories: [
    "End Month Sales",
    "Weekly Sales",
    "Daily Sales",
    "Portfolio Commission",
    "Additional Commission",
  ],
  operators: {
    number: [
      { value: "eq", label: "Equals" },
      { value: "gt", label: "Greater than" },
      { value: "gte", label: "Greater than or equal" },
      { value: "lt", label: "Less than" },
      { value: "lte", label: "Less than or equal" },
    ],
    select: [{ value: "eq", label: "Equals" }],
  },
};

// Global reactive state
const state = reactive<ConfigState>({
  rules: [],
  fieldConfig: [],
  formulaVariables: [],
  dropdownOptions: defaultDropdownOptions,
  calculationTypes: [],
  formulaExamples: [],
  isLoading: true,
  error: null,
  isApiConnected: false,
});

// Load configuration from API
const loadConfig = async (): Promise<void> => {
  state.isLoading = true;
  state.error = null;

  try {
    // First check if API is available
    const isHealthy = await ApiService.healthCheck();

    if (!isHealthy) {
      throw new Error(
        "API server is not responding. Please ensure JSON Server is running on port 3001.",
      );
    }

    // Fetch all configuration data
    const config = await ApiService.getAllConfig();

    // Update state
    Object.assign(state, {
      ...config,
      isLoading: false,
      error: null,
      isApiConnected: true,
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Failed to load configuration";

    // Fallback to default rules if API fails
    Object.assign(state, {
      rules: [],
      fieldConfig: [],
      formulaVariables: [],
      dropdownOptions: defaultDropdownOptions,
      calculationTypes: [],
      formulaExamples: [],
      isLoading: false,
      error: errorMessage,
      isApiConnected: false,
    });

    console.error(
      "❌ Failed to load configuration, using defaults:",
      errorMessage,
    );
  }
};

// Configuration actions
const refreshConfig = async (): Promise<void> => {
  await loadConfig();
};

const updateRules = (rules: Rule[]): void => {
  state.rules = rules;
};

const addRule = async (rule: Omit<Rule, "id">): Promise<Rule> => {
  try {
    const newRule = await ApiService.createRule(rule);
    state.rules.push(newRule);
    console.log("✅ Rule added successfully:", newRule.id);
    return newRule;
  } catch (error) {
    console.error("❌ Failed to add rule:", error);
    throw error;
  }
};

const updateRule = async (rule: Rule): Promise<Rule> => {
  try {
    const updatedRule = await ApiService.updateRule(rule.id, rule);
    const index = state.rules.findIndex((r) => r.id === rule.id);
    if (index !== -1) {
      state.rules[index] = updatedRule;
    }
    console.log("✅ Rule updated successfully:", updatedRule.id);
    return updatedRule;
  } catch (error) {
    console.error("❌ Failed to update rule:", error);
    throw error;
  }
};

const removeRule = async (id: string): Promise<void> => {
  try {
    await ApiService.deleteRule(id);
    const index = state.rules.findIndex((r) => r.id === id);
    if (index !== -1) {
      state.rules.splice(index, 1);
    }
    console.log("✅ Rule deleted successfully:", id);
  } catch (error) {
    console.error("❌ Failed to delete rule:", error);
    throw error;
  }
};

const retryConnection = async (): Promise<void> => {
  await loadConfig();
};

// Main composable function
export function useConfig() {
  // Initialize configuration on first use
  if (state.isLoading && state.rules.length === 0) {
    loadConfig();
  }

  return {
    // Reactive state
    state: readonly(state),
    rules: computed(() => state.rules),
    fieldConfig: computed(() => state.fieldConfig),
    formulaVariables: computed(() => state.formulaVariables),
    dropdownOptions: computed(
      () => state.dropdownOptions || defaultDropdownOptions,
    ),
    calculationTypes: computed(() => state.calculationTypes),
    formulaExamples: computed(() => state.formulaExamples),
    isLoading: computed(() => state.isLoading),
    error: computed(() => state.error),
    isApiConnected: computed(() => state.isApiConnected),

    // Actions
    refreshConfig,
    updateRules,
    addRule,
    updateRule,
    removeRule,
    retryConnection,
  };
}
