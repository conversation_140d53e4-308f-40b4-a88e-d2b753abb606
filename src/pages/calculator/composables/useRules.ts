// Vue 3 Composable for Rules Management
import { computed } from 'vue'
import { useConfig } from './useConfig'
import type { Rule } from '../types'

/**
 * Specialized composable for rules management
 * Provides focused access to rules-related state and actions
 */
export function useRules() {
  const { 
    rules, 
    updateRules, 
    addRule, 
    updateRule, 
    removeRule,
    isLoading,
    error,
    isApiConnected 
  } = useConfig()

  // Helper function to find rule by ID
  const findRuleById = (id: string): Rule | undefined => {
    return rules.value.find(rule => rule.id === id)
  }

  // Helper function to get rules by category
  const getRulesByCategory = (category: string): Rule[] => {
    return rules.value.filter(rule => rule.category === category)
  }

  // Helper function to get enabled rules only
  const getEnabledRules = (): Rule[] => {
    return rules.value.filter(rule => rule.enabled)
  }

  // Helper function to get rules by frequency
  const getRulesByFrequency = (frequency: 'daily' | 'weekly' | 'monthly'): Rule[] => {
    return rules.value.filter(rule => rule.frequency === frequency)
  }

  // Computed properties for rule statistics
  const totalRules = computed(() => rules.value.length)
  const enabledRulesCount = computed(() => getEnabledRules().length)
  const disabledRulesCount = computed(() => rules.value.length - enabledRulesCount.value)

  // Get unique categories
  const categories = computed(() => {
    const uniqueCategories = new Set(rules.value.map(rule => rule.category))
    return Array.from(uniqueCategories).sort()
  })

  // Get unique frequencies
  const frequencies = computed(() => {
    const uniqueFrequencies = new Set(
      rules.value
        .map(rule => rule.frequency)
        .filter(freq => freq !== undefined)
    )
    return Array.from(uniqueFrequencies).sort()
  })

  // Helper function to toggle rule enabled state
  const toggleRuleEnabled = async (ruleId: string): Promise<void> => {
    const rule = findRuleById(ruleId)
    if (rule) {
      const updatedRule = { ...rule, enabled: !rule.enabled }
      await updateRule(updatedRule)
    }
  }

  // Helper function to duplicate a rule
  const duplicateRule = async (ruleId: string): Promise<Rule | null> => {
    const rule = findRuleById(ruleId)
    if (rule) {
      const duplicatedRule = {
        ...rule,
        name: `${rule.name} (Copy)`,
        priority: rules.value.length + 1
      }
      // Remove the id to create a new rule
      const { id, ...ruleWithoutId } = duplicatedRule
      return await addRule(ruleWithoutId)
    }
    return null
  }

  // Helper function to reorder rules
  const reorderRules = async (fromIndex: number, toIndex: number): Promise<void> => {
    const reorderedRules = [...rules.value]
    const [movedRule] = reorderedRules.splice(fromIndex, 1)
    reorderedRules.splice(toIndex, 0, movedRule)
    
    // Update priorities
    const updatedRules = reorderedRules.map((rule, index) => ({
      ...rule,
      priority: index + 1
    }))
    
    updateRules(updatedRules)
    
    // Update each rule in the API
    for (const rule of updatedRules) {
      try {
        await updateRule(rule)
      } catch (error) {
        console.error('Failed to update rule priority:', error)
      }
    }
  }

  return {
    // State
    rules,
    isLoading,
    error,
    isApiConnected,
    
    // Statistics
    totalRules,
    enabledRulesCount,
    disabledRulesCount,
    categories,
    frequencies,
    
    // Actions
    updateRules,
    addRule,
    updateRule,
    removeRule,
    
    // Helper functions
    findRuleById,
    getRulesByCategory,
    getEnabledRules,
    getRulesByFrequency,
    toggleRuleEnabled,
    duplicateRule,
    reorderRules,
  }
}
