// Vue 3 Composable for Field Configuration Management
import { computed } from 'vue'
import { useConfig } from './useConfig'
import type { FieldConfig } from '../types'

/**
 * Specialized composable for field configuration
 * Provides access to field metadata and validation rules
 */
export function useFieldConfig() {
  const { fieldConfig } = useConfig()

  // Helper function to get field configuration by field name
  const getFieldConfig = (fieldName: string): FieldConfig | undefined => {
    return fieldConfig.value.find(config => config.field === fieldName)
  }

  // Helper function to get field type
  const getFieldType = (fieldName: string): 'number' | 'select' | undefined => {
    const config = getFieldConfig(fieldName)
    return config?.type
  }

  // Helper function to get field label
  const getFieldLabel = (fieldName: string): string => {
    const config = getFieldConfig(fieldName)
    return config?.label || fieldName
  }

  // Helper function to get field description
  const getFieldDescription = (fieldName: string): string => {
    const config = getFieldConfig(fieldName)
    return config?.description || ''
  }

  // Helper function to check if field is required
  const isFieldRequired = (fieldName: string): boolean => {
    const config = getFieldConfig(fieldName)
    return config?.required || false
  }

  // Helper function to get field placeholder
  const getFieldPlaceholder = (fieldName: string): string => {
    const config = getFieldConfig(fieldName)
    return config?.placeholder || ''
  }

  // Helper function to get field options (for select fields)
  const getFieldOptions = (fieldName: string): string[] => {
    const config = getFieldConfig(fieldName)
    return config?.options || []
  }

  // Helper function to get field validation rules
  const getFieldValidation = (fieldName: string) => {
    const config = getFieldConfig(fieldName)
    return config?.validation || {}
  }

  // Helper function to validate field value
  const validateFieldValue = (fieldName: string, value: any): { isValid: boolean; error?: string } => {
    const config = getFieldConfig(fieldName)
    if (!config) {
      return { isValid: true }
    }

    // Check required
    if (config.required && (value === null || value === undefined || value === '')) {
      return { isValid: false, error: `${config.label} is required` }
    }

    // Check validation rules for number fields
    if (config.type === 'number' && config.validation) {
      const numValue = Number(value)
      
      if (isNaN(numValue)) {
        return { isValid: false, error: `${config.label} must be a valid number` }
      }

      if (config.validation.min !== undefined && numValue < config.validation.min) {
        return { isValid: false, error: `${config.label} must be at least ${config.validation.min}` }
      }

      if (config.validation.max !== undefined && numValue > config.validation.max) {
        return { isValid: false, error: `${config.label} must be at most ${config.validation.max}` }
      }
    }

    // Check options for select fields
    if (config.type === 'select' && config.options) {
      if (!config.options.includes(value)) {
        return { isValid: false, error: `${config.label} must be one of: ${config.options.join(', ')}` }
      }
    }

    return { isValid: true }
  }

  // Get all field names
  const fieldNames = computed(() => fieldConfig.value.map(config => config.field))

  // Get fields by type
  const numberFields = computed(() => 
    fieldConfig.value.filter(config => config.type === 'number')
  )

  const selectFields = computed(() => 
    fieldConfig.value.filter(config => config.type === 'select')
  )

  // Get required fields
  const requiredFields = computed(() => 
    fieldConfig.value.filter(config => config.required)
  )

  return {
    // State
    fieldConfig,
    
    // Computed lists
    fieldNames,
    numberFields,
    selectFields,
    requiredFields,
    
    // Helper functions
    getFieldConfig,
    getFieldType,
    getFieldLabel,
    getFieldDescription,
    isFieldRequired,
    getFieldPlaceholder,
    getFieldOptions,
    getFieldValidation,
    validateFieldValue,
  }
}
