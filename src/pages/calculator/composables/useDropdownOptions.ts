// Vue 3 Composable for Dropdown Options Management
import { computed } from 'vue'
import { useConfig } from './useConfig'
import type { DropdownOptions } from '../types'

/**
 * Specialized composable for dropdown options
 * Provides easy access to all dropdown data used in forms
 */
export function useDropdownOptions() {
  const { dropdownOptions } = useConfig()

  // Individual dropdown options
  const loanTypes = computed(() => dropdownOptions.value?.loanTypes || [])
  const clientTypes = computed(() => dropdownOptions.value?.clientTypes || [])
  const employerTypes = computed(() => dropdownOptions.value?.employerTypes || [])
  const frequencies = computed(() => dropdownOptions.value?.frequencies || [])
  const categories = computed(() => dropdownOptions.value?.categories || [])
  
  // Operator options
  const numberOperators = computed(() => dropdownOptions.value?.operators.number || [])
  const selectOperators = computed(() => dropdownOptions.value?.operators.select || [])

  // Helper function to get operators by field type
  const getOperatorsByFieldType = (fieldType: 'number' | 'select') => {
    return fieldType === 'number' ? numberOperators.value : selectOperators.value
  }

  // Helper function to get label for operator value
  const getOperatorLabel = (operatorValue: string, fieldType: 'number' | 'select' = 'number') => {
    const operators = getOperatorsByFieldType(fieldType)
    const operator = operators.find(op => op.value === operatorValue)
    return operator?.label || operatorValue
  }

  // Helper function to check if option exists in a list
  const hasOption = (list: string[], value: string): boolean => {
    return list.includes(value)
  }

  // Validation helpers
  const isValidLoanType = (value: string) => hasOption(loanTypes.value, value)
  const isValidClientType = (value: string) => hasOption(clientTypes.value, value)
  const isValidEmployerType = (value: string) => hasOption(employerTypes.value, value)
  const isValidFrequency = (value: string) => hasOption(frequencies.value, value)
  const isValidCategory = (value: string) => hasOption(categories.value, value)

  return {
    // All options
    dropdownOptions,
    
    // Individual option lists
    loanTypes,
    clientTypes,
    employerTypes,
    frequencies,
    categories,
    numberOperators,
    selectOperators,
    
    // Helper functions
    getOperatorsByFieldType,
    getOperatorLabel,
    hasOption,
    
    // Validation helpers
    isValidLoanType,
    isValidClientType,
    isValidEmployerType,
    isValidFrequency,
    isValidCategory,
  }
}
