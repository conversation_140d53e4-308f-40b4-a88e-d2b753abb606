// Vue 3 Composable for Configuration Status Management
import { computed } from 'vue'
import { useConfig } from './useConfig'

/**
 * Specialized composable for configuration loading and error states
 * Provides focused access to status-related state and actions
 */
export function useConfigStatus() {
  const { 
    isLoading, 
    error, 
    isApiConnected, 
    retryConnection,
    refreshConfig 
  } = useConfig()

  // Computed properties for different status states
  const isConnected = computed(() => isApiConnected.value)
  const isDisconnected = computed(() => !isApiConnected.value)
  const hasError = computed(() => error.value !== null)
  const isReady = computed(() => !isLoading.value && !hasError.value)
  const isInitializing = computed(() => isLoading.value && !isApiConnected.value)

  // Status message for UI display
  const statusMessage = computed(() => {
    if (isLoading.value) {
      return 'Loading configuration...'
    }
    if (hasError.value) {
      return error.value || 'Unknown error occurred'
    }
    if (isConnected.value) {
      return 'Connected to API server'
    }
    return 'Using offline mode'
  })

  // Status type for styling
  const statusType = computed(() => {
    if (isLoading.value) return 'loading'
    if (hasError.value) return 'error'
    if (isConnected.value) return 'success'
    return 'warning'
  })

  // Connection status icon
  const statusIcon = computed(() => {
    if (isLoading.value) return 'refresh-cw'
    if (hasError.value) return 'alert-circle'
    if (isConnected.value) return 'wifi'
    return 'wifi-off'
  })

  return {
    // State
    isLoading,
    error,
    isApiConnected,
    
    // Computed status
    isConnected,
    isDisconnected,
    hasError,
    isReady,
    isInitializing,
    statusMessage,
    statusType,
    statusIcon,
    
    // Actions
    retryConnection,
    refreshConfig,
  }
}
