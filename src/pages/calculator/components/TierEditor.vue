<template>
  <v-card class="tier-editor-card" variant="outlined">
    <v-card-title class="d-flex align-center pa-4">
      <v-icon size="20" color="primary" class="mr-2">mdi-view-list</v-icon>
      <h5 class="text-h6 font-weight-medium">
        Tiers (based on {{ getFieldLabel(comparisonField || "netSales") }})
      </h5>
    </v-card-title>

    <v-divider />

    <v-card-text class="pa-4">
      <!-- Tier Items -->
      <div class="tier-items">
        <v-card
          v-for="(tier, index) in tiers"
          :key="index"
          class="tier-item mb-3"
          variant="outlined"
          elevation="1"
        >
          <v-card-text class="pa-4">
            <v-row align="center">
              <!-- Min Value -->
              <v-col cols="12" sm="3">
                <v-text-field
                  :model-value="tier.min"
                  @update:model-value="
                    (value) => updateTier(index, 'min', parseFloat(value) || 0)
                  "
                  label="Min"
                  type="number"
                  variant="outlined"
                  density="compact"
                  placeholder="0"
                  class="tier-input"
                />
              </v-col>

              <!-- Max Value -->
              <v-col cols="12" sm="3">
                <v-text-field
                  :model-value="tier.max === Infinity ? 999999999 : tier.max"
                  @update:model-value="
                    (value) => handleMaxValueChange(index, value)
                  "
                  label="Max"
                  type="number"
                  variant="outlined"
                  density="compact"
                  placeholder="999999999"
                  class="tier-input"
                />
              </v-col>

              <!-- Tier Value -->
              <v-col cols="12" sm="4">
                <v-text-field
                  :model-value="tier.value"
                  @update:model-value="
                    (value) =>
                      updateTier(index, 'value', parseFloat(value) || 0)
                  "
                  :label="valueLabel"
                  type="number"
                  :step="tierValueType === 'fixed' ? '1' : '0.01'"
                  variant="outlined"
                  density="compact"
                  :placeholder="valuePlaceholder"
                  class="tier-input"
                />
              </v-col>

              <!-- Remove Button -->
              <v-col cols="12" sm="2" class="d-flex justify-center">
                <v-btn
                  @click="removeTier(index)"
                  color="error"
                  variant="tonal"
                  size="small"
                  icon="mdi-delete"
                />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </div>

      <!-- Add Tier Button -->
      <div class="d-flex justify-center mt-4">
        <v-btn
          @click="addTier"
          color="success"
          variant="elevated"
          prepend-icon="mdi-plus"
          class="add-tier-btn"
        >
          Add Tier
        </v-btn>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import type {
  TierEditorProps,
  TierEditorEmits,
} from "@/pages/calculator/types/index";

// Props
const props = defineProps<TierEditorProps>();

// Emits
const emit = defineEmits<TierEditorEmits>();

// Helper functions
const getFieldLabel = (field: string): string => {
  const labels: Record<string, string> = {
    netSales: "Sales Amount",
    loanTerm: "Months",
    portfolio: "Portfolio Size",
    numberOfLoans: "Number of Loans",
    par: "PAR %",
    dailyNetSales: "Daily Sales",
    weeklyNetSales: "Weekly Sales",
    monthlyNetSales: "Monthly Sales",
  };
  return labels[field] || "Value";
};

// Computed properties
const valueLabel = computed((): string => {
  return props.tierValueType === "fixed" ? "Amount (KES)" : "Rate (%)";
});

const valuePlaceholder = computed((): string => {
  return props.tierValueType === "fixed" ? "5000" : "2.5";
});

// Tier management functions
const addTier = (): void => {
  const lastTier = props.tiers[props.tiers.length - 1];
  const newMin = lastTier ? lastTier.max : 0;
  const newTiers = [
    ...props.tiers,
    { min: newMin, max: newMin + 100000, value: 0 },
  ];
  emit("update:tiers", newTiers);
};

const updateTier = (index: number, field: string, value: number): void => {
  const newTiers = [...props.tiers];
  newTiers[index] = { ...newTiers[index], [field]: value };
  emit("update:tiers", newTiers);
};

const removeTier = (index: number): void => {
  const newTiers = props.tiers.filter((_: any, i: number) => i !== index);
  emit("update:tiers", newTiers);
};

const handleMaxValueChange = (index: number, value: string): void => {
  const numValue = parseFloat(value) || 0;
  const maxValue = numValue === 999999999 ? Infinity : numValue;
  updateTier(index, "max", maxValue);
};
</script>

<style scoped>
.tier-editor-card {
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tier-editor-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.tier-item {
  border-radius: 12px;
  transition: all 0.2s ease;
  border: 1px solid rgba(var(--v-theme-outline), 0.2);
}

.tier-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  border-color: rgba(var(--v-theme-primary), 0.3);
}

:deep(.tier-input .v-field) {
  border-radius: 8px;
  font-size: 0.95rem;
}

:deep(.tier-input .v-field--focused) {
  box-shadow: 0 0 0 2px rgba(var(--v-theme-primary), 0.2);
}

:deep(.tier-input .v-field__input) {
  font-family: "Roboto Mono", monospace;
  text-align: right;
  font-size: 0.95rem;
}

.add-tier-btn {
  border-radius: 12px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(var(--v-theme-success), 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.add-tier-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--v-theme-success), 0.4);
}

/* Consistent typography */
:deep(.v-card-title) {
  font-size: 1.1rem;
  font-weight: 600;
}

/* Animation for tier items */
.tier-items {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  :deep(.tier-input .v-field__input) {
    text-align: left;
  }
}
</style>
