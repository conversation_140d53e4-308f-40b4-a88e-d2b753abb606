<template>
  <v-card elevation="2" class="rules-management-card mt-6">
    <!-- Header -->
    <v-card-title class="d-flex align-center justify-space-between pa-6">
      <div class="d-flex align-center">
        <v-icon size="32" color="primary" class="mr-3">mdi-cog-outline</v-icon>
        <div>
          <h2 class="text-h5 font-weight-bold">Commission Rules</h2>
          <p class="text-body-2 text-medium-emphasis">
            {{ activeRulesCount }} of {{ rules.length }} rules active
          </p>
        </div>
      </div>
      <v-btn
        @click="handleAddRule"
        color="primary"
        variant="elevated"
        prepend-icon="mdi-plus"
        class="add-rule-btn"
        size="large"
      >
        Add New Rule
      </v-btn>
    </v-card-title>

    <v-divider />

    <v-card-text class="pa-6">
      <!-- Rules List -->
      <div v-if="rules.length > 0" class="rules-list">
        <RuleEditor
          v-for="(rule, index) in rules"
          :key="rule.id"
          :rule="rule"
          @update:rule="(updatedRule) => handleUpdateRule(index, updatedRule)"
          @remove="() => handleRemoveRule(index)"
          class="mb-4"
        />
      </div>

      <!-- Empty State -->
      <v-card v-else variant="outlined" class="empty-state-card">
        <v-card-text class="text-center py-12">
          <v-icon size="80" color="grey-lighten-2" class="mb-6"
            >mdi-cog-outline</v-icon
          >
          <h3 class="text-h6 font-weight-medium mb-3">No Rules Configured</h3>
          <p class="text-body-1 text-medium-emphasis mb-6">
            Create your first commission rule to start calculating commissions
          </p>
          <v-btn
            @click="handleAddRule"
            color="primary"
            variant="elevated"
            prepend-icon="mdi-plus"
            size="large"
          >
            Create First Rule
          </v-btn>
        </v-card-text>
      </v-card>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import RuleEditor from "./RuleEditor.vue";
import type {
  RulesManagementProps,
  RulesManagementEmits,
  Rule,
} from "@/pages/calculator/types/index";

// Props
const props = defineProps<RulesManagementProps>();

// Emits
const emit = defineEmits<RulesManagementEmits>();

// Computed properties
const activeRulesCount = computed((): number => {
  return props.rules.filter((rule) => rule.enabled).length;
});

// Event handlers
const handleAddRule = (): void => {
  emit("addRule");
};

const handleUpdateRule = (index: number, updatedRule: Rule): void => {
  emit("updateRule", index, updatedRule);
};

const handleRemoveRule = (index: number): void => {
  emit("removeRule", index);
};
</script>

<style scoped>
.rules-management-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rules-management-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.add-rule-btn {
  border-radius: 12px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(var(--v-theme-primary), 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.add-rule-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--v-theme-primary), 0.4);
}

.rules-list {
  animation: fadeInUp 0.5s ease-out;
}

.empty-state-card {
  border-radius: 12px;
  border: 2px dashed rgba(var(--v-theme-outline), 0.3);
  background: rgba(var(--v-theme-surface-variant), 0.1);
}

/* Animation for rules list */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 960px) {
  :deep(.v-card-title) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .add-rule-btn {
    width: 100%;
  }
}
</style>
