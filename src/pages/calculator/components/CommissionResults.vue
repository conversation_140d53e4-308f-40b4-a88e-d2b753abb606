<template>
  <v-card elevation="2" class="commission-results-card">
    <v-card-title class="d-flex align-center">
      <v-icon size="28" color="primary" class="mr-3">mdi-trending-up</v-icon>
      <div>
        <h2 class="text-h5 font-weight-bold">Commission Calculation</h2>
        <p class="text-body-2 text-medium-emphasis">
          Real-time commission breakdown
        </p>
      </div>
    </v-card-title>

    <v-card-text class="pa-6">
      <!-- Total Commission -->
      <v-card
        class="total-commission-card mb-6"
        color="primary"
        variant="elevated"
      >
        <v-card-text class="pa-6">
          <div class="d-flex align-center justify-space-between">
            <div>
              <p class="text-body-2 text-primary-lighten-2 mb-1">
                Total Commission
              </p>
              <p class="text-h3 font-weight-bold text-white">
                {{ formattedTotal }}
              </p>
            </div>
            <v-icon size="48" color="primary-lighten-2"
              >mdi-currency-usd</v-icon
            >
          </div>
        </v-card-text>
      </v-card>

      <!-- Commission Breakdown -->
      <div>
        <h3 class="text-h6 font-weight-bold text-grey-darken-2 mb-4">
          Commission Breakdown
        </h3>

        <!-- Breakdown Items -->
        <div v-if="hasBreakdownItems" class="breakdown-items">
          <v-card
            v-for="(item, index) in result.breakdown"
            :key="index"
            class="breakdown-item mb-3"
            variant="outlined"
          >
            <v-card-text class="pa-4">
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="d-flex align-center">
                  <v-icon size="20" color="grey-darken-1" class="mr-2"
                    >mdi-calendar-outline</v-icon
                  >
                  <span class="text-body-1 font-weight-medium">{{
                    item.ruleName
                  }}</span>
                </div>
                <v-chip
                  color="success"
                  variant="elevated"
                  class="font-weight-bold"
                >
                  {{ formatAmount(item.amount) }}
                </v-chip>
              </div>
              <div class="text-body-2 text-medium-emphasis mb-1">
                <span class="font-weight-medium">Category:</span>
                {{ item.category }}
              </div>
              <div class="text-body-2 text-medium-emphasis">
                <span class="font-weight-medium">Calculation:</span>
                {{ item.calculation }}
              </div>
            </v-card-text>
          </v-card>
        </div>

        <!-- No Results State -->
        <v-card v-else class="text-center pa-8" variant="outlined">
          <v-icon size="64" color="grey-lighten-2" class="mb-4"
            >mdi-trending-up</v-icon
          >
          <p class="text-body-1 text-medium-emphasis mb-2">
            No commission breakdown available
          </p>
          <p class="text-body-2 text-disabled">
            Configure rules to see detailed calculations
          </p>
        </v-card>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import type { CommissionResultsProps } from "@/pages/calculator/types/index";

// Props
const props = defineProps<CommissionResultsProps>();

// Computed properties
const formattedTotal = computed((): string => {
  return `KES ${props.result.total.toLocaleString()}`;
});

const hasBreakdownItems = computed((): boolean => {
  return props.result.breakdown.length > 0;
});

// Helper function to format amounts
const formatAmount = (amount: number): string => {
  return `KES ${amount.toLocaleString()}`;
};
</script>

<style scoped>
.commission-results-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.commission-results-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.total-commission-card {
  border-radius: 16px;
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 1) 0%,
    rgba(var(--v-theme-primary-darken-1), 1) 100%
  );
}

.breakdown-item {
  border-radius: 12px;
  transition: all 0.2s ease;
}

.breakdown-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .d-flex.justify-space-between {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
