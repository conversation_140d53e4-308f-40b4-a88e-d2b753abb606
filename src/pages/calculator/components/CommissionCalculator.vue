<template>
  <v-container fluid class="calculator-page">
    <div class="calculator-container">
      <Header @export="exportConfiguration" @import="importConfiguration" />

      <TabNavigation
        :active-tab="activeTab"
        @update:active-tab="setActiveTab"
      />

      <!-- Calculator Tab -->
      <v-row v-if="activeTab === 'calculator'" class="mt-6">
        <v-col cols="12" lg="6">
          <SalesDataForm
            :sales-data="salesData"
            @update:sales-data="setSalesData"
          />
        </v-col>
        <v-col cols="12" lg="6">
          <CommissionResults :result="result" />
        </v-col>
      </v-row>

      <!-- Rules Management Tab -->
      <RulesManagement
        v-if="activeTab === 'rules'"
        :rules="rules"
        @update-rule="updateRule"
        @remove-rule="removeRule"
        @add-rule="addNewRule"
      />

      <!-- API Rules Management Tab -->
      <v-card v-if="activeTab === 'api-rules'" class="mt-6" elevation="2">
        <v-card-title class="d-flex align-center">
          <v-icon size="32" color="primary" class="mr-3">mdi-api</v-icon>
          <div>
            <h2 class="text-h5 font-weight-bold">API Rule Manager</h2>
            <p class="text-body-2 text-medium-emphasis">
              Manage commission rules with live API integration
            </p>
          </div>
        </v-card-title>
        <v-card-text class="text-center py-12">
          <v-icon size="64" color="grey-lighten-2" class="mb-4"
            >mdi-cog-outline</v-icon
          >
          <p class="text-body-1 text-medium-emphasis mb-2">
            API Rule Manager component will be loaded here.
          </p>
          <p class="text-body-2 text-disabled">
            This tab demonstrates live API integration for rule management.
          </p>
        </v-card-text>
      </v-card>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { RuleEngine } from "../services/rule-engine";
import { useRules } from "@/pages/calculator/composables/index";
import Header from "./Header.vue";
import TabNavigation from "./TabNavigation.vue";
import SalesDataForm from "./SalesDataForm.vue";
import CommissionResults from "./CommissionResults.vue";
import RulesManagement from "./RulesManagement.vue";
import type {
  Rule,
  SalesData,
  CommissionResult,
  TabType,
} from "@/pages/calculator/types/index";

// Use composables
const {
  rules,
  updateRules: setRules,
  addRule: addRuleAPI,
  updateRule: updateRuleAPI,
  removeRule: removeRuleAPI,
} = useRules();

// Reactive state
const salesData = reactive<SalesData>({
  netSales: 250000,
  loanTerm: 24,
  loanType: "New",
  clientType: "New",
  portfolio: 150000,
  par: 3.5,
  parDays: 30,
  weeklyTarget: 100000,
  dailyNetSales: 35000,
  weeklyNetSales: 180000,
  monthlyNetSales: 750000,
  numberOfLoans: 5,
  numberOfNewLoans: 3,
  numberOfRepeatLoans: 2,
  employerType: "Government",
});

const activeTab = ref<TabType>("calculator");
const result = ref<CommissionResult>({ total: 0, breakdown: [] });

// Rule engine instance
const ruleEngine = new RuleEngine();

// Migration function to add frequency to rules that don't have it
const migrateRulesWithFrequency = (rules: Rule[]): Rule[] => {
  return rules.map((rule) => {
    if (!rule.frequency) {
      // Determine frequency based on category or rule name
      let frequency: "daily" | "weekly" | "monthly" = "monthly"; // default

      if (
        rule.category === "Daily Sales" ||
        rule.name.toLowerCase().includes("daily")
      ) {
        frequency = "daily";
      } else if (
        rule.category === "Weekly Sales" ||
        rule.name.toLowerCase().includes("weekly")
      ) {
        frequency = "weekly";
      } else if (
        rule.category === "End Month Sales" ||
        rule.category === "Portfolio Commission" ||
        rule.name.toLowerCase().includes("monthly")
      ) {
        frequency = "monthly";
      }

      return { ...rule, frequency };
    }
    return rule;
  });
};

// Calculate commission when rules or sales data change
const calculateCommission = (): void => {
  // Migrate rules to ensure they all have frequency
  const migratedRules = migrateRulesWithFrequency(rules.value);
  if (JSON.stringify(migratedRules) !== JSON.stringify(rules.value)) {
    setRules(migratedRules);
    return; // Don't calculate yet, wait for next render with migrated rules
  }

  const calculatedResult = ruleEngine.calculateTotalCommission(
    rules.value,
    salesData,
  );
  result.value = calculatedResult;
};

// Watch for changes in rules and sales data
watch([rules, salesData], calculateCommission, { deep: true });

// Initialize calculation on mount
onMounted(() => {
  calculateCommission();
});

// Event handlers
const setActiveTab = (tab: TabType): void => {
  activeTab.value = tab;
};

const setSalesData = (newSalesData: SalesData): void => {
  Object.assign(salesData, newSalesData);
};

const addNewRule = async (): Promise<void> => {
  const newRule = {
    name: "New Rule",
    description: "Enter description",
    enabled: true,
    category: "End Month Sales",
    frequency: "monthly" as const,
    conditions: [],
    calculation: {
      type: "percentage" as const,
      baseField: "netSales",
      value: 1,
    },
    priority: rules.value.length + 1,
  };

  try {
    await addRuleAPI(newRule);
  } catch (error) {
    console.error("Failed to add new rule:", error);
    // Could show user notification here
  }
};

const updateRule = async (_index: number, updatedRule: Rule): Promise<void> => {
  try {
    await updateRuleAPI(updatedRule);
  } catch (error) {
    console.error("Failed to update rule:", error);
    // Could show user notification here
  }
};

const removeRule = async (index: number): Promise<void> => {
  const ruleToRemove = rules.value[index];
  if (ruleToRemove) {
    try {
      await removeRuleAPI(ruleToRemove.id);
    } catch (error) {
      console.error("Failed to remove rule:", error);
      // Could show user notification here
    }
  }
};

const exportConfiguration = (): void => {
  const config = { rules: rules.value, salesData };
  const blob = new Blob([JSON.stringify(config, null, 2)], {
    type: "application/json",
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "commission-config.json";
  a.click();
  URL.revokeObjectURL(url);
};

const importConfiguration = (event: Event): void => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target?.result as string);
      if (config.rules && Array.isArray(config.rules)) {
        setRules(config.rules);
      }
      if (config.salesData) {
        setSalesData(config.salesData);
      }
    } catch (error) {
      alert("Invalid configuration file");
    }
  };
  reader.readAsText(file);
};
</script>

<style scoped>
.calculator-page {
  background: linear-gradient(
    135deg,
    rgba(248, 250, 252, 1) 0%,
    rgba(241, 245, 249, 1) 100%
  );
  min-height: calc(100vh - 64px);
  padding: 24px;
}

.calculator-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* Modern card styling */
:deep(.v-card) {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.v-card:hover) {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .calculator-page {
    padding: 16px;
  }
}
</style>
