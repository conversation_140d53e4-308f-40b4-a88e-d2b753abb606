<template>
  <v-card
    :class="[
      'rule-editor-card',
      { 'rule-expanded': isExpanded, 'rule-disabled': !localRule.enabled },
    ]"
    :variant="localRule.enabled ? 'elevated' : 'outlined'"
    :elevation="localRule.enabled ? 2 : 0"
  >
    <!-- Rule Header -->
    <v-card-title class="pa-4">
      <div class="d-flex align-center justify-space-between w-100">
        <div class="d-flex align-center">
          <v-switch
            v-model="localRule.enabled"
            @change="handleEnabledChange"
            color="primary"
            hide-details
            class="mr-4"
          />
          <div class="rule-info">
            <h3 class="text-h6 font-weight-semibold mb-1">
              {{ localRule.name }}
            </h3>
            <p class="text-body-2 text-medium-emphasis mb-2">
              {{ localRule.description }}
            </p>
            <div class="d-flex align-center ga-2 flex-wrap">
              <v-chip
                size="small"
                color="primary"
                variant="tonal"
                prepend-icon="mdi-tag"
              >
                {{ localRule.category }}
              </v-chip>
              <v-chip
                size="small"
                color="success"
                variant="tonal"
                prepend-icon="mdi-clock-outline"
              >
                {{ localRule.frequency || "monthly" }}
              </v-chip>
              <v-chip
                v-if="hasChanges"
                size="small"
                color="warning"
                variant="tonal"
                prepend-icon="mdi-pencil"
              >
                Modified
              </v-chip>
            </div>
          </div>
        </div>

        <div class="d-flex align-center ga-2">
          <!-- Update/Discard buttons (only show when there are changes) -->
          <template v-if="hasChanges">
            <v-btn
              @click="saveChanges"
              color="success"
              variant="tonal"
              size="small"
              prepend-icon="mdi-check"
            >
              Update
            </v-btn>
            <v-btn
              @click="discardChanges"
              color="grey"
              variant="tonal"
              size="small"
              prepend-icon="mdi-close"
            >
              Discard
            </v-btn>
          </template>

          <v-btn
            @click="toggleExpanded"
            :color="isExpanded ? 'primary' : 'grey'"
            variant="tonal"
            size="small"
            :prepend-icon="isExpanded ? 'mdi-chevron-up' : 'mdi-pencil'"
          >
            {{ isExpanded ? "Collapse" : "Edit" }}
          </v-btn>
          <v-btn
            @click="handleRemove"
            color="error"
            variant="tonal"
            size="small"
            prepend-icon="mdi-delete"
          >
            Remove
          </v-btn>
        </div>
      </div>
    </v-card-title>

    <!-- Expanded Edit Form -->
    <v-expand-transition>
      <div v-if="isExpanded">
        <v-divider />
        <v-card-text class="pa-6">
          <v-form class="edit-form">
            <!-- Basic Rule Information -->
            <v-row class="mb-4">
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="localRule.name"
                  label="Rule Name"
                  variant="outlined"
                  prepend-inner-icon="mdi-tag-outline"
                  class="rule-input"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="localRule.category"
                  label="Category"
                  :items="categoryOptions"
                  variant="outlined"
                  prepend-inner-icon="mdi-folder-outline"
                  class="rule-input"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="localRule.frequency"
                  label="Frequency"
                  :items="frequencyOptions"
                  variant="outlined"
                  prepend-inner-icon="mdi-clock-outline"
                  class="rule-input"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model.number="localRule.priority"
                  label="Priority"
                  type="number"
                  variant="outlined"
                  prepend-inner-icon="mdi-sort-numeric-ascending"
                  class="rule-input"
                />
              </v-col>
            </v-row>

            <!-- Description -->
            <v-row class="mb-4">
              <v-col cols="12">
                <v-textarea
                  v-model="localRule.description"
                  label="Description"
                  variant="outlined"
                  prepend-inner-icon="mdi-text"
                  rows="3"
                  class="rule-input"
                />
              </v-col>
            </v-row>

            <!-- Conditions Section -->
            <v-row class="mb-4">
              <v-col cols="12">
                <div class="d-flex align-center justify-space-between mb-4">
                  <h4 class="text-h6 font-weight-medium">Conditions</h4>
                  <v-btn
                    @click="addCondition"
                    color="success"
                    variant="tonal"
                    size="small"
                    prepend-icon="mdi-plus"
                  >
                    Add Condition
                  </v-btn>
                </div>
                <div class="conditions-list">
                  <ConditionEditor
                    v-for="(condition, index) in localRule.conditions"
                    :key="index"
                    :condition="condition"
                    @update:condition="
                      (newCondition) => updateCondition(index, newCondition)
                    "
                    @remove="removeCondition(index)"
                    class="mb-3"
                  />
                </div>
              </v-col>
            </v-row>

            <!-- Calculation Section -->
            <v-row>
              <v-col cols="12">
                <h4 class="text-h6 font-weight-medium mb-4">Calculation</h4>
                <CalculationEditor
                  :calculation="localRule.calculation"
                  @update:calculation="updateCalculation"
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-expand-transition>
  </v-card>
</template>

<script setup lang="ts">
import ConditionEditor from "./ConditionEditor.vue";
import CalculationEditor from "./CalculationEditor.vue";
import type {
  RuleEditorProps,
  RuleEditorEmits,
  Rule,
  Condition,
  Calculation,
} from "@/pages/calculator/types/index";

// Props
const props = defineProps<RuleEditorProps>();

// Emits
const emit = defineEmits<RuleEditorEmits>();

// Options for select fields
const categoryOptions = [
  { title: "End Month Sales", value: "End Month Sales" },
  { title: "Weekly Sales", value: "Weekly Sales" },
  { title: "Daily Sales", value: "Daily Sales" },
  { title: "Portfolio Commission", value: "Portfolio Commission" },
  { title: "Additional Commission", value: "Additional Commission" },
];

const frequencyOptions = [
  { title: "Daily", value: "daily" },
  { title: "Weekly", value: "weekly" },
  { title: "Monthly", value: "monthly" },
];

// Local state
const isExpanded = ref(false);
const localRule = reactive<Rule>({ ...props.rule });
const hasChanges = ref(false);

// Watch for changes in props.rule to update local copy
watch(
  () => props.rule,
  (newRule: Rule) => {
    Object.assign(localRule, newRule);
    hasChanges.value = false;
  },
  { deep: true },
);

// Watch for changes in local rule to detect modifications
watch(
  localRule,
  () => {
    hasChanges.value = JSON.stringify(localRule) !== JSON.stringify(props.rule);
  },
  { deep: true },
);

// Event handlers
const toggleExpanded = (): void => {
  isExpanded.value = !isExpanded.value;
};

const handleEnabledChange = (event: Event): void => {
  const target = event.target as HTMLInputElement;
  localRule.enabled = target.checked;
};

const handleRemove = (): void => {
  emit("remove");
};

const updateLocalRule = (field: keyof Rule, value: any): void => {
  (localRule as any)[field] = value;
};

const saveChanges = (): void => {
  emit("update:rule", { ...localRule });
  hasChanges.value = false;
};

const discardChanges = (): void => {
  Object.assign(localRule, props.rule);
  hasChanges.value = false;
};

const addCondition = (): void => {
  const newCondition: Condition = {
    field: "netSales",
    operator: "gt",
    value: 0,
  };
  localRule.conditions = [...localRule.conditions, newCondition];
};

const updateCondition = (index: number, newCondition: Condition): void => {
  const newConditions = [...localRule.conditions];
  newConditions[index] = newCondition;
  localRule.conditions = newConditions;
};

const removeCondition = (index: number): void => {
  localRule.conditions = localRule.conditions.filter(
    (_: Condition, i: number) => i !== index,
  );
};

const updateCalculation = (calculation: Calculation): void => {
  localRule.calculation = calculation;
};
</script>

<style scoped>
.rule-editor-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 16px;
  overflow: hidden;
}

.rule-editor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.rule-expanded {
  box-shadow: 0 8px 32px rgba(var(--v-theme-primary), 0.15);
}

.rule-disabled {
  opacity: 0.7;
  background: rgba(var(--v-theme-surface-variant), 0.3);
}

.rule-info {
  flex: 1;
}

.edit-form {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
}

:deep(.rule-input .v-field) {
  border-radius: 12px;
}

:deep(.rule-input .v-field--focused) {
  box-shadow: 0 0 0 2px rgba(var(--v-theme-primary), 0.2);
}

.conditions-list {
  background: rgba(var(--v-theme-surface), 1);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(var(--v-theme-outline), 0.2);
}

/* Switch styling */
:deep(.v-switch .v-selection-control__wrapper) {
  height: 32px;
}

/* Chip styling */
:deep(.v-chip) {
  border-radius: 8px;
  font-weight: 500;
}

/* Button styling */
:deep(.v-btn) {
  border-radius: 8px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
}

/* Animation for expand transition */
:deep(.v-expand-transition-enter-active),
:deep(.v-expand-transition-leave-active) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 960px) {
  :deep(.v-card-title) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .d-flex.ga-2 {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 600px) {
  .d-flex.ga-2 {
    flex-direction: column;
    width: 100%;
  }

  :deep(.v-btn) {
    width: 100%;
  }
}
</style>
