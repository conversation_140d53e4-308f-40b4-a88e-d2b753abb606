<template>
  <v-card elevation="2" class="sales-data-card">
    <v-card-title class="d-flex align-center">
      <v-icon size="28" color="primary" class="mr-3"
        >mdi-database-outline</v-icon
      >
      <div>
        <h2 class="text-h5 font-weight-bold">Sales Data Input</h2>
        <p class="text-body-2 text-medium-emphasis">
          Configure sales parameters for commission calculation
        </p>
      </div>
    </v-card-title>

    <v-card-text class="pa-6">
      <v-form>
        <!-- Net Sales and Loan Term -->
        <v-row class="mb-4">
          <v-col cols="12" md="6">
            <v-text-field
              :model-value="salesData.netSales"
              @update:model-value="
                updateField('netSales', parseFloat($event) || 0)
              "
              label="Net Sales (KES)"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-currency-usd"
              placeholder="250000"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" md="6">
            <v-text-field
              :model-value="salesData.loanTerm"
              @update:model-value="
                updateField('loanTerm', parseInt($event) || 0)
              "
              label="Loan Term (months)"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-calendar-month"
              placeholder="24"
              class="sales-input"
            />
          </v-col>
        </v-row>

        <!-- Loan Type and Client Type -->
        <v-row class="mb-4">
          <v-col cols="12" md="6">
            <v-select
              :model-value="salesData.loanType"
              @update:model-value="updateField('loanType', $event)"
              label="Loan Type"
              :items="loanTypeOptions"
              variant="outlined"
              prepend-inner-icon="mdi-file-document-outline"
            />
          </v-col>
          <v-col cols="12" md="6">
            <v-select
              :model-value="salesData.clientType"
              @update:model-value="updateField('clientType', $event)"
              label="Client Type"
              :items="clientTypeOptions"
              variant="outlined"
              prepend-inner-icon="mdi-account-outline"
            />
          </v-col>
        </v-row>

        <!-- Portfolio, PAR, and PAR Days -->
        <v-row class="mb-4">
          <v-col cols="12" md="4">
            <v-text-field
              :model-value="salesData.portfolio"
              @update:model-value="
                updateField('portfolio', parseFloat($event) || 0)
              "
              label="Portfolio Size (KES)"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-briefcase-outline"
              placeholder="150000"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              :model-value="salesData.par"
              @update:model-value="updateField('par', parseFloat($event) || 0)"
              label="PAR (%)"
              type="number"
              step="0.1"
              variant="outlined"
              prepend-inner-icon="mdi-percent"
              placeholder="3.5"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              :model-value="salesData.parDays"
              @update:model-value="
                updateField('parDays', parseFloat($event) || 0)
              "
              label="PAR Days Threshold"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-clock-outline"
              placeholder="30"
              hint="Days past due"
              class="sales-input"
            />
          </v-col>
        </v-row>

        <!-- Sales Data: Weekly Target, Daily, Weekly, Monthly -->
        <v-row class="mb-4">
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.weeklyTarget"
              @update:model-value="
                updateField('weeklyTarget', parseFloat($event) || 0)
              "
              label="Weekly Target (KES)"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-target"
              placeholder="100000"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.dailyNetSales"
              @update:model-value="
                updateField('dailyNetSales', parseFloat($event) || 0)
              "
              label="Daily Sales (KES)"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-calendar-today"
              placeholder="35000"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.weeklyNetSales"
              @update:model-value="
                updateField('weeklyNetSales', parseFloat($event) || 0)
              "
              label="Weekly Sales (KES)"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-calendar-week"
              placeholder="180000"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.monthlyNetSales"
              @update:model-value="
                updateField('monthlyNetSales', parseFloat($event) || 0)
              "
              label="Monthly Sales (KES)"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-calendar-month-outline"
              placeholder="750000"
              class="sales-input"
            />
          </v-col>
        </v-row>

        <!-- Loan Counts and Employer Type -->
        <v-row class="mb-4">
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.numberOfLoans"
              @update:model-value="
                updateField('numberOfLoans', parseInt($event) || 0)
              "
              label="Total Loans"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-counter"
              placeholder="5"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.numberOfNewLoans"
              @update:model-value="
                updateField('numberOfNewLoans', parseInt($event) || 0)
              "
              label="New Loans"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-plus-circle-outline"
              placeholder="3"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.numberOfRepeatLoans"
              @update:model-value="
                updateField('numberOfRepeatLoans', parseInt($event) || 0)
              "
              label="Repeat Loans"
              type="number"
              variant="outlined"
              prepend-inner-icon="mdi-repeat"
              placeholder="2"
              class="sales-input"
            />
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-text-field
              :model-value="salesData.employerType"
              @update:model-value="updateField('employerType', $event)"
              label="Employer Type"
              variant="outlined"
              prepend-inner-icon="mdi-office-building"
              placeholder="Government"
              class="sales-input"
            />
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import type {
  SalesDataFormProps,
  SalesDataFormEmits,
  SalesData,
} from "@/pages/calculator/types/index";

// Props
const props = defineProps<SalesDataFormProps>();
// Emits
const emit = defineEmits<SalesDataFormEmits>();

// Options for select fields
const loanTypeOptions = [
  { title: "New", value: "New" },
  { title: "Repeat", value: "Repeat" },
  { title: "Buyoff", value: "Buyoff" },
  { title: "Top-up", value: "Top-up" },
  { title: "Partnership", value: "Partnership" },
];
const clientTypeOptions = [
  { title: "New Client", value: "New" },
  { title: "Repeat Client", value: "Repeat" },
];

// Helper function to update fields
const updateField = (field: keyof SalesData, value: any): void => {
  const updatedSalesData = { ...props.salesData, [field]: value };
  emit("update:salesData", updatedSalesData);
};
</script>

<style scoped>
.sales-data-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sales-data-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

:deep(.sales-input .v-field) {
  border-radius: 12px;
}

:deep(.sales-input .v-field--focused) {
  box-shadow: 0 0 0 2px rgba(var(--v-theme-primary), 0.2);
}

/* Modern input styling */
:deep(.v-text-field .v-field__input) {
  font-family: "Roboto Mono", monospace;
  text-align: right;
}

:deep(.v-select .v-field__input) {
  font-weight: 500;
}
</style>
