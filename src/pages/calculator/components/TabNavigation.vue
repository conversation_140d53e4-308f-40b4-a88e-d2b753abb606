<template>
  <v-card elevation="1" class="tab-navigation-card mb-6">
    <v-tabs
      v-model="activeTabLocal"
      color="primary"
      align-tabs="start"
      class="modern-tabs"
    >
      <v-tab
        value="calculator"
        class="tab-item"
        prepend-icon="mdi-calculator-variant"
      >
        Calculator
      </v-tab>
      <v-tab value="rules" class="tab-item" prepend-icon="mdi-cog">
        Rules Management
      </v-tab>
      <v-tab value="api-rules" class="tab-item" prepend-icon="mdi-api">
        API Rules
      </v-tab>
    </v-tabs>
  </v-card>
</template>

<script setup lang="ts">
import type {
  TabNavigationProps,
  TabNavigationEmits,
  TabType,
} from "@/pages/calculator/types/index";

// Props
const props = defineProps<TabNavigationProps>();

// Emits
const emit = defineEmits<TabNavigationEmits>();

// Computed property for active tab
const activeTabLocal = computed({
  get: () => props.activeTab,
  set: (value: TabType) => emit("update:activeTab", value),
});
</script>

<style scoped>
.tab-navigation-card {
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.modern-tabs {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-surface), 1) 0%,
    rgba(var(--v-theme-surface-variant), 0.2) 100%
  );
}

:deep(.v-tab) {
  border-radius: 12px 12px 0 0;
  margin: 0 4px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.v-tab--selected) {
  background: rgba(var(--v-theme-primary), 0.1);
  color: rgb(var(--v-theme-primary));
}

:deep(.v-tab:hover) {
  background: rgba(var(--v-theme-primary), 0.05);
}

:deep(.v-tabs-slider) {
  height: 3px;
  border-radius: 2px;
  background: linear-gradient(
    90deg,
    rgb(var(--v-theme-primary)) 0%,
    rgb(var(--v-theme-primary-darken-1)) 100%
  );
}

/* Responsive adjustments */
@media (max-width: 600px) {
  :deep(.v-tab) {
    min-width: auto;
    padding: 8px 12px;
    font-size: 0.875rem;
  }
}
</style>
