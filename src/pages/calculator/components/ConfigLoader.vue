<template>
  <!-- Loading State -->
  <div v-if="isLoading" class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">
          Loading Configuration
        </h2>
        <p class="text-gray-600 mb-4">
          Fetching commission rules and settings from the server...
        </p>
        <div class="flex items-center justify-center space-x-2 text-sm text-gray-500">
          <Server class="w-4 h-4" />
          <span>Connecting to API server</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div v-else-if="hasError" class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
      <div class="text-center">
        <div class="flex items-center justify-center mb-4">
          <Wifi v-if="isApiConnected" class="w-12 h-12 text-yellow-500" />
          <WifiOff v-else class="w-12 h-12 text-red-500" />
        </div>
        
        <h2 class="text-xl font-semibold text-gray-900 mb-2">
          Configuration Error
        </h2>
        
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <div class="flex items-start">
            <AlertCircle class="w-5 h-5 text-red-400 mt-0.5 mr-3 flex-shrink-0" />
            <div class="text-sm text-red-700">
              <p class="font-medium mb-1">Failed to load configuration</p>
              <p>{{ error }}</p>
            </div>
          </div>
        </div>

        <div v-if="!isApiConnected" class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
          <div class="text-sm text-blue-700">
            <p class="font-medium mb-2">To start the API server:</p>
            <div class="bg-blue-100 rounded p-2 font-mono text-xs">
              <p>npm run server</p>
              <p class="text-blue-600 mt-1">or</p>
              <p>npm run dev:full</p>
            </div>
          </div>
        </div>

        <button
          @click="handleRetryConnection"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          <RefreshCw class="w-4 h-4 mr-2" />
          Retry Connection
        </button>

        <div class="mt-4 text-xs text-gray-500">
          <p>Make sure JSON Server is running on port 3001</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Success State - Render Children -->
  <div v-else>
    <!-- API Status Indicator -->
    <div class="fixed top-4 right-4 z-50">
      <div 
        :class="statusIndicatorClasses"
        class="flex items-center space-x-2 px-3 py-1 rounded-full text-xs"
      >
        <div 
          :class="statusDotClasses"
          class="w-2 h-2 rounded-full"
        ></div>
        <span>{{ statusText }}</span>
      </div>
    </div>
    
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { AlertCircle, RefreshCw, Server, Wifi, WifiOff } from 'lucide-vue-next'
import { useConfigStatus } from '../composables'

// Use the config status composable
const { 
  isLoading, 
  error, 
  hasError,
  isApiConnected, 
  retryConnection 
} = useConfigStatus()

// Computed properties for status indicator
const statusIndicatorClasses = computed(() => {
  if (isApiConnected.value) {
    return 'bg-green-100 text-green-800'
  }
  return 'bg-yellow-100 text-yellow-800'
})

const statusDotClasses = computed(() => {
  if (isApiConnected.value) {
    return 'bg-green-500 animate-pulse'
  }
  return 'bg-yellow-500'
})

const statusText = computed(() => {
  if (isApiConnected.value) {
    return 'API Connected'
  }
  return 'Offline Mode'
})

// Event handlers
const handleRetryConnection = async (): Promise<void> => {
  await retryConnection()
}
</script>
