<template>
  <v-card elevation="2" class="header-card mb-6">
    <v-card-text class="pa-6">
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-icon size="40" color="primary" class="mr-4"
            >mdi-calculator-variant</v-icon
          >
          <div>
            <h1 class="text-h4 font-weight-bold text-primary mb-1">
              Dynamic Commission Calculator
            </h1>
            <p class="text-body-1 text-medium-emphasis">
              Configure and calculate sales commissions with custom rules
            </p>
          </div>
        </div>
        <div class="d-flex align-center ga-3">
          <v-btn
            @click="handleExport"
            color="success"
            variant="elevated"
            prepend-icon="mdi-download"
            class="export-btn"
          >
            Export Config
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            prepend-icon="mdi-upload"
            class="import-btn"
            @click="$refs.fileInput?.click()"
          >
            Import Config
          </v-btn>
          <input
            ref="fileInput"
            type="file"
            accept=".json"
            @change="handleImport"
            style="display: none"
          />
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { HeaderProps, HeaderEmits } from "../types";

// Props (none for this component, but keeping interface for consistency)
defineProps<HeaderProps>();

// Emits
const emit = defineEmits<HeaderEmits>();

// Template ref for file input
const fileInput = ref<HTMLInputElement>();

// Event handlers
const handleExport = (): void => {
  emit("export");
};

const handleImport = (event: Event): void => {
  emit("import", event);
};
</script>

<style scoped>
.header-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-surface), 1) 0%,
    rgba(var(--v-theme-surface-variant), 0.3) 100%
  );
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
}

.export-btn,
.import-btn {
  border-radius: 12px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.export-btn:hover,
.import-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .d-flex.justify-space-between {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .d-flex.ga-3 {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 600px) {
  .d-flex.ga-3 {
    flex-direction: column;
    width: 100%;
  }

  .export-btn,
  .import-btn {
    width: 100%;
  }
}
</style>
