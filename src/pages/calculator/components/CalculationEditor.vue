<template>
  <v-card class="calculation-editor-card" variant="outlined" elevation="1">
    <v-card-title class="d-flex align-center pa-4">
      <v-icon size="24" color="primary" class="mr-3">mdi-calculator</v-icon>
      <h4 class="text-h6 font-weight-semibold">Calculation Configuration</h4>
    </v-card-title>

    <v-divider />

    <v-card-text class="pa-6">
      <!-- Calculation Type and Base Field -->
      <v-row class="mb-4">
        <v-col cols="12" md="6">
          <v-select
            v-model="calculation.type"
            @update:model-value="handleTypeChange"
            label="Calculation Type"
            :items="calculationTypeOptions"
            variant="outlined"
            prepend-inner-icon="mdi-function-variant"
            class="calculation-input"
          />
        </v-col>

        <v-col cols="12" md="6">
          <v-select
            v-model="calculation.baseField"
            @update:model-value="handleBaseFieldChange"
            label="Base Field"
            :items="baseFieldOptions"
            variant="outlined"
            prepend-inner-icon="mdi-database-outline"
            class="calculation-input"
          />
        </v-col>
      </v-row>

      <!-- Percentage Type -->
      <v-row v-if="calculation.type === 'percentage'" class="mb-4">
        <v-col cols="12" md="6">
          <v-text-field
            v-model.number="calculation.value"
            @update:model-value="handleValueChange"
            label="Percentage (%)"
            type="number"
            step="0.01"
            variant="outlined"
            prepend-inner-icon="mdi-percent"
            placeholder="2.5"
            hint="Enter percentage value (e.g., 2.5 for 2.5%)"
            class="calculation-input"
          />
        </v-col>
      </v-row>

      <!-- Fixed Amount Type -->
      <v-row v-if="calculation.type === 'fixed'" class="mb-4">
        <v-col cols="12" md="6">
          <v-text-field
            v-model.number="calculation.value"
            @update:model-value="handleValueChange"
            label="Fixed Amount (KES)"
            type="number"
            variant="outlined"
            prepend-inner-icon="mdi-currency-usd"
            placeholder="5000"
            hint="Enter fixed commission amount"
            class="calculation-input"
          />
        </v-col>
      </v-row>

      <!-- Per-Unit Type -->
      <v-row v-if="calculation.type === 'per-unit'" class="mb-4">
        <v-col cols="12" md="6">
          <v-text-field
            v-model.number="calculation.unitAmount"
            @update:model-value="handleUnitAmountChange"
            label="Amount Per Unit (KES)"
            type="number"
            variant="outlined"
            prepend-inner-icon="mdi-counter"
            placeholder="1000"
            hint="Amount paid per unit of the base field (e.g., KES 1,000 per loan)"
            class="calculation-input"
          />
        </v-col>
      </v-row>

      <!-- Tiered Type -->
      <template v-if="calculation.type === 'tiered'">
        <v-row class="mb-4">
          <v-col cols="12" md="6">
            <v-select
              v-model="calculation.comparisonField"
              @update:model-value="handleComparisonFieldChange"
              label="Comparison Field"
              :items="comparisonFieldOptions"
              variant="outlined"
              prepend-inner-icon="mdi-compare"
              hint="Which field to compare against tier ranges"
              class="calculation-input"
            />
            <v-alert type="info" variant="tonal" density="compact" class="mt-2">
              <div class="text-body-2">
                <strong>Base Field:</strong> What commission is calculated on<br />
                <strong>Comparison Field:</strong> What determines which tier
                applies
              </div>
            </v-alert>
          </v-col>

          <v-col cols="12" md="6">
            <v-select
              v-model="calculation.tierValueType"
              @update:model-value="handleTierValueTypeChange"
              label="Tier Value Type"
              :items="tierValueTypeOptions"
              variant="outlined"
              prepend-inner-icon="mdi-format-list-numbered"
              hint="How to interpret the values in your tiers"
              class="calculation-input"
            />
            <v-alert type="info" variant="tonal" density="compact" class="mt-2">
              <div class="text-body-2">
                <strong>Percentage:</strong> Applied as % of base field<br />
                <strong>Fixed Amount:</strong> Returned as-is (KES amounts)
              </div>
            </v-alert>
          </v-col>
        </v-row>

        <v-row class="mb-4">
          <v-col cols="12">
            <TierEditor
              :tiers="calculation.tiers || []"
              @update:tiers="handleTiersChange"
              :comparison-field="
                calculation.comparisonField || calculation.baseField
              "
              :tier-value-type="calculation.tierValueType || 'percentage'"
            />
          </v-col>
        </v-row>
      </template>

      <!-- Formula Type -->
      <v-row v-if="calculation.type === 'formula'" class="mb-4">
        <v-col cols="12">
          <v-textarea
            v-model="calculation.formula"
            @update:model-value="handleFormulaChange"
            label="Formula (JavaScript)"
            variant="outlined"
            prepend-inner-icon="mdi-code-braces"
            placeholder="e.g., data.netSales * 0.025"
            rows="4"
            hint="Enter JavaScript formula using available variables"
            class="calculation-input formula-input"
          />

          <!-- Formula Validation -->
          <v-alert
            v-if="invalidVariables.length > 0"
            type="error"
            variant="tonal"
            class="mt-3"
          >
            <div class="text-body-2 font-weight-medium mb-2">
              Invalid variables:
            </div>
            <ul class="text-body-2">
              <li v-for="(variable, index) in invalidVariables" :key="index">
                • {{ variable }}
              </li>
            </ul>
          </v-alert>

          <!-- Available Variables -->
          <v-expansion-panels class="mt-4" variant="accordion">
            <v-expansion-panel>
              <v-expansion-panel-title>
                <div class="d-flex align-center">
                  <v-icon class="mr-2">mdi-variable</v-icon>
                  <span class="text-body-1 font-weight-medium"
                    >Available Variables</span
                  >
                </div>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-row>
                  <v-col
                    v-for="variable in formulaVariables"
                    :key="variable.name"
                    cols="12"
                    md="6"
                  >
                    <v-card variant="outlined" class="pa-3">
                      <div class="d-flex align-center mb-2">
                        <v-chip size="small" color="primary" variant="tonal">
                          <code class="text-body-2">{{ variable.name }}</code>
                        </v-chip>
                      </div>
                      <p class="text-body-2 text-medium-emphasis">
                        {{ variable.description }}
                      </p>
                    </v-card>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>

          <!-- Quick Examples -->
          <v-alert type="info" variant="tonal" class="mt-4">
            <div class="text-body-2 font-weight-medium mb-3">
              Formula Examples:
            </div>
            <div class="examples-list">
              <div class="example-item mb-2">
                <v-chip
                  size="small"
                  color="success"
                  variant="tonal"
                  class="mr-2"
                >
                  <code class="text-body-2">data.netSales * 0.025</code>
                </v-chip>
                <span class="text-body-2">2.5% of net sales</span>
              </div>
              <div class="example-item mb-2">
                <v-chip
                  size="small"
                  color="success"
                  variant="tonal"
                  class="mr-2"
                >
                  <code class="text-body-2"
                    >data.par &lt; 5 ? data.numberOfNewLoans * 1000 : 0</code
                  >
                </v-chip>
                <span class="text-body-2"
                  >KES 1,000 per new loan if PAR &lt; 5%</span
                >
              </div>
              <div class="example-item">
                <v-chip
                  size="small"
                  color="success"
                  variant="tonal"
                  class="mr-2"
                >
                  <code class="text-body-2"
                    >data.weeklyNetSales &gt;= data.weeklyTarget ? 5000 :
                    0</code
                  >
                </v-chip>
                <span class="text-body-2">KES 5,000 if target met</span>
              </div>
            </div>
          </v-alert>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import TierEditor from "./TierEditor.vue";
import {
  useFieldConfig,
  useConfig,
} from "@/pages/calculator/composables/index";
import type {
  CalculationEditorProps,
  CalculationEditorEmits,
  Calculation,
} from "@/pages/calculator/types/index";

// Props
const props = defineProps<CalculationEditorProps>();

// Emits
const emit = defineEmits<CalculationEditorEmits>();

// Use composables for dynamic data
const { fieldConfig } = useFieldConfig();
const { formulaVariables } = useConfig();

// Options for select fields
const calculationTypeOptions = [
  { title: "Percentage", value: "percentage" },
  { title: "Fixed Amount", value: "fixed" },
  { title: "Tiered", value: "tiered" },
  { title: "Amount Per Unit", value: "per-unit" },
  { title: "Formula", value: "formula" },
];

const tierValueTypeOptions = [
  { title: "Percentage (e.g., 2.5 = 2.5%)", value: "percentage" },
  { title: "Fixed Amount (e.g., 5000 = KES 5,000)", value: "fixed" },
];

// Dynamic field options from API
const baseFieldOptions = computed(() =>
  fieldConfig.value
    .filter((field) => field.type === "number")
    .map((field) => ({ title: field.label, value: field.field })),
);

const comparisonFieldOptions = computed(() =>
  fieldConfig.value.map((field) => ({
    title: field.label,
    value: field.field,
  })),
);

// Formula validation using dynamic variables
const invalidVariables = computed(() => {
  const formula = props.calculation.formula || "";
  const dataRefs = formula.match(/data\.\w+/g) || [];
  const validVars = formulaVariables.value.map((variable) => variable.name);
  return dataRefs.filter((ref) => !validVars.includes(ref));
});

// Event handlers
const updateCalculation = (updates: Partial<Calculation>): void => {
  const updatedCalculation = { ...props.calculation, ...updates };
  emit("update:calculation", updatedCalculation);
};

const handleTypeChange = (value: string): void => {
  updateCalculation({ type: value as Calculation["type"] });
};

const handleBaseFieldChange = (value: string): void => {
  updateCalculation({ baseField: value });
};

const handleValueChange = (value: number): void => {
  updateCalculation({ value: value || 0 });
};

const handleUnitAmountChange = (value: number): void => {
  updateCalculation({ unitAmount: value || 0 });
};

const handleComparisonFieldChange = (value: string): void => {
  updateCalculation({ comparisonField: value });
};

const handleTierValueTypeChange = (value: string): void => {
  updateCalculation({ tierValueType: value as "percentage" | "fixed" });
};

const handleTiersChange = (
  tiers: Array<{ min: number; max: number; value: number }>,
): void => {
  updateCalculation({ tiers });
};

const handleFormulaChange = (value: string): void => {
  updateCalculation({ formula: value });
};
</script>

<style scoped>
.calculation-editor-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.calculation-editor-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

:deep(.calculation-input .v-field) {
  border-radius: 12px;
  font-size: 1rem;
}

:deep(.calculation-input .v-field--focused) {
  box-shadow: 0 0 0 2px rgba(var(--v-theme-primary), 0.2);
}

:deep(.calculation-input .v-field__input) {
  font-size: 1rem;
  line-height: 1.5;
}

:deep(.calculation-input .v-field__prepend-inner) {
  padding-top: 12px;
}

/* Formula input styling */
:deep(.formula-input .v-field__input) {
  font-family: "Roboto Mono", monospace;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Consistent typography */
:deep(.v-card-title) {
  font-size: 1.25rem;
  font-weight: 600;
}

:deep(.v-alert .v-alert__content) {
  font-size: 0.95rem;
  line-height: 1.5;
}

:deep(.v-expansion-panel-title) {
  font-size: 1rem;
  font-weight: 500;
}

/* Example items styling */
.examples-list .example-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.examples-list code {
  font-size: 0.85rem;
  font-family: "Roboto Mono", monospace;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .examples-list .example-item {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Consistent spacing */
:deep(.v-field__hint) {
  font-size: 0.875rem;
  line-height: 1.4;
}

:deep(.v-chip) {
  font-size: 0.875rem;
  font-weight: 500;
}
</style>
