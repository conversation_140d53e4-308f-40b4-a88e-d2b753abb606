<template>
  <div class="flex items-center gap-2 p-3 border rounded bg-gray-50">
    <!-- Field Selection -->
    <select
      :value="condition.field"
      @change="handleFieldChange"
      class="border rounded px-2 py-1"
    >
      <option
        v-for="option in fieldOptions"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>

    <!-- Operator Selection -->
    <select
      :value="condition.operator"
      @change="handleOperatorChange"
      class="border rounded px-2 py-1"
    >
      <option
        v-for="option in availableOperators"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>

    <!-- Value Input - Select Field -->
    <select
      v-if="fieldType === 'select'"
      :value="condition.value"
      @change="handleValueChange"
      class="border rounded px-3 py-2 w-40"
    >
      <option value="">Select...</option>
      <option
        v-for="option in currentFieldOptions"
        :key="option"
        :value="option"
      >
        {{ option }}
      </option>
    </select>

    <!-- Value Input - Number Field -->
    <input
      v-else
      type="number"
      :value="condition.value"
      @input="handleNumberValueChange"
      class="border rounded px-3 py-2 w-40 font-mono text-right"
      placeholder="Enter value"
    />

    <!-- Remove Button -->
    <button
      @click="handleRemove"
      class="text-red-500 hover:text-red-700 px-2 py-1 transition-colors"
    >
      ×
    </button>
  </div>
</template>

<script setup lang="ts">
import {
  useFieldConfig,
  useDropdownOptions,
} from "@/pages/calculator/composables/index";
import type {
  ConditionEditorProps,
  ConditionEditorEmits,
  Condition,
} from "@/pages/calculator/types/index";

// Props
const props = defineProps<ConditionEditorProps>();

// Emits
const emit = defineEmits<ConditionEditorEmits>();

// Composables
const { fieldConfig } = useFieldConfig();
const { dropdownOptions } = useDropdownOptions();

// Helper functions
const getFieldType = (fieldName: string): "number" | "select" => {
  const field = fieldConfig.value.find((f) => f.field === fieldName);
  return field?.type || "number";
};

const getFieldOptions = (fieldName: string): string[] => {
  const field = fieldConfig.value.find((f) => f.field === fieldName);
  return field?.options || [];
};

const getAvailableOperators = (fieldName: string) => {
  const fieldType = getFieldType(fieldName);
  if (fieldType === "select") {
    return dropdownOptions.value.operators.select;
  }
  return dropdownOptions.value.operators.number;
};

// Computed properties
const fieldType = computed(() => getFieldType(props.condition.field));

const fieldOptions = computed(() =>
  fieldConfig.value.map((field) => ({
    value: field.field,
    label: field.label,
  })),
);

const availableOperators = computed(() =>
  getAvailableOperators(props.condition.field),
);

const currentFieldOptions = computed(() =>
  getFieldOptions(props.condition.field),
);

// Event handlers
const handleFieldChange = (event: Event): void => {
  const target = event.target as HTMLSelectElement;
  const updatedCondition: Condition = {
    ...props.condition,
    field: target.value,
  };
  emit("update:condition", updatedCondition);
};

const handleOperatorChange = (event: Event): void => {
  const target = event.target as HTMLSelectElement;
  const updatedCondition: Condition = {
    ...props.condition,
    operator: target.value as Condition["operator"],
  };
  emit("update:condition", updatedCondition);
};

const handleValueChange = (event: Event): void => {
  const target = event.target as HTMLSelectElement;
  const updatedCondition: Condition = {
    ...props.condition,
    value: target.value,
  };
  emit("update:condition", updatedCondition);
};

const handleNumberValueChange = (event: Event): void => {
  const target = event.target as HTMLInputElement;
  const updatedCondition: Condition = {
    ...props.condition,
    value: parseFloat(target.value) || 0,
  };
  emit("update:condition", updatedCondition);
};

const handleRemove = (): void => {
  emit("remove");
};

// Auto-correct invalid operator for field type
watch(
  () => [props.condition.field, props.condition.operator],
  ([newField, newOperator]) => {
    const fieldType = getFieldType(newField);
    if (fieldType === "select" && newOperator !== "eq") {
      // Force select fields to use 'eq' operator
      const updatedCondition: Condition = {
        ...props.condition,
        operator: "eq",
      };
      emit("update:condition", updatedCondition);
    }
  },
);
</script>
