// Types and Interfaces for Vue 3 Commission Calculator

export interface Rule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  category: string;
  frequency?: 'daily' | 'weekly' | 'monthly';
  conditions: Condition[];
  calculation: Calculation;
  priority: number;
}

export interface Condition {
  field: string;
  operator: 'eq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'between';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface Calculation {
  type: 'percentage' | 'fixed' | 'tiered' | 'formula' | 'per-unit';
  baseField: string;           // What to calculate commission on
  comparisonField?: string;    // What to compare against tiers (for tiered only)
  tierValueType?: 'percentage' | 'fixed';  // How to interpret tier values (for tiered only)
  value?: number;
  tiers?: Array<{ min: number; max: number; value: number }>;
  formula?: string;
  unitAmount?: number;         // Amount per unit (for per-unit calculations)
}

export interface SalesData {
  netSales: number;
  loanTerm: number;
  loanType: 'New' | 'Repeat' | 'Buyoff' | 'Top-up' | 'Partnership';
  clientType: 'New' | 'Repeat';
  portfolio: number;
  par: number;           // PAR percentage
  parDays: number;       // Days past due threshold for PAR calculation
  weeklyTarget: number;  // Weekly sales target
  dailyNetSales: number;
  weeklyNetSales: number;
  monthlyNetSales: number;
  numberOfLoans: number;       // Total number of loans
  numberOfNewLoans: number;    // Number of new loans only
  numberOfRepeatLoans: number; // Number of repeat loans only
  employerType: string;
}

// Available variables for formula validation and autocomplete
export const AVAILABLE_VARIABLES = [
  { name: 'data.netSales', type: 'number', description: 'Net sales amount for the period' },
  { name: 'data.loanTerm', type: 'number', description: 'Loan term in months' },
  { name: 'data.loanType', type: 'string', description: 'Loan type: "New", "Repeat", "Buyoff", "Top-up", "Partnership"' },
  { name: 'data.clientType', type: 'string', description: 'Client type: "New" or "Repeat"' },
  { name: 'data.portfolio', type: 'number', description: 'Portfolio size under management' },
  { name: 'data.par', type: 'number', description: 'PAR percentage' },
  { name: 'data.parDays', type: 'number', description: 'Days past due threshold for PAR calculation' },
  { name: 'data.weeklyTarget', type: 'number', description: 'Weekly sales target' },
  { name: 'data.dailyNetSales', type: 'number', description: 'Daily net sales amount' },
  { name: 'data.weeklyNetSales', type: 'number', description: 'Weekly net sales amount' },
  { name: 'data.monthlyNetSales', type: 'number', description: 'Monthly net sales amount' },
  { name: 'data.numberOfLoans', type: 'number', description: 'Total number of loans' },
  { name: 'data.numberOfNewLoans', type: 'number', description: 'Number of new loans only' },
  { name: 'data.numberOfRepeatLoans', type: 'number', description: 'Number of repeat loans only' },
  { name: 'data.employerType', type: 'string', description: 'Employer type' }
] as const;

// Field configuration for condition editor
export const FIELD_CONFIG = {
  netSales: { type: 'number', label: 'Net Sales' },
  loanTerm: { type: 'number', label: 'Loan Term (months)' },
  loanType: {
    type: 'select',
    label: 'Loan Type',
    options: ['New', 'Repeat', 'Buyoff', 'Top-up', 'Partnership']
  },
  clientType: {
    type: 'select',
    label: 'Client Type',
    options: ['New', 'Repeat']
  },
  portfolio: { type: 'number', label: 'Portfolio Size' },
  par: { type: 'number', label: 'PAR %' },
  parDays: { type: 'number', label: 'PAR Days Threshold' },
  weeklyTarget: { type: 'number', label: 'Weekly Target' },
  dailyNetSales: { type: 'number', label: 'Daily Net Sales' },
  weeklyNetSales: { type: 'number', label: 'Weekly Net Sales' },
  monthlyNetSales: { type: 'number', label: 'Monthly Net Sales' },
  numberOfLoans: { type: 'number', label: 'Total Number of Loans' },
  numberOfNewLoans: { type: 'number', label: 'Number of New Loans' },
  numberOfRepeatLoans: { type: 'number', label: 'Number of Repeat Loans' },
  employerType: {
    type: 'select',
    label: 'Employer Type',
    options: ['Government', 'Private', 'NGO', 'Self-Employed']
  }
} as const;

export interface CommissionResult {
  total: number;
  breakdown: Array<{
    ruleName: string;
    category: string;
    amount: number;
    calculation: string;
  }>;
}

export type TabType = 'calculator' | 'rules' | 'api-rules';

// Vue 3 Component Props interfaces
export interface ConditionEditorProps {
  condition: Condition;
}

export interface ConditionEditorEmits {
  (e: 'update:condition', condition: Condition): void;
  (e: 'remove'): void;
}

export interface TierEditorProps {
  tiers: Array<{ min: number; max: number; value: number }>;
  comparisonField?: string;
  tierValueType?: 'percentage' | 'fixed';
}

export interface TierEditorEmits {
  (e: 'update:tiers', tiers: Array<{ min: number; max: number; value: number }>): void;
}

export interface CalculationEditorProps {
  calculation: Calculation;
}

export interface CalculationEditorEmits {
  (e: 'update:calculation', calculation: Calculation): void;
}

export interface RuleEditorProps {
  rule: Rule;
}

export interface RuleEditorEmits {
  (e: 'update:rule', rule: Rule): void;
  (e: 'remove'): void;
}

export interface SalesDataFormProps {
  salesData: SalesData;
}

export interface SalesDataFormEmits {
  (e: 'update:salesData', salesData: SalesData): void;
}

export interface CommissionResultsProps {
  result: CommissionResult;
}

export interface RulesManagementProps {
  rules: Rule[];
}

export interface RulesManagementEmits {
  (e: 'updateRule', index: number, rule: Rule): void;
  (e: 'removeRule', index: number): void;
  (e: 'addRule'): void;
}

export interface HeaderProps {}

export interface HeaderEmits {
  (e: 'export'): void;
  (e: 'import', event: Event): void;
}

export interface TabNavigationProps {
  activeTab: TabType;
}

export interface TabNavigationEmits {
  (e: 'update:activeTab', tab: TabType): void;
}

// API Service Types
export interface FieldConfig {
  field: string;
  type: 'number' | 'select';
  label: string;
  description: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    step?: number;
  };
}

export interface FormulaVariable {
  name: string;
  type: 'number' | 'string';
  description: string;
  category: string;
}

export interface DropdownOptions {
  loanTypes: string[];
  clientTypes: string[];
  employerTypes: string[];
  frequencies: string[];
  categories: string[];
  operators: {
    number: Array<{ value: string; label: string }>;
    select: Array<{ value: string; label: string }>;
  };
}

export interface CalculationType {
  type: string;
  label: string;
  description: string;
  fields: string[];
  tierValueTypes?: string[];
  validation?: {
    [key: string]: {
      min?: number;
      max?: number;
      step?: number;
    };
  };
}

export interface FormulaExample {
  title: string;
  formula: string;
  description: string;
}

// API Error Class
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public endpoint?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}
