<template>
  <v-container fluid class="reports-page">
    <v-row>
      <v-col cols="12">
        <div class="d-flex align-center justify-space-between mb-6">
          <div>
            <h1 class="text-h4 font-weight-bold text-primary">
              Reports & Analytics
            </h1>
            <p class="text-body-1 text-medium-emphasis mt-2">
              Comprehensive insights and performance analytics
            </p>
          </div>
          
          <v-btn
            color="primary"
            size="large"
            prepend-icon="mdi-download"
            class="text-none"
          >
            Export Report
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Report Cards -->
    <v-row class="mb-6">
      <v-col cols="12" md="6" lg="3">
        <v-card elevation="2" class="text-center pa-4">
          <v-icon size="48" color="primary" class="mb-3">
            mdi-chart-line
          </v-icon>
          <h3 class="text-h6 font-weight-bold">Performance</h3>
          <p class="text-body-2 text-medium-emphasis">Monthly trends</p>
          <v-btn variant="outlined" size="small" class="mt-2">
            View Details
          </v-btn>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="6" lg="3">
        <v-card elevation="2" class="text-center pa-4">
          <v-icon size="48" color="success" class="mb-3">
            mdi-currency-usd
          </v-icon>
          <h3 class="text-h6 font-weight-bold">Revenue</h3>
          <p class="text-body-2 text-medium-emphasis">Commission totals</p>
          <v-btn variant="outlined" size="small" class="mt-2">
            View Details
          </v-btn>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="6" lg="3">
        <v-card elevation="2" class="text-center pa-4">
          <v-icon size="48" color="info" class="mb-3">
            mdi-account-group
          </v-icon>
          <h3 class="text-h6 font-weight-bold">Clients</h3>
          <p class="text-body-2 text-medium-emphasis">Client analytics</p>
          <v-btn variant="outlined" size="small" class="mt-2">
            View Details
          </v-btn>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="6" lg="3">
        <v-card elevation="2" class="text-center pa-4">
          <v-icon size="48" color="warning" class="mb-3">
            mdi-clock-outline
          </v-icon>
          <h3 class="text-h6 font-weight-bold">Activity</h3>
          <p class="text-body-2 text-medium-emphasis">System usage</p>
          <v-btn variant="outlined" size="small" class="mt-2">
            View Details
          </v-btn>
        </v-card>
      </v-col>
    </v-row>

    <!-- Charts Section -->
    <v-row>
      <v-col cols="12" md="8">
        <CommissionTrendsChart :loading="chartsLoading" />
      </v-col>
      
      <v-col cols="12" md="4">
        <div class="charts-column">
          <!-- Commission Breakdown Donut Chart -->
          <DonutChart
            title="Commission Breakdown"
            subtitle="By category"
            :data="commissionBreakdown"
            center-label="Total"
            currency
            class="mb-4"
          />

          <!-- Top Performers Card -->
          <v-card elevation="2">
            <v-card-title>Top Performers</v-card-title>
            <v-card-text>
              <v-list>
                <v-list-item
                  v-for="(performer, index) in topPerformers"
                  :key="index"
                >
                  <template #prepend>
                    <v-avatar :color="getPerformerColor(index)" size="40">
                      <span class="text-white font-weight-bold">
                        {{ index + 1 }}
                      </span>
                    </v-avatar>
                  </template>

                  <v-list-item-title>{{ performer.name }}</v-list-item-title>
                  <v-list-item-subtitle>
                    ${{ performer.amount.toLocaleString() }}
                  </v-list-item-subtitle>
                </v-list-item>
              </v-list>
            </v-card-text>
          </v-card>
        </div>
      </v-col>
    </v-row>

    <!-- Recent Reports -->
    <v-row class="mt-6">
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title>Recent Reports</v-card-title>
          <v-card-text>
            <v-data-table
              :headers="reportHeaders"
              :items="recentReports"
              :items-per-page="5"
              class="elevation-0"
            >
              <template #item.type="{ item }">
                <v-chip
                  :color="getReportTypeColor(item.type)"
                  size="small"
                  variant="tonal"
                >
                  {{ item.type }}
                </v-chip>
              </template>
              
              <template #item.actions="{ item }">
                <v-btn
                  icon="mdi-download"
                  size="small"
                  variant="text"
                  @click="downloadReport(item)"
                />
                <v-btn
                  icon="mdi-eye"
                  size="small"
                  variant="text"
                  @click="viewReport(item)"
                />
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CommissionTrendsChart from '@/components/charts/CommissionTrendsChart.vue'
import DonutChart from '@/components/charts/DonutChart.vue'

// Loading states
const chartsLoading = ref(false)

// Data
const topPerformers = ref([
  { name: 'Sarah Johnson', amount: 15420 },
  { name: 'Mike Chen', amount: 12350 },
  { name: 'Emily Davis', amount: 9870 },
  { name: 'Alex Rodriguez', amount: 8650 }
])

const commissionBreakdown = ref([
  { label: 'Sales Commission', value: 45000, color: 'rgb(25, 118, 210)' },
  { label: 'Referral Bonus', value: 18000, color: 'rgb(255, 107, 53)' },
  { label: 'Performance Bonus', value: 12000, color: 'rgb(76, 175, 80)' },
  { label: 'Team Lead Bonus', value: 8000, color: 'rgb(156, 39, 176)' },
  { label: 'Other', value: 5000, color: 'rgb(255, 193, 7)' }
])

const reportHeaders = ref([
  { title: 'Name', key: 'name', sortable: true },
  { title: 'Type', key: 'type', sortable: true },
  { title: 'Generated', key: 'date', sortable: true },
  { title: 'Size', key: 'size', sortable: false },
  { title: 'Actions', key: 'actions', sortable: false }
])

const recentReports = ref([
  {
    name: 'Monthly Commission Report',
    type: 'Commission',
    date: '2024-01-15',
    size: '2.4 MB'
  },
  {
    name: 'Client Performance Analysis',
    type: 'Analytics',
    date: '2024-01-14',
    size: '1.8 MB'
  },
  {
    name: 'Revenue Summary Q1',
    type: 'Revenue',
    date: '2024-01-13',
    size: '3.2 MB'
  }
])

// Methods
const getPerformerColor = (index: number) => {
  const colors = ['primary', 'success', 'info', 'warning']
  return colors[index] || 'grey'
}

const getReportTypeColor = (type: string) => {
  switch (type) {
    case 'Commission': return 'primary'
    case 'Analytics': return 'info'
    case 'Revenue': return 'success'
    default: return 'grey'
  }
}

const downloadReport = (item: any) => {
  console.log('Download report:', item)
}

const viewReport = (item: any) => {
  console.log('View report:', item)
}
</script>

<style scoped>
.reports-page {
  background-color: rgb(var(--v-theme-surface));
  min-height: 100vh;
}

.charts-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}


</style>
