<template>
    <v-container fluid>
        <ThePageHeader title="Tenants Management" subtitle="Manage and monitor tenant organizations">
            <template #default>
                <v-spacer></v-spacer>
                <v-btn color="primary" prepend-icon="mdi-plus" @click="showCreateTenantDialog = true">
                    Create Tenant
                </v-btn>
            </template>
        </ThePageHeader>

        <TenantFilters @filter="handleFilter" @reset="resetFilters" />

        <TenantsTable 
            :tenants="tenants" 
            :loading="isLoading" 
            :current-page="currentPage" 
            :items-per-page="filters.limit!"
            :total-items="totalItems" 
            @update:current-page="handlePageChange" 
            @update:options="handleTableUpdate" 
            @view="viewTenant"
            @edit="editTenant" 
            @delete="deleteTenant"
        />

        <CreateTenantDialog 
            v-model="showCreateTenantDialog" 
            :loading="isCreatingTenant" 
            @submit="handleCreateTenant" 
        />

        <CreateTenantDialog 
            v-model="showEditTenantDialog" 
            :loading="isUpdatingTenant" 
            :tenant="selectedTenant"
            @submit="handleUpdateTenant" 
        />

        <v-dialog v-model="showDeleteDialog" max-width="500">
            <v-card>
                <v-card-title class="text-h5">Delete Tenant</v-card-title>
                <v-card-text>
                    Are you sure you want to delete tenant "{{ selectedTenant?.name }}"? This action cannot be undone.
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn variant="text" @click="showDeleteDialog = false">Cancel</v-btn>
                    <v-btn color="error" :loading="isDeletingTenant" @click="confirmDeleteTenant">Delete</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useTenantStore } from '@/stores/tenant'
import ThePageHeader from '@/components/ThePageHeader.vue'
import TenantFilters from '@/components/tenants/TenantFilters.vue'
import TenantsTable from '@/components/tenants/TenantsTable.vue'
import CreateTenantDialog from '@/components/tenants/CreateTenantDialog.vue'
import type { CreateTenantDto, UpdateTenantDto, Tenant, TenantQuery } from '@/types/tenant'

const router = useRouter()
const tenantStore = useTenantStore()
const { tenants, totalItems, currentPage, filters, isLoading } = storeToRefs(tenantStore)

const showCreateTenantDialog = ref(false)
const showEditTenantDialog = ref(false)
const showDeleteDialog = ref(false)
const isCreatingTenant = ref(false)
const isUpdatingTenant = ref(false)
const isDeletingTenant = ref(false)
const selectedTenant = ref<Tenant | null>(null)

const handleFilter = (filterData: TenantQuery) => {
    tenantStore.fetchTenants(filterData)
}

const resetFilters = () => {
    tenantStore.resetFilters()
    tenantStore.fetchTenants()
}

const handlePageChange = (page: number) => {
    tenantStore.fetchTenants({ ...filters.value, page })
}

const handleTableUpdate = (options: any) => {
    const { page, itemsPerPage, sortBy } = options
    const query: TenantQuery = {
        ...filters.value,
        page,
        limit: itemsPerPage
    }
    
    if (sortBy && sortBy.length > 0) {
        query.sortBy = sortBy[0].key
        query.sortOrder = sortBy[0].order
    }
    
    tenantStore.fetchTenants(query)
}

const viewTenant = (tenant: Tenant) => {
    router.push(`/tenants/${tenant.id}`)
}

const editTenant = (tenant: Tenant) => {
    selectedTenant.value = tenant
    showEditTenantDialog.value = true
}

const deleteTenant = (tenant: Tenant) => {
    selectedTenant.value = tenant
    showDeleteDialog.value = true
}

const handleCreateTenant = async (data: CreateTenantDto) => {
    try {
        isCreatingTenant.value = true
        await tenantStore.createTenant(data)
        showCreateTenantDialog.value = false
        // Refresh the list
        await tenantStore.fetchTenants()
    } catch (error) {
        console.error('Failed to create tenant:', error)
    } finally {
        isCreatingTenant.value = false
    }
}

const handleUpdateTenant = async (data: UpdateTenantDto) => {
    if (!selectedTenant.value) return
    
    try {
        isUpdatingTenant.value = true
        await tenantStore.updateTenant(selectedTenant.value.id, data)
        showEditTenantDialog.value = false
        selectedTenant.value = null
    } catch (error) {
        console.error('Failed to update tenant:', error)
    } finally {
        isUpdatingTenant.value = false
    }
}

const confirmDeleteTenant = async () => {
    if (!selectedTenant.value) return
    
    try {
        isDeletingTenant.value = true
        await tenantStore.deleteTenant(selectedTenant.value.id)
        showDeleteDialog.value = false
        selectedTenant.value = null
    } catch (error) {
        console.error('Failed to delete tenant:', error)
    } finally {
        isDeletingTenant.value = false
    }
}

onMounted(() => {
    tenantStore.fetchTenants()
})
</script>
