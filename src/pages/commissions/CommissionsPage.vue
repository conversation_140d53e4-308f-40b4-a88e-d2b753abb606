<template>
  <v-container fluid class="commissions-page">
    <v-row>
      <v-col cols="12">
        <div class="d-flex align-center justify-space-between mb-6">
          <div>
            <h1 class="text-h4 font-weight-bold text-primary">
              Commission Management
            </h1>
            <p class="text-body-1 text-medium-emphasis mt-2">
              Calculate, track, and manage commission payments
            </p>
          </div>
          
          <v-btn
            color="primary"
            size="large"
            prepend-icon="mdi-calculator-variant"
            class="text-none"
          >
            New Calculation
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Quick Stats -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card elevation="2">
          <v-card-text class="text-center">
            <v-icon size="48" color="success" class="mb-2">
              mdi-currency-usd
            </v-icon>
            <h3 class="text-h5 font-weight-bold text-success">$45,230</h3>
            <p class="text-body-2 text-medium-emphasis">This Month</p>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card elevation="2">
          <v-card-text class="text-center">
            <v-icon size="48" color="primary" class="mb-2">
              mdi-calculator-variant
            </v-icon>
            <h3 class="text-h5 font-weight-bold text-primary">156</h3>
            <p class="text-body-2 text-medium-emphasis">Calculations</p>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card elevation="2">
          <v-card-text class="text-center">
            <v-icon size="48" color="warning" class="mb-2">
              mdi-clock-outline
            </v-icon>
            <h3 class="text-h5 font-weight-bold text-warning">23</h3>
            <p class="text-body-2 text-medium-emphasis">Pending</p>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" sm="6" md="3">
        <v-card elevation="2">
          <v-card-text class="text-center">
            <v-icon size="48" color="info" class="mb-2">
              mdi-check-circle
            </v-icon>
            <h3 class="text-h5 font-weight-bold text-info">89</h3>
            <p class="text-body-2 text-medium-emphasis">Completed</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Commission Table -->
    <v-row>
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title class="d-flex align-center justify-space-between">
            <span>Recent Commissions</span>
            <v-btn variant="text" size="small">
              View All
            </v-btn>
          </v-card-title>
          
          <v-card-text>
            <v-data-table
              :headers="headers"
              :items="commissions"
              :items-per-page="10"
              class="elevation-0"
            >
              <template #item.status="{ item }">
                <v-chip
                  :color="getStatusColor(item.status)"
                  size="small"
                  variant="tonal"
                >
                  {{ item.status }}
                </v-chip>
              </template>
              
              <template #item.amount="{ item }">
                <span class="font-weight-bold text-success">
                  ${{ item.amount.toLocaleString() }}
                </span>
              </template>
              
              <template #item.actions="{ item }">
                <v-btn
                  icon="mdi-eye"
                  size="small"
                  variant="text"
                  @click="viewCommission(item)"
                />
                <v-btn
                  icon="mdi-pencil"
                  size="small"
                  variant="text"
                  @click="editCommission(item)"
                />
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Data
const headers = ref([
  { title: 'ID', key: 'id', sortable: true },
  { title: 'Client', key: 'client', sortable: true },
  { title: 'Amount', key: 'amount', sortable: true },
  { title: 'Status', key: 'status', sortable: true },
  { title: 'Date', key: 'date', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false }
])

const commissions = ref([
  {
    id: 'COM-001',
    client: 'TechCorp Solutions',
    amount: 5420,
    status: 'Completed',
    date: '2024-01-15'
  },
  {
    id: 'COM-002',
    client: 'Global Industries',
    amount: 3280,
    status: 'Pending',
    date: '2024-01-14'
  },
  {
    id: 'COM-003',
    client: 'StartupXYZ',
    amount: 1950,
    status: 'Processing',
    date: '2024-01-13'
  },
  {
    id: 'COM-004',
    client: 'Enterprise Ltd',
    amount: 7650,
    status: 'Completed',
    date: '2024-01-12'
  }
])

// Methods
const getStatusColor = (status: string) => {
  switch (status) {
    case 'Completed': return 'success'
    case 'Pending': return 'warning'
    case 'Processing': return 'info'
    default: return 'grey'
  }
}

const viewCommission = (item: any) => {
  console.log('View commission:', item)
}

const editCommission = (item: any) => {
  console.log('Edit commission:', item)
}
</script>

<style scoped>
.commissions-page {
  background-color: rgb(var(--v-theme-surface));
  min-height: 100vh;
}
</style>
