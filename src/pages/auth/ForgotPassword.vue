<template>
  <v-container fluid class="auth-container">
    <v-card class="forgot-password-form mx-auto" max-width="400">
      <v-card-title class="text-center pa-6">
        <div>
          <h1 class="text-h4 font-weight-bold mb-2">Forgot Password</h1>
          <p class="text-body-1 text-medium-emphasis">
            Enter your email address and we'll send you a link to reset your
            password
          </p>
        </div>
      </v-card-title>
      <v-card-text class="pa-6">
        <v-form ref="form" v-model="isFormValid" @submit.prevent="onFinish">
          <v-text-field
            v-model="formState.email"
            label="Email Address"
            type="email"
            :rules="emailRules"
            variant="outlined"
            density="comfortable"
            prepend-inner-icon="mdi-email"
            required
            class="mb-4"
          />
          <v-btn
            block
            color="primary"
            size="large"
            type="submit"
            :loading="loading"
            :disabled="!isFormValid"
            class="mb-4"
          >
            Send Reset Link
          </v-btn>
          <div class="text-center">
            <v-btn
              variant="text"
              color="primary"
              @click="$router.push('/auth/login')"
            >
              Back to Login
            </v-btn>
          </div>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useAppStore } from "@/stores/app";
import { useRouter } from "vue-router";
const router = useRouter();
const authStore = useAuthStore();
const appStore = useAppStore();
const loading = computed(() => appStore.loading);
const form = ref<any>(null);
const isFormValid = ref(false);
const formState = reactive({
  email: "",
});
const emailRules = [
  (v: string) => !!v || "Email is required",
  (v: string) => /.+@.+\..+/.test(v) || "Email must be valid",
];
const onFinish = async () => {
  const { valid } = await form.value?.validate();
  if (!valid) return;
  try {
    const response = await authStore.forgotPassword(formState.email);
    // Show success message - you can implement a snackbar here
    // Optionally redirect to login with a success message
    router.push("/auth/login");
  } catch (error: any) {
    // Show error message - you can implement a snackbar here
  }
};
</script>
<style scoped>
.auth-container {
  min-height: calc(100vh - 64px); /* Subtract app bar height */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(var(--v-theme-background));
  padding: 1.5rem;
}
.forgot-password-form {
  width: 100%;
  max-width: 400px;
  background-color: white;
}
/* Responsive adjustments */
@media (max-width: 600px) {
  .auth-container {
    padding: 1rem;
  }
  .forgot-password-form {
    max-width: 100%;
  }
}
</style>
