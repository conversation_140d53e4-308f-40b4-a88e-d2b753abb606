<template>
    <v-container fluid class="auth-container">
        <v-card class="reset-password-form mx-auto" max-width="400">
            <v-card-title class="text-center pa-6">
                <div>
                    <h1 class="text-h4 font-weight-bold mb-2">Reset Password</h1>
                    <p class="text-body-1 text-medium-emphasis">
                        Enter your new password below
                    </p>
                </div>
            </v-card-title>

            <v-card-text class="pa-6">
                <v-form ref="form" v-model="isFormValid" @submit.prevent="onFinish">
                    <v-text-field
                        v-model="formState.password"
                        label="New Password"
                        :type="showPassword ? 'text' : 'password'"
                        :rules="passwordRules"
                        variant="outlined"
                        density="comfortable"
                        prepend-inner-icon="mdi-lock"
                        :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                        @click:append-inner="showPassword = !showPassword"
                        required
                        class="mb-4"
                    />

                    <v-text-field
                        v-model="formState.confirmPassword"
                        label="Confirm Password"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        :rules="confirmPasswordRules"
                        variant="outlined"
                        density="comfortable"
                        prepend-inner-icon="mdi-lock-check"
                        :append-inner-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                        @click:append-inner="showConfirmPassword = !showConfirmPassword"
                        required
                        class="mb-4"
                    />

                    <v-btn
                        block
                        color="primary"
                        size="large"
                        type="submit"
                        :loading="loading"
                        :disabled="!isFormValid"
                        class="mb-4"
                    >
                        Reset Password
                    </v-btn>

                    <div class="text-center">
                        <v-btn
                            variant="text"
                            color="primary"
                            @click="$router.push('/auth/login')"
                        >
                            Back to Login
                        </v-btn>
                    </div>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()
const loading = computed(() => appStore.loading)

const form = ref<any>(null)
const isFormValid = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)

const formState = reactive({
    password: '',
    confirmPassword: ''
})

const passwordRules = [
    (v: string) => !!v || 'Password is required',
    (v: string) => (v && v.length >= 8) || 'Password must be at least 8 characters',
    (v: string) => /(?=.*[a-z])/.test(v) || 'Password must contain at least one lowercase letter',
    (v: string) => /(?=.*[A-Z])/.test(v) || 'Password must contain at least one uppercase letter',
    (v: string) => /(?=.*\d)/.test(v) || 'Password must contain at least one number'
]

const confirmPasswordRules = [
    (v: string) => !!v || 'Please confirm your password',
    (v: string) => v === formState.password || 'Passwords do not match'
]

const onFinish = async () => {
    const { valid } = await form.value?.validate()

    if (!valid) return

    try {
        const token = (route.query.token as string) || (route.params as any).token as string
        if (!token) {
            console.error('No reset token found')
            return
        }

        const response = await authStore.resetPassword(
            token,
            formState.password,
            formState.confirmPassword
        )

        // Show success message - you can implement a snackbar here
        console.log('Password reset successfully:', response.message)
        router.push('/auth/login')
    } catch (error: any) {
        // Show error message - you can implement a snackbar here
        console.error('Failed to reset password:', error.response?.data?.message || 'Failed to reset password')
    }
}
</script>

<style scoped>
.auth-container {
    min-height: calc(100vh - 64px); /* Subtract app bar height */
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgb(var(--v-theme-background));
    padding: 1.5rem;
}

.reset-password-form {
    width: 100%;
    max-width: 400px;
    background-color: white;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .auth-container {
        padding: 1rem;
    }

    .reset-password-form {
        max-width: 100%;
    }
}
</style>
