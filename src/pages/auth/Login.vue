<template>
  <div class="login-container">
    <div class="login-background">
      <div class="background-pattern"></div>
      <div class="background-gradient"></div>
    </div>

    <v-container
      class="d-flex align-center justify-center"
      style="height: 100vh; position: relative; z-index: 2"
    >
      <v-card
        class="login-card card-modern"
        elevation="0"
        max-width="480"
        width="100%"
      >
        <v-card-text class="pa-8">
          <div class="text-center mb-8">
            <div class="logo-container mb-6">
              <v-icon size="64" color="primary" class="mb-4"
                >mdi-calculator-variant</v-icon
              >
              <h1 class="text-h4 font-weight-bold text-gradient mb-2">
                Smart Commissions
              </h1>
              <p class="text-body-1 text-medium-emphasis">
                Commission Calculator
              </p>
            </div>
            <div class="welcome-text">
              <h2 class="text-h5 font-weight-medium mb-2">Welcome Back</h2>
              <p class="text-body-2 text-medium-emphasis">
                Sign in to access your commission dashboard
              </p>
            </div>
          </div>
          <div class="form-content">
            <v-form ref="form" v-model="isFormValid" @submit.prevent="onFinish">
              <v-text-field
                v-model="formState.email"
                label="Email Address"
                variant="outlined"
                :rules="[
                  (v) => !!v || 'Email is required',
                  (v) => /.+@.+\..+/.test(v) || 'Email must be valid',
                ]"
                density="comfortable"
                class="mb-4 modern-input"
                hide-details="auto"
                prepend-inner-icon="mdi-email-outline"
                color="primary"
              />

              <v-text-field
                v-model="formState.password"
                label="Password"
                :type="showPassword ? 'text' : 'password'"
                variant="outlined"
                :rules="[(v) => !!v || 'Password is required']"
                density="comfortable"
                class="mb-6 modern-input"
                hide-details="auto"
                prepend-inner-icon="mdi-lock-outline"
                :append-inner-icon="
                  showPassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'
                "
                @click:append-inner="showPassword = !showPassword"
                color="primary"
              />

              <div class="d-flex align-center justify-space-between mb-6">
                <v-checkbox
                  v-model="formState.rememberDevice"
                  label="Remember this Device"
                  density="comfortable"
                  hide-details
                  color="primary"
                />
                <v-btn
                  variant="text"
                  color="primary"
                  density="comfortable"
                  class="text-none font-weight-medium"
                  @click="onForgotPassword"
                >
                  Forgot Password?
                </v-btn>
              </div>

              <v-btn
                block
                color="primary"
                size="x-large"
                type="submit"
                :loading="loading"
                class="mb-6 btn-modern text-none font-weight-medium"
                style="height: 56px"
              >
                <v-icon start>mdi-login</v-icon>
                Sign In
              </v-btn>

              <div class="text-center">
                <p class="text-body-2 text-medium-emphasis mb-4">
                  Don't have an account?
                  <v-btn
                    variant="text"
                    color="primary"
                    class="text-none font-weight-medium pa-0"
                    style="text-decoration: none"
                  >
                    Contact Administrator
                  </v-btn>
                </p>
              </div>
            </v-form>
          </div>
        </v-card-text>
      </v-card>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useAppStore } from "@/stores/app";
import { useRouter } from "vue-router";

const router = useRouter();
const authStore = useAuthStore();
const appStore = useAppStore();
const loading = computed(() => appStore.loading);

const form = ref<any>(null);
const isFormValid = ref(false);
const showPassword = ref(false);

const formState = reactive({
  email: "",
  password: "",
  rememberDevice: false,
});

const onForgotPassword = () => {
  router.push("/auth/forgot-password");
};

const onFinish = async () => {
  const { valid } = await form.value?.validate();
  if (!valid) return;
  try {
    const response = await authStore.login(formState.email, formState.password);

    if (response.requiresOtp) {
      router.push("/auth/verify-otp");
      // snackbar.showSuccess(response.message)
    } else {
      router.push("/dashboard");
      // snackbar.showSuccess('Login successful')
    }
  } catch (error: any) {
    // snackbar.showError(error.response?.data?.message || 'Login failed')
  }
};
</script>

<style scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.1;
}

.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 50%,
    rgba(25, 118, 210, 0.05) 100%
  );
}

.login-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.logo-container {
  position: relative;
}

.logo-container::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  border-radius: 2px;
}

.welcome-text {
  padding: 1rem 0;
}

.modern-input {
  margin-bottom: 1.5rem;
}

.modern-input :deep(.v-field) {
  border-radius: 12px;
  transition: all 0.2s ease;
}

.modern-input :deep(.v-field:hover) {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}

.modern-input :deep(.v-field--focused) {
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.25);
}

.btn-modern {
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3) !important;
  transition: all 0.2s ease !important;
}

.btn-modern:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(25, 118, 210, 0.4) !important;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .login-card {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .logo-container h1 {
    font-size: 1.75rem !important;
  }
}

@media (max-width: 400px) {
  .login-card {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
  }

  .login-card :deep(.pa-8) {
    padding: 1.5rem !important;
  }
}

/* Animation for the login card */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card {
  animation: slideInUp 0.6s ease-out;
}

/* Floating animation for the icon */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.logo-container .v-icon {
  animation: float 3s ease-in-out infinite;
}
</style>
