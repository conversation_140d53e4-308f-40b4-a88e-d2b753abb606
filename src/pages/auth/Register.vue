<template>
  <div class="auth-container">
    <div class="register-form">
      <div class="form-content">
        <h1 class="title">Create Account</h1>
        <p class="subtitle">
          Sign up today and unlock a world of possibilities.
        </p>
        <a-steps :current="currentStep" class="mb-8">
          <a-step title="Basic Info" />
          <a-step title="Personal Details" />
          <a-step title="Verification" />
        </a-steps>
        <!-- Step 1: Basic Information -->
        <div v-if="currentStep === 0">
          <a-form :model="formState" layout="vertical">
            <a-form-item
              label="Email"
              name="email"
              :rules="[
                {
                  required: true,
                  type: 'email',
                  message: 'Please input a valid email!',
                },
              ]"
            >
              <a-input
                v-model:value="formState.email"
                placeholder="Enter email"
              />
            </a-form-item>
            <a-form-item
              label="Password"
              name="password"
              :rules="[
                { required: true, message: 'Please input your password!' },
              ]"
            >
              <a-input-password
                v-model:value="formState.password"
                placeholder="Create a password"
              />
            </a-form-item>
            <a-form-item
              label="Confirm Password"
              name="confirmPassword"
              :rules="[
                { required: true, message: 'Please confirm your password!' },
                { validator: validateConfirmPassword },
              ]"
            >
              <a-input-password
                v-model:value="formState.confirmPassword"
                placeholder="Confirm password"
              />
            </a-form-item>
          </a-form>
        </div>
        <!-- Step 2: Personal Details -->
        <div v-if="currentStep === 1">
          <a-form
            :model="formState"
            layout="vertical"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-row :gutter="24">
              <a-col :span="8">
                <a-form-item
                  label="First Name"
                  name="firstName"
                  :rules="[
                    {
                      required: true,
                      message: 'Please input your first name!',
                    },
                  ]"
                >
                  <a-input v-model:value="formState.firstName" size="large" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="Middle Name" name="middleName">
                  <a-input
                    v-model:value="formState.middleName"
                    placeholder="Enter middle name"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="Last Name"
                  name="lastName"
                  :rules="[
                    { required: true, message: 'Please input your last name!' },
                  ]"
                >
                  <a-input
                    v-model:value="formState.lastName"
                    placeholder="Enter last name"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item
                  label="Phone Number"
                  name="mobilePhone"
                  :rules="[
                    {
                      required: true,
                      message: 'Please input your phone number!',
                    },
                    { validator: validatePhoneNumber },
                  ]"
                >
                  <a-input-group compact>
                    <a-select
                      v-model:value="formState.countryCode"
                      style="width: 100px"
                      size="large"
                    >
                      <a-select-option value="+254">+254</a-select-option>
                      <a-select-option value="+255">+255</a-select-option>
                      <a-select-option value="+256">+256</a-select-option>
                      <a-select-option value="+257">+257</a-select-option>
                    </a-select>
                    <a-input
                      v-model:value="formState.phoneNumber"
                      style="width: calc(100% - 100px)"
                      placeholder="712345678"
                      size="large"
                    />
                  </a-input-group>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="Birth Date"
                  name="birthDate"
                  :rules="[
                    {
                      required: true,
                      message: 'Please select your birth date!',
                    },
                  ]"
                >
                  <a-date-picker
                    v-model:value="formState.birthDate"
                    class="w-full"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="Marital Status" name="maritalStatus">
                  <a-select
                    v-model:value="formState.maritalStatus"
                    placeholder="Select marital status"
                  >
                    <a-select-option value="single">Single</a-select-option>
                    <a-select-option value="married">Married</a-select-option>
                    <a-select-option value="divorced">Divorced</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Residence" name="residence">
                  <a-input
                    v-model:value="formState.residence"
                    placeholder="Enter residence"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="County" name="county">
                  <a-input v-model:value="formState.county" size="large" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Sub County" name="subCounty">
                  <a-input
                    v-model:value="formState.subCounty"
                    placeholder="Enter sub county"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item label="Ward" name="ward">
                  <a-input
                    v-model:value="formState.ward"
                    placeholder="Enter ward"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item class="mb-2">
              <a-checkbox v-model:checked="formState.acceptTerms">
                I agree to the
                <a href="#" class="text-primary">Terms & Conditions</a>
              </a-checkbox>
            </a-form-item>
          </a-form>
        </div>
        <!-- Step 3: Verification -->
        <div v-if="currentStep === 2">
          <div class="text-center">
            <check-circle-outlined class="text-success text-5xl mb-4" />
            <h2 class="text-xl font-semibold mb-4">Verify Your Email</h2>
            <p class="text-gray-600 mb-4">
              We've sent a verification link to your email address. Please check
              your inbox and verify your account.
            </p>
          </div>
        </div>
        <div class="steps-action">
          <a-space>
            <a-button v-if="currentStep > 0" @click="prev" size="large">
              Previous
            </a-button>
            <a-button
              v-if="currentStep < 2"
              type="primary"
              @click="next"
              :loading="loading"
              size="large"
            >
              Next
            </a-button>
            <a-button
              v-if="currentStep === 2"
              type="primary"
              @click="finish"
              :loading="loading"
              size="large"
            >
              Finish
            </a-button>
          </a-space>
        </div>
        <div class="text-center mt-8">
          Already have an account?
          <router-link to="/auth/login" class="text-primary"
            >Log in</router-link
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useAppStore } from "@/stores/app";
import { message } from "ant-design-vue";
import { useRouter } from "vue-router";
import { CheckCircleOutlined } from "@ant-design/icons-vue";
import { EntityType, UserType } from "@/types/user";
import { authApi } from "@/services/auth.api";
const router = useRouter();
const authStore = useAuthStore();
const appStore = useAppStore();
const loading = computed(() => appStore.loading);
const currentStep = ref(2);
const formState = reactive({
  firstName: "",
  lastName: "",
  middleName: "",
  email: "",
  password: "",
  confirmPassword: "",
  countryCode: "+254",
  phoneNumber: "",
  birthDate: "",
  maritalStatus: undefined as "single" | "married" | "divorced" | undefined,
  userState: "Active",
  residence: "",
  county: "",
  subCounty: "",
  ward: "",
  isAdministrator: false,
  isSupport: false,
  acceptTerms: false,
  entityType: "USER" as EntityType,
  userType: "PROFESSIONAL_SERVICE_PROVIDER" as UserType,
});
const validateConfirmPassword = async (_rule: any, value: string) => {
  if (value !== formState.password) {
    throw new Error("The two passwords do not match!");
  }
};
const validatePhoneNumber = async (_rule: any, value: string) => {
  const phoneRegex = /^\d{9}$/; // Matches exactly 9 digits
  if (!formState.phoneNumber) {
    throw new Error("Please input your phone number!");
  }
  if (!phoneRegex.test(formState.phoneNumber)) {
    throw new Error("Please enter a valid 9-digit phone number!");
  }
};
const getFullPhoneNumber = computed(() => {
  if (!formState.phoneNumber) return "";
  return `${formState.countryCode}${formState.phoneNumber}`;
});
const validateStep = () => {
  if (currentStep.value === 0) {
    if (!formState.email || !formState.password || !formState.confirmPassword) {
      message.error("Please fill in all required fields");
      return false;
    }
  } else if (currentStep.value === 1) {
    if (!formState.firstName || !formState.lastName || !formState.phoneNumber) {
      message.error("Please fill in all required fields");
      return false;
    }
    if (!formState.acceptTerms) {
      message.error("Please accept the Terms & Conditions");
      return false;
    }
  }
  return true;
};
const next = async () => {
  if (!validateStep()) return;
  if (currentStep.value === 1) {
    try {
      await authApi.signup({
        ...formState,
        birthDate: new Date(formState.birthDate).toISOString(),
        isAdministrator: false,
        isSupport: false,
        userState: "Active",
        mobilePhone: getFullPhoneNumber.value,
      });
      currentStep.value++;
    } catch (error: any) {
      message.error(error.response?.data?.message || "Registration failed");
    }
  } else {
    currentStep.value++;
  }
};
const prev = () => {
  currentStep.value--;
};
const finish = () => {
  router.push("/auth/login");
  message.success(
    "Registration completed! Please check your email to verify your account.",
  );
};
</script>
<style scoped>
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 1.5rem;
}
.register-form {
  width: 100%;
  max-width: 800px;
  padding: 2rem;
}
.form-content {
  width: 100%;
}
.title {
  font-size: 1.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1a1a1a;
}
.subtitle {
  color: #666;
  margin-bottom: 2rem;
}
.a-row {
  margin-bottom: 1.5rem;
}
.steps-action {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
}
:deep(.ant-input),
:deep(.ant-input-password),
:deep(.ant-select),
:deep(.ant-picker) {
  width: 100%;
  height: 42px;
  border-radius: 6px;
  background-color: #f5f5f5;
  border: 1px solid #e6e6e6;
}
:deep(.ant-form-item) {
  margin-bottom: 1.5rem;
}
:deep(.ant-steps) {
  max-width: 600px;
  margin: 0 auto 3rem;
}
:deep(.ant-checkbox-wrapper) {
  color: #666;
}
:deep(.ant-checkbox-wrapper a) {
  color: #1a1a1a;
  text-decoration: underline;
}
.text-success {
  color: #52c41a;
}
:deep(.ant-select-selector) {
  height: 42px !important;
  padding-top: 5px !important;
}
:deep(.ant-input-group-compact) {
  display: flex;
}
:deep(.ant-input-group-compact .ant-select) {
  border-right: none;
}
:deep(.ant-input-group-compact .ant-input) {
  border-left: none;
}
</style>
