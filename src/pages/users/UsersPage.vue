<template>
  <v-container fluid class="modern-page pa-6">
    <div class="page-header mb-8">
      <div class="d-flex align-center justify-space-between">
        <div class="header-text">
          <h1 class="text-h4 font-weight-bold text-primary mb-2">
            <v-icon size="32" class="mr-3" color="primary"
              >mdi-account-group</v-icon
            >
            User Management
          </h1>
          <p class="text-body-1 text-medium-emphasis">
            Manage system users and their permissions for commission
            calculations
          </p>
        </div>
        <div class="header-actions">
          <v-btn
            color="primary"
            size="large"
            @click="showCreateUserDialog = true"
            class="btn-modern"
            elevation="2"
          >
            <v-icon start>mdi-account-plus</v-icon>
            Create User
          </v-btn>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <v-card class="card-modern mb-6" elevation="0">
      <v-card-text class="pa-6">
        <UserFilters @filter="handleFilter" @reset="resetFilters" />
      </v-card-text>
    </v-card>

    <!-- Users Table Section -->
    <v-card class="card-modern" elevation="0">
      <v-card-title class="pa-6 pb-0">
        <div class="d-flex align-center justify-space-between w-100">
          <div>
            <h2 class="text-h6 font-weight-medium">Users List</h2>
            <p class="text-body-2 text-medium-emphasis mt-1">
              {{ totalItems }} total users
            </p>
          </div>
          <v-chip color="primary" variant="tonal" size="small">
            <v-icon start size="16">mdi-account-multiple</v-icon>
            {{ users.length }} loaded
          </v-chip>
        </div>
      </v-card-title>

      <v-card-text class="pa-0">
        <UsersTable
          :users="users"
          :loading="isLoading"
          :current-page="currentPage"
          :items-per-page="filters.limit || 10"
          :total-items="totalItems"
          :get-available-transitions="getAvailableTransitions"
          @update:current-page="handlePageChange"
          @update:options="handleTableUpdate"
          @view="viewUser"
          @edit="editUser"
          @state-change="handleStateTransition"
          @change-password="handleChangePassword"
          @change-email="handleChangeEmail"
          @delete="handleDeleteUser"
        />
      </v-card-text>
    </v-card>

    <StateChangeDialog
      v-model="usersStore.stateChangeConfirmation.show"
      :loading="usersStore.stateChangeConfirmation.loading"
      :title="usersStore.stateChangeConfirmation.title"
      :message="usersStore.stateChangeConfirmation.message"
      @confirm="usersStore.confirmStateChange"
      @cancel="usersStore.cancelStateChange"
    />

    <CreateUserDialog
      v-model="showCreateUserDialog"
      :loading="isCreatingUser"
      @submit="handleCreateUser"
    />

    <ChangePasswordDialog
      v-model="showChangePasswordDialog"
      :user="selectedUser"
      @success="handlePasswordChangeSuccess"
    />

    <ChangeEmailDialog
      v-model="showChangeEmailDialog"
      :user="selectedUser"
      @success="handleEmailChangeSuccess"
    />

    <EditUserDialog
      v-model="showEditUserDialog"
      :user="selectedUser"
      @success="handleEditUserSuccess"
    />

    <DeleteUserDialog
      :show="showDeleteUserDialog"
      :user="selectedUser"
      :loading="isDeletingUser"
      @confirm="confirmDeleteUser"
      @cancel="cancelDeleteUser"
    />
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { useUsersStore } from "@/stores/users";
import UserFilters from "@/components/users/UserFilters.vue";
import UsersTable from "@/components/users/UsersTable.vue";
import CreateUserDialog from "@/components/users/CreateUserDialog.vue";
import StateChangeDialog from "@/components/users/StateChangeDialog.vue";
import ChangePasswordDialog from "@/components/users/ChangePasswordDialog.vue";
import ChangeEmailDialog from "@/components/users/ChangeEmailDialog.vue";
import DeleteUserDialog from "@/components/users/DeleteUserDialog.vue";
import EditUserDialog from "@/components/users/EditUserDialog.vue";
import type { CreateUserDto } from "@/types/user.dto";
import type { User } from "@/types/user";

const router = useRouter();
const usersStore = useUsersStore();
const { users, totalItems, currentPage, filters, isLoading } =
  storeToRefs(usersStore);

// Initialize users on component mount
onMounted(() => {
  usersStore.fetchUsers();
});

const showCreateUserDialog = ref(false);
const isCreatingUser = ref(false);
const showChangePasswordDialog = ref(false);
const showChangeEmailDialog = ref(false);
const showEditUserDialog = ref(false);
const showDeleteUserDialog = ref(false);
const selectedUser = ref<User | null>(null);
const isDeletingUser = ref(false);

const handleFilter = (filterData) => {
  usersStore.fetchUsers(filterData);
};

const resetFilters = () => {
  usersStore.setFilters({});
  usersStore.fetchUsers();
};

const handleTableUpdate = (options: any) => {
  const { page, itemsPerPage } = options;
  usersStore.setFilters({ page, limit: itemsPerPage });
  usersStore.fetchUsers();
};

const viewUser = (user) => {
  router.push(`/users/${user.id}`);
};

const editUser = (user: User) => {
  selectedUser.value = user;
  showEditUserDialog.value = true;
};

const handleCreateUser = async (userData: CreateUserDto) => {
  try {
    isCreatingUser.value = true;
    await usersStore.createUser(userData);
    showCreateUserDialog.value = false;
    usersStore.fetchUsers();
  } catch (error) {
    console.error("Error creating user:", error);
  } finally {
    isCreatingUser.value = false;
  }
};

const getAvailableTransitions = (currentState) => {
  return usersStore.getAvailableTransitions(currentState);
};

const handleStateTransition = ({ userId, newState, currentState }) => {
  usersStore.showStateChangeConfirmation(userId, newState, currentState);
};

const handlePageChange = (page: number) => {
  usersStore.setFilters({ ...filters.value, page });
  usersStore.fetchUsers();
};

const handleChangePassword = (user: User) => {
  selectedUser.value = user;
  showChangePasswordDialog.value = true;
};

const handleChangeEmail = (user: User) => {
  selectedUser.value = user;
  showChangeEmailDialog.value = true;
};

const handleDeleteUser = (user: User) => {
  selectedUser.value = user;
  showDeleteUserDialog.value = true;
};

const confirmDeleteUser = async () => {
  if (!selectedUser.value) return;

  try {
    isDeletingUser.value = true;
    await usersStore.deleteUser(selectedUser.value.id);
    showDeleteUserDialog.value = false;
    selectedUser.value = null;
    usersStore.fetchUsers();
  } catch (error) {
    console.error("Error deleting user:", error);
  } finally {
    isDeletingUser.value = false;
  }
};

const cancelDeleteUser = () => {
  showDeleteUserDialog.value = false;
  selectedUser.value = null;
};

const handlePasswordChangeSuccess = () => {
  usersStore.fetchUsers();
};

const handleEmailChangeSuccess = () => {
  usersStore.fetchUsers();
};

const handleEditUserSuccess = () => {
  usersStore.fetchUsers();
};
</script>

<style scoped>
.modern-page {
  background: #ffffff;
  min-height: calc(100vh - 64px);
}

.page-header {
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%
  );
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.header-text h1 {
  background: linear-gradient(
    135deg,
    var(--v-theme-primary),
    var(--v-theme-accent)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions .btn-modern {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  transition: all 0.2s ease;
}

.header-actions .btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(25, 118, 210, 0.4);
}

.card-modern {
  border-radius: 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.3s ease !important;
}

.card-modern:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
}

/* Responsive design */
@media (max-width: 960px) {
  .page-header .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }

  .header-actions .btn-modern {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .modern-page {
    padding: 1rem !important;
  }

  .page-header {
    padding: 1.5rem !important;
  }

  .header-text h1 {
    font-size: 1.75rem !important;
  }
}
</style>
