<template>
  <v-container fluid class="user-detail-page">
    <ThePageHeader
      :title="`User Details - ${user?.fullName || 'Loading...'}`"
      subtitle="View and manage user information"
    >
      <template #default>
        <v-btn
          variant="outlined"
          prepend-icon="mdi-arrow-left"
          @click="goBack"
          class="mr-3"
        >
          Back to Users
        </v-btn>
        <v-btn
          color="primary"
          prepend-icon="mdi-pencil"
          @click="showEditDialog = true"
          :disabled="!user"
        >
          Edit User
        </v-btn>
      </template>
    </ThePageHeader>

    <div v-if="loading" class="text-center py-8">
      <v-progress-circular indeterminate size="64" color="primary" />
      <p class="text-body-1 text-medium-emphasis mt-4">
        Loading user details...
      </p>
    </div>

    <div v-else-if="error" class="text-center py-8">
      <v-icon size="64" color="error" class="mb-4">mdi-alert-circle</v-icon>
      <h3 class="text-h6 text-error mb-2">Error Loading User</h3>
      <p class="text-body-2 text-medium-emphasis">{{ error }}</p>
      <v-btn color="primary" @click="fetchUser" class="mt-4">Try Again</v-btn>
    </div>

    <v-row v-else-if="user">
      <!-- User Information Card -->
      <v-col cols="12" md="8">
        <v-card class="user-info-card" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-avatar size="48" color="primary" class="mr-4">
              <v-icon color="white" size="24">mdi-account</v-icon>
            </v-avatar>
            <div>
              <h3 class="text-h6">{{ user.fullName }}</h3>
              <p class="text-body-2 text-medium-emphasis">
                {{ user.email || "No email provided" }}
              </p>
            </div>
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <div class="info-item">
                  <div class="info-label">Full Name</div>
                  <div class="info-value">{{ user.fullName }}</div>
                </div>
              </v-col>
              <v-col cols="12" md="6">
                <div class="info-item">
                  <div class="info-label">Email Address</div>
                  <div class="info-value">
                    {{ user.email || "Not provided" }}
                  </div>
                </div>
              </v-col>
              <v-col cols="12" md="6">
                <div class="info-item">
                  <div class="info-label">Tenant ID</div>
                  <div class="info-value">{{ user.tenantId }}</div>
                </div>
              </v-col>
              <v-col cols="12" md="6">
                <div class="info-item">
                  <div class="info-label">User ID</div>
                  <div class="info-value">{{ user.id }}</div>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Status and Actions Card -->
      <v-col cols="12" md="4">
        <v-card class="status-card" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon color="success" class="mr-3">mdi-shield-check</v-icon>
            <div>
              <h3 class="text-h6">Account Status</h3>
              <p class="text-body-2 text-medium-emphasis">
                Current status information
              </p>
            </div>
          </v-card-title>

          <v-card-text>
            <div class="status-items">
              <div class="status-item">
                <div class="status-label">Account Status</div>
                <v-chip
                  :color="user.isActive ? 'success' : 'error'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user.isActive ? "mdi-check-circle" : "mdi-close-circle"
                    }}
                  </v-icon>
                  {{ user.isActive ? "Active" : "Inactive" }}
                </v-chip>
              </div>

              <div class="status-item">
                <div class="status-label">Email Verification</div>
                <v-chip
                  :color="user.emailVerified ? 'info' : 'warning'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user.emailVerified ? "mdi-email-check" : "mdi-email-alert"
                    }}
                  </v-icon>
                  {{ user.emailVerified ? "Verified" : "Pending" }}
                </v-chip>
              </div>

              <div class="status-item">
                <div class="status-label">Account Verification</div>
                <v-chip
                  :color="user.verified ? 'success' : 'warning'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user.verified ? "mdi-check-decagram" : "mdi-clock-alert"
                    }}
                  </v-icon>
                  {{ user.verified ? "Verified" : "Pending" }}
                </v-chip>
              </div>

              <div class="status-item">
                <div class="status-label">Administrator</div>
                <v-chip
                  :color="user.isAdministrator ? 'primary' : 'default'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user.isAdministrator ? "mdi-shield-crown" : "mdi-account"
                    }}
                  </v-icon>
                  {{ user.isAdministrator ? "Yes" : "No" }}
                </v-chip>
              </div>
            </div>
          </v-card-text>
        </v-card>

        <!-- Quick Actions Card -->
        <v-card class="mt-4" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon color="warning" class="mr-3">mdi-cog</v-icon>
            <div>
              <h3 class="text-h6">Quick Actions</h3>
              <p class="text-body-2 text-medium-emphasis">User management</p>
            </div>
          </v-card-title>

          <v-card-text>
            <div class="quick-actions">
              <v-btn
                block
                variant="outlined"
                color="primary"
                prepend-icon="mdi-pencil"
                class="mb-3"
                @click="showEditDialog = true"
              >
                Edit User
              </v-btn>

              <v-btn
                block
                variant="outlined"
                color="warning"
                prepend-icon="mdi-lock-reset"
                class="mb-3"
                @click="showChangePasswordDialog = true"
              >
                Change Password
              </v-btn>

              <v-btn
                block
                variant="outlined"
                color="info"
                prepend-icon="mdi-email-edit"
                @click="showChangeEmailDialog = true"
              >
                Change Email
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Edit User Dialog -->
    <EditUserDialog
      v-model="showEditDialog"
      :user="user"
      @success="handleEditSuccess"
    />

    <!-- Change Password Dialog -->
    <ChangePasswordDialog
      v-model="showChangePasswordDialog"
      :user="user"
      @success="handlePasswordChangeSuccess"
    />

    <!-- Change Email Dialog -->
    <ChangeEmailDialog
      v-model="showChangeEmailDialog"
      :user="user"
      @success="handleEmailChangeSuccess"
    />
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { usersApi } from "@/services/users.api";
import { useSnackbar } from "@/composables/useSnackbar";
import { useAuthStore } from "@/stores/auth";
import ThePageHeader from "@/components/ThePageHeader.vue";
import EditUserDialog from "@/components/users/EditUserDialog.vue";
import ChangePasswordDialog from "@/components/users/ChangePasswordDialog.vue";
import ChangeEmailDialog from "@/components/users/ChangeEmailDialog.vue";
import type { User } from "@/types/user";
import { UserLifeCycle } from "@/types/user";

const route = useRoute();
const router = useRouter();
const { showSuccess, showError } = useSnackbar();
const authStore = useAuthStore();

const user = ref<User | null>(null);
const loading = ref(false);
const error = ref("");
const showEditDialog = ref(false);
const showChangePasswordDialog = ref(false);
const showChangeEmailDialog = ref(false);

const fetchUser = async () => {
  const userId = route.params.id as string;
  console.log("Fetching user with ID:", userId);
  console.log("Auth status:", authStore.isAuthenticated);
  console.log("Current user:", authStore.user);
  console.log("Access token exists:", !!localStorage.getItem("access_token"));

  if (!userId) {
    error.value = "Invalid user ID";
    return;
  }

  try {
    loading.value = true;
    error.value = "";
    // Convert string ID to number
    const numericUserId = Number(userId);
    if (isNaN(numericUserId)) {
      throw new Error("Invalid user ID format");
    }
    console.log("Calling API with numeric ID:", numericUserId);
    user.value = await usersApi.getUserById(numericUserId);
    console.log("User data received:", user.value);
  } catch (err: any) {
    console.error("Error fetching user:", err);
    console.error("Error response:", err.response);
    error.value =
      err.response?.data?.message ||
      err.message ||
      "Failed to load user details";
    showError("Failed to load user details");
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push("/users");
};

const handleEditSuccess = () => {
  showSuccess("User updated successfully");
  fetchUser(); // Refresh user data
};

const handlePasswordChangeSuccess = () => {
  showSuccess("Password changed successfully");
};

const handleEmailChangeSuccess = () => {
  showSuccess("Email changed successfully");
  fetchUser(); // Refresh user data
};

onMounted(() => {
  fetchUser();
});
</script>

<style scoped>
.user-detail-page {
  background: white;
  min-height: calc(100vh - 64px);
}

.user-info-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e0e0e0;
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.info-item {
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--v-theme-on-surface-variant);
  margin-bottom: 4px;
}

.info-value {
  font-size: 1rem;
  color: var(--v-theme-on-surface);
  font-weight: 400;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: var(--v-theme-on-surface);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Responsive design */
@media (max-width: 960px) {
  .user-detail-page {
    padding: 16px 8px;
  }

  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
