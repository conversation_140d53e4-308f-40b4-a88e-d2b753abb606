<template>
  <v-container fluid class="profile-page">
    <ThePageHeader
      title="My Profile"
      subtitle="Manage your personal information and account settings"
    >
      <template #default>
        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          prepend-icon="mdi-content-save"
          @click="handleSave"
          :loading="isSaving"
          :disabled="!hasChanges"
        >
          Save Changes
        </v-btn>
      </template>
    </ThePageHeader>

    <v-row>
      <!-- Profile Information Card -->
      <v-col cols="12" md="8">
        <v-card class="profile-card" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon color="primary" class="mr-3">mdi-account-edit</v-icon>
            <div>
              <h3 class="text-h6">Profile Information</h3>
              <p class="text-body-2 text-medium-emphasis">
                Update your personal details
              </p>
            </div>
          </v-card-title>

          <v-card-text>
            <v-form ref="formRef" v-model="isFormValid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="form.fullName"
                    label="Full Name"
                    :rules="nameRules"
                    required
                    variant="outlined"
                    prepend-inner-icon="mdi-account-outline"
                    color="primary"
                    class="modern-input"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="form.email"
                    label="Email Address"
                    type="email"
                    :rules="emailRules"
                    variant="outlined"
                    prepend-inner-icon="mdi-email-outline"
                    color="primary"
                    class="modern-input"
                  />
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    :value="user?.tenantId"
                    label="Tenant ID"
                    readonly
                    variant="outlined"
                    prepend-inner-icon="mdi-domain"
                    color="primary"
                    class="modern-input"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    :value="formatDate(user?.createdAt)"
                    label="Member Since"
                    readonly
                    variant="outlined"
                    prepend-inner-icon="mdi-calendar"
                    color="primary"
                    class="modern-input"
                  />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Account Status Card -->
      <v-col cols="12" md="4">
        <v-card class="status-card" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon color="success" class="mr-3">mdi-shield-check</v-icon>
            <div>
              <h3 class="text-h6">Account Status</h3>
              <p class="text-body-2 text-medium-emphasis">
                Your account information
              </p>
            </div>
          </v-card-title>

          <v-card-text>
            <div class="status-items">
              <div class="status-item">
                <div class="status-label">Account Status</div>
                <v-chip
                  :color="user?.isActive ? 'success' : 'error'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user?.isActive ? "mdi-check-circle" : "mdi-close-circle"
                    }}
                  </v-icon>
                  {{ user?.isActive ? "Active" : "Inactive" }}
                </v-chip>
              </div>

              <div class="status-item">
                <div class="status-label">Email Verification</div>
                <v-chip
                  :color="user?.emailVerified ? 'info' : 'warning'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user?.emailVerified
                        ? "mdi-email-check"
                        : "mdi-email-alert"
                    }}
                  </v-icon>
                  {{ user?.emailVerified ? "Verified" : "Pending" }}
                </v-chip>
              </div>

              <div class="status-item">
                <div class="status-label">Account Verification</div>
                <v-chip
                  :color="user?.verified ? 'success' : 'warning'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user?.verified ? "mdi-check-decagram" : "mdi-clock-alert"
                    }}
                  </v-icon>
                  {{ user?.verified ? "Verified" : "Pending" }}
                </v-chip>
              </div>

              <div class="status-item">
                <div class="status-label">Administrator</div>
                <v-chip
                  :color="user?.isAdministrator ? 'primary' : 'default'"
                  size="small"
                  variant="tonal"
                >
                  <v-icon start size="12">
                    {{
                      user?.isAdministrator ? "mdi-shield-crown" : "mdi-account"
                    }}
                  </v-icon>
                  {{ user?.isAdministrator ? "Yes" : "No" }}
                </v-chip>
              </div>
            </div>
          </v-card-text>
        </v-card>

        <!-- Quick Actions Card -->
        <v-card class="mt-4" elevation="2">
          <v-card-title class="d-flex align-center">
            <v-icon color="warning" class="mr-3">mdi-cog</v-icon>
            <div>
              <h3 class="text-h6">Quick Actions</h3>
              <p class="text-body-2 text-medium-emphasis">Account management</p>
            </div>
          </v-card-title>

          <v-card-text>
            <div class="quick-actions">
              <v-btn
                block
                variant="outlined"
                color="warning"
                prepend-icon="mdi-lock-reset"
                class="mb-3"
                @click="showChangePasswordDialog = true"
              >
                Change Password
              </v-btn>

              <v-btn
                block
                variant="outlined"
                color="info"
                prepend-icon="mdi-email-edit"
                @click="showChangeEmailDialog = true"
              >
                Change Email
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Change Password Dialog -->
    <ChangePasswordDialog
      v-model="showChangePasswordDialog"
      :user="user"
      @success="handlePasswordChangeSuccess"
    />

    <!-- Change Email Dialog -->
    <ChangeEmailDialog
      v-model="showChangeEmailDialog"
      :user="user"
      @success="handleEmailChangeSuccess"
    />
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useSnackbar } from "@/composables/useSnackbar";
import ThePageHeader from "@/components/ThePageHeader.vue";
import ChangePasswordDialog from "@/components/users/ChangePasswordDialog.vue";
import ChangeEmailDialog from "@/components/users/ChangeEmailDialog.vue";
import type { UpdateUserBioDataDto } from "@/types/user.dto";

const authStore = useAuthStore();
const { showSuccess, showError } = useSnackbar();

const user = computed(() => authStore.user);
const formRef = ref<any>(null);
const isFormValid = ref(false);
const isSaving = ref(false);
const showChangePasswordDialog = ref(false);
const showChangeEmailDialog = ref(false);

// Form data
const form = ref({
  fullName: "",
  email: "",
});

// Original data for comparison
const originalData = ref({
  fullName: "",
  email: "",
});

// Validation rules
const nameRules = [
  (v: string) => !!v || "Full name is required",
  (v: string) => v.length >= 2 || "Full name must be at least 2 characters",
];

const emailRules = [
  (v: string) => !v || /.+@.+\..+/.test(v) || "Email must be valid",
];

// Computed properties
const hasChanges = computed(() => {
  return (
    form.value.fullName !== originalData.value.fullName ||
    form.value.email !== originalData.value.email
  );
});

// Methods
const initializeForm = () => {
  if (user.value) {
    form.value = {
      fullName: user.value.fullName || "",
      email: user.value.email || "",
    };
    originalData.value = { ...form.value };
  }
};

const formatDate = (date: Date | string | undefined) => {
  if (!date) return "N/A";
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const handleSave = async () => {
  if (!isFormValid.value) return;

  try {
    isSaving.value = true;

    const updateData: UpdateUserBioDataDto = {};

    if (form.value.fullName !== originalData.value.fullName) {
      updateData.fullName = form.value.fullName;
    }

    if (form.value.email !== originalData.value.email) {
      updateData.email = form.value.email;
    }

    await authStore.updateProfile(updateData);

    // Update original data
    originalData.value = { ...form.value };

    showSuccess("Profile updated successfully");
  } catch (error: any) {
    showError(error.response?.data?.message || "Failed to update profile");
  } finally {
    isSaving.value = false;
  }
};

const handlePasswordChangeSuccess = () => {
  showSuccess("Password changed successfully");
};

const handleEmailChangeSuccess = () => {
  showSuccess("Email changed successfully");
  initializeForm(); // Refresh form with new email
};

// Watch for user changes
watch(user, initializeForm, { immediate: true });

onMounted(() => {
  initializeForm();
});
</script>

<style scoped>
.profile-page {
  background: linear-gradient(
    135deg,
    rgba(248, 250, 252, 1) 0%,
    rgba(241, 245, 249, 1) 100%
  );
  min-height: calc(100vh - 64px);
}

.profile-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.modern-input :deep(.v-field) {
  border-radius: 12px !important;
  background: rgba(255, 255, 255, 0.8) !important;
}

.modern-input :deep(.v-field--focused) {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}

.status-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: var(--v-theme-on-surface);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Responsive design */
@media (max-width: 960px) {
  .profile-page {
    padding: 16px 8px;
  }

  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
