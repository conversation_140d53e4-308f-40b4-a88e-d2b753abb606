<template>
  <v-app>
    <!-- Global App Bar -->
    <TheHeader v-if="isAuthenticated" :toggle-sidebar="toggleDrawer" />
    <!-- Sidebar -->
    <TheSidebar v-if="isAuthenticated" v-model="drawer" :rail="rail" />
    <v-main>
      <v-overlay v-model="loading" class="align-center justify-center">
        <v-progress-circular indeterminate size="64" />
      </v-overlay>
      <RouterView />
      <v-snackbar v-model="snackbar.show" :color="snackbar.color" :timeout="snackbar.timeout" multi-line transtion="scale-transition">
        {{ snackbar.message }}
        <template #actions>
          <v-btn variant="text" @click="close" size="small">Close</v-btn>
        </template>
      </v-snackbar>
    </v-main>
  </v-app>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RouterView } from 'vue-router'
import { useAppStore } from './stores/app'
import { useAuthStore } from './stores/auth'
import { storeToRefs } from 'pinia'
import { useSnackbar } from '@/composables/useSnackbar'
import TheSidebar from '@/components/TheSidebar.vue'
import TheHeader from '@/components/TheHeader.vue'

const appStore = useAppStore()
const authStore = useAuthStore()
const { snackbar, close } = useSnackbar()
const { loading } = storeToRefs(appStore)
const { isAuthenticated } = storeToRefs(authStore)

const drawer = ref(true)
const rail = ref(false)

const toggleDrawer = () => {
  rail.value = !rail.value
  drawer.value = !drawer.value
}
</script>

<style scoped></style>
