const dev = {
	BACKEND_SERVICE: 'http://localhost:3030/api',
	EVENT_SERVICE: 'http://localhost:3030/api/event',
};

const local = {
	BACKEND_SERVICE: import.meta.env.VITE_APP_LOCAL_NGINX,
	EVENT_SERVICE: import.meta.env.VITE_APP_LOCAL_NGINX,
};

const prod = {
	BACKEND_SERVICE: 'http://localhost:3030/api',
	EVENT_SERVICE:
		'http://localhost:3030/api/event',
};

const choose = {
	dev,
	prod,
	local,
};

const config = import.meta.env.VITE_APP_STAGE
	? choose[import.meta.env.VITE_APP_STAGE]
	: choose['prod'];

export default config;
