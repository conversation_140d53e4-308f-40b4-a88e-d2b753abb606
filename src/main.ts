import { NODE_ENV } from './shared/constants';
import { HttpAdapterHost, NestFactory, Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common';
import { json } from 'express';
import { AllExceptionsFilter } from './shared/exceptions/catch-all';
import { setupSwagger } from './shared';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const reflector = app.get(Reflector);
  const configService = app.get(ConfigService);
  const PORT = configService.get<number>('PORT') || 3030;
  const ENV = configService.get('NODE_ENV') || NODE_ENV.DEVELOPMENT;
  const httpAdapterHost = app.get(HttpAdapterHost);
  // const { httpAdapter } = httpAdapterHost;
  // Set API prefix based on environment
  const API_PREFIX = 'api';

  app.setGlobalPrefix(API_PREFIX);
  setupSwagger(app, configService);

  app.enableCors();
  app.use(json({ limit: '150mb' }));
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      // forbidNonWhitelisted: true,
      // transformOptions: {
      //   enableImplicitConversion: true,
      // },
    }),
  );

  app.useGlobalInterceptors(new ClassSerializerInterceptor(reflector));
  app.useGlobalFilters(
    new AllExceptionsFilter(httpAdapterHost),
    // new PrismaClientExceptionFilter(httpAdapter)
  );

  await app.listen(PORT, async () => {
    console.log(`Application is running on: ${await app.getUrl()}--${ENV}`);
    console.log(
      `Application docs running on: ${await app.getUrl()}/documentation`,
    );
  });
}
bootstrap();

