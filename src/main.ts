import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import axios from "axios";
import config from "./config";
import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedState from "pinia-plugin-persistedstate";
import router from "./router";
import "vuetify/styles";
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import App from "./App.vue";
import "./style.css";

// Components

const vuetify = createVuetify({
  components,
  directives,
  theme: {
    defaultTheme: "light",
    themes: {
      light: {
        colors: {
          primary: "#1976D2", // Professional blue
          secondary: "#424242", // Dark grey
          accent: "#FF6B35", // Orange accent for commissions
          error: "#F44336", // Red for errors
          warning: "#FF9800", // Amber for warnings
          info: "#2196F3", // Light blue for info
          success: "#4CAF50", // Green for success
          surface: "#FFFFFF", // White surface
          background: "#FFFFFF", // Light grey background
          "on-primary": "#FFFFFF",
          "on-secondary": "#FFFFFF",
          "on-surface": "#1A202C",
          "on-background": "#1A202C",
          "primary-darken-1": "#1565C0",
          "secondary-darken-1": "#303030",
        },
      },
      dark: {
        colors: {
          primary: "#2196F3", // Lighter blue for dark mode
          secondary: "#616161", // Medium grey
          accent: "#FF7043", // Lighter orange accent
          error: "#EF5350", // Lighter red
          warning: "#FFA726", // Lighter amber
          info: "#42A5F5", // Lighter blue
          success: "#66BB6A", // Lighter green
          surface: "#1E1E1E", // Dark surface
          background: "#121212", // Dark background
          "on-primary": "#FFFFFF",
          "on-secondary": "#FFFFFF",
          "on-surface": "#FFFFFF",
          "on-background": "#FFFFFF",
        },
      },
    },
  },
});
// Axios config
axios.defaults.baseURL = config["BACKEND_SERVICE"];

const app = createApp(App);
app.component("VueDatePicker", VueDatePicker);

const pinia = createPinia();
pinia.use(piniaPluginPersistedState);

app.use(pinia);
app.use(router);
app.use(vuetify);

app.mount("#app");
