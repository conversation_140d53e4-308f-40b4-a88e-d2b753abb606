<template>
  <v-navigation-drawer
    v-model="drawerModel"
    :rail="collapsed"
    :permanent="!isMobile"
    :temporary="isMobile"
    :rail-width="56"
    width="256"
    elevation="4"
    class="drawer"
  >
    <!-- Toggle Button -->
    <div class="toggle-wrapper px-2 py-4 d-flex justify-end">
      <v-btn
        variant="text"
        size="small"
        icon
        @click="toggleCollapsed"
        :disabled="isMobile"
      >
        <v-icon>{{
          collapsed ? "mdi-chevron-right" : "mdi-chevron-left"
        }}</v-icon>
      </v-btn>
    </div>

    <!-- User Profile Section -->
    <div class="user-section px-4 py-2">
      <v-row no-gutters align="center">
        <v-avatar size="48" color="primary">
          <span class="text-h6">{{ user?.firstName?.[0] || "U" }}</span>
        </v-avatar>
        <div v-if="!collapsed" class="user-info ml-4">
          <div class="user-name text-body-1 font-weight-medium">
            {{ user?.firstName || "User" }}
          </div>
          <div class="user-email text-caption text-medium-emphasis">
            {{ user?.email }}
          </div>
        </div>
      </v-row>
    </div>

    <v-divider class="my-2"></v-divider>

    <!-- Main Menu -->
    <v-list nav density="compact">
      <template v-for="group in sidebarLinks" :key="group.key">
        <v-list-subheader
          v-if="!collapsed"
          class="text-caption text-uppercase font-weight-bold"
        >
          {{ group.title }}
        </v-list-subheader>

        <v-list-item
          v-for="item in group.items"
          :key="item.key"
          :value="item.key"
          :to="item.key !== 'logout' ? item.key : undefined"
          :active="route.path === item.key"
          @click="handleMenuClick(item.key)"
          :title="item.label"
          :prepend-icon="getIconName(item.icon)"
        >
          <template v-slot:title>
            <span class="text-body-2">{{ item.label }}</span>
          </template>
        </v-list-item>
      </template>
    </v-list>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { useDisplay } from "vuetify";
interface SidebarItem {
  key: string;
  label: string;
  icon: any;
}

interface SidebarGroup {
  key: string;
  title: string;
  items: SidebarItem[];
}

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const { mobile } = useDisplay();

const user = computed(() => authStore.user);
const collapsed = ref(false);
const drawerModel = ref(false);
const isMobile = computed(() => mobile.value);

// Convert Ant Design icons to Material Design Icons
const iconMap: Record<string, string> = {
  DashboardOutlined: "mdi-view-dashboard",
  BankOutlined: "mdi-bank",
  UserOutlined: "mdi-account",
  TeamOutlined: "mdi-account-group",
  CustomerServiceOutlined: "mdi-headphones",
  ProfileOutlined: "mdi-account-circle",
  LogoutOutlined: "mdi-logout",
};

const getIconName = (antIcon: any): string => {
  const iconName = Object.keys(iconMap).find((key) => antIcon.name === key);
  return iconMap[iconName || ""] || "mdi-circle";
};

const sidebarLinks = ref<SidebarGroup[]>([
  {
    key: "main",
    title: "Main Menu",
    items: [
      // {
      //     key: '/dashboard',
      //     label: 'Dashboard',
      //     icon: 'DashboardOutlined'
      // },
      {
        key: "/users",
        label: "Users",
        icon: "UserOutlined",
      },
      {
        key: "/clients",
        label: "Clients",
        icon: "TeamOutlined",
      },
      {
        key: "/tenants",
        label: "tenants",
        icon: "CustomerServiceOutlined",
      },
    ],
  },
  {
    key: "account",
    title: "Account",
    items: [
      {
        key: "/profile",
        label: "Profile",
        icon: "ProfileOutlined",
      },
      {
        key: "logout",
        label: "Logout",
        icon: "LogoutOutlined",
      },
    ],
  },
]);

const emit = defineEmits<{
  "update:collapsed": [value: boolean];
}>();

const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
  emit("update:collapsed", collapsed.value);
};

const handleMenuClick = async (key: string) => {
  if (key === "logout") {
    await authStore.logout();
    router.push("/auth/login");
  }
};

const handleResize = () => {
  if (window.innerWidth < 992 && !isMobile.value) {
    collapsed.value = true;
    emit("update:collapsed", true);
  }
};

onMounted(() => {
  handleResize();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.drawer {
  border-right: 1px solid rgb(var(--v-border-color) var(--v-border-opacity));
}

.user-section {
  border-bottom: 1px solid rgb(var(--v-border-color) var(--v-border-opacity));
}
.user-info {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.v-list-item--active) {
  background-color: rgb(var(--v-theme-primary) var(--v-hover-opacity));
}

:deep(.v-list-item:hover) {
  background-color: rgb(var(--v-theme-surface-variant) var(--v-hover-opacity));
}
</style>
