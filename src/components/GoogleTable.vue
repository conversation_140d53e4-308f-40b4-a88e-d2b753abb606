<template>
    <v-card flat class="google-table">
        <!-- Table Toolbar with Tabs (Optional) -->
        <div v-if="showTabs" class="toolbar-container">
            <div class="d-flex align-center px-2">
                <v-tabs
                    v-model="activeTab"
                    class="primary"
                    color="deep-purple-accent-4"
                    align-tabs="start"
                    slider-color="#f78166"
                    @update:modelValue="handleTabChange"
                >
                    <v-tab
                        v-for="tab in tabs"
                        :key="tab.value"
                        :value="tab.value"
                        class="text-none"
                    >
                        {{ tab.label }}
                        <v-chip
                            v-if="tab.count"
                            size="x-small"
                            :color="tab.badgeColor"
                            class="ml-2"
                        >
                            {{ tab.count }}
                        </v-chip>
                    </v-tab>
                </v-tabs>
                
                <v-spacer></v-spacer>
                <!-- Toolbar Actions Slot -->
               
                    <slot name="toolbar-actions"></slot>
             
            </div>
        </div>
        <!-- Selected Items Actions Bar -->
        <v-slide-y-transition>
            <v-sheet
                v-if="showSelectionBar && selectedItems.length"
                class="selection-bar d-flex align-center px-4"
                color="primary"
                elevation="2"
            >
                <span class="text-white">{{ selectedItems.length }} selected</span>
                <v-spacer></v-spacer>
                <slot name="selection-actions">
                    <v-btn
                        variant="text"
                        color="white"
                        @click="clearSelection"
                    >
                        Clear
                    </v-btn>
                </slot>
            </v-sheet>
        </v-slide-y-transition>

        <!-- Contextual Banner (Optional) -->
        <v-banner
            v-if="showBanner"
            color="info"
            class="banner-container"
        >
            <slot name="banner-content">
                <v-icon start icon="mdi-information"></v-icon>
                {{ bannerText }}
            </slot>
        </v-banner>

        <!-- Main Table -->
        <v-data-table
            v-bind="$attrs"
            :headers="headers"
            :items="items"
            :loading="loading"
            v-model="selectedItems"
            hover
            class="google-data-table"
        >
            <!-- Default Item Slot -->
            <template v-for="(_, slotName) in $slots" :key="slotName" v-slot:[slotName]="slotData">
                <slot :name="slotName" v-bind="slotData"></slot>
            </template>
        </v-data-table>

        <!-- Custom Pagination -->
        <Pagination
            v-if="useCustomPagination"
            :total-pages="pagination.totalPages"
            :current-page="pagination.currentPage"
            :limit="pagination.limit"
            @page-size-change="handlePaginationChange"
        />
    </v-card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Pagination from './Paginations.vue';

interface Tab {
    label: string;
    value: string;
    count?: number;
    badgeColor?: string;
}

interface PaginationProps {
    totalPages: number;
    currentPage: number;
    limit: number;
}

const props = defineProps({
    headers: {
        type: Array,
        required: true,
    },
    items: {
        type: Array,
        required: true,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    showTabs: {
        type: Boolean,
        default: false,
    },
    tabs: {
        type: Array as () => Tab[],
        default: () => [],
    },
    showBanner: {
        type: Boolean,
        default: false,
    },
    bannerText: {
        type: String,
        default: '',
    },
    showSelectionBar: {
        type: Boolean,
        default: true,
    },
    useCustomPagination: {
        type: Boolean,
        default: false
    },
    pagination: {
        type: Object as () => PaginationProps,
        default: () => ({
            totalPages: 1,
            currentPage: 1,
            limit: 20
        })
    }
});

const emit = defineEmits([
    'clear-selection', 
    'update:selected', 
    'tab-change',
    'pagination-change'
]);
const activeTab = ref(props.tabs[0]?.value);

const selectedItems = ref<any[]>([]);

// Watch selectedItems and emit changes
watch(selectedItems, (newValue) => {
    emit('update:selected', newValue);
});



// Method to clear selection
const clearSelection = () => {
    selectedItems.value = [];
    emit('update:selected', []);
};

// Add tab change handler
const handleTabChange = (newValue: string) => {
    emit('tab-change', newValue);
};

// Handle pagination changes
const handlePaginationChange = (paginationData: { pageSize: number; page: number }) => {
    emit('pagination-change', paginationData);
};
</script>

<style scoped>
.google-table {
    border: 1px solid rgb(218, 220, 224);
    border-radius: 8px;
    overflow: hidden;
}

.toolbar-container {
    border-bottom: 1px solid rgb(218, 220, 224);
}

.google-tabs {
    min-height: 48px;
}

.banner-container {
    border-bottom: 1px solid rgb(218, 220, 224);
}

.google-data-table {
    /* Remove default table borders */
    :deep(.v-data-table__wrapper) {
        border: none;
    }

    :deep(th) {
        font-weight: 500 !important;
        color: rgb(95, 99, 104) !important;
        background-color: rgb(248, 249, 250) !important;
        border-bottom: 1px solid rgb(218, 220, 224) !important;
    }

    :deep(td) {
        border-bottom: 1px solid rgb(218, 220, 224) !important;
        color: rgb(32, 33, 36) !important;
    }

    :deep(tr:hover) {
        background-color: rgb(248, 249, 250) !important;
    }

    /* Add Google's selection style */
    :deep(tr.v-data-table__selected) {
        background-color: rgb(232, 240, 254) !important;
    }

    /* Keep hover effect even on selected rows */
    :deep(tr.v-data-table__selected:hover) {
        background-color: rgb(226, 235, 253) !important;
    }
}

.selection-toolbar {
    height: 48px;
    background-color: rgb(232, 240, 254);
    border-bottom: 1px solid rgb(218, 220, 224);
}

/* Remove the fixed position styles */
.selection-bar {
    position: static;
    height: 48px;
}

/* Ensure toolbar actions are properly aligned */
:deep(.v-btn) {
    margin: 4px;
}

:deep(.v-text-field) {
    margin: 4px;
}
</style>