<template>
  <v-overlay
    :model-value="loading"
    :persistent="persistent"
    :scrim="scrim"
    class="loading-overlay"
    :class="{ 'loading-overlay--fullscreen': fullscreen }"
  >
    <div class="loading-content">
      <!-- Loading Animation -->
      <div class="loading-animation">
        <v-progress-circular
          v-if="type === 'circular'"
          :indeterminate="indeterminate"
          :model-value="progress"
          :size="size"
          :width="width"
          :color="color"
          class="loading-spinner"
        />
        
        <v-progress-linear
          v-else-if="type === 'linear'"
          :indeterminate="indeterminate"
          :model-value="progress"
          :color="color"
          :height="4"
          class="loading-bar"
          rounded
        />
        
        <div v-else-if="type === 'dots'" class="loading-dots">
          <div
            v-for="i in 3"
            :key="i"
            class="loading-dot"
            :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
          />
        </div>
        
        <div v-else-if="type === 'pulse'" class="loading-pulse">
          <div class="pulse-circle" />
        </div>
      </div>

      <!-- Loading Text -->
      <div v-if="message" class="loading-message">
        <h3 class="loading-title">{{ message }}</h3>
        <p v-if="description" class="loading-description">
          {{ description }}
        </p>
      </div>

      <!-- Progress Information -->
      <div v-if="showProgress && !indeterminate" class="progress-info">
        <div class="progress-text">
          {{ Math.round(progress || 0) }}%
        </div>
        <div v-if="progressLabel" class="progress-label">
          {{ progressLabel }}
        </div>
      </div>

      <!-- Cancel Button -->
      <div v-if="cancellable" class="loading-actions">
        <v-btn
          variant="outlined"
          color="primary"
          size="small"
          @click="$emit('cancel')"
        >
          Cancel
        </v-btn>
      </div>
    </div>
  </v-overlay>
</template>

<script setup lang="ts">
interface Props {
  loading: boolean
  type?: 'circular' | 'linear' | 'dots' | 'pulse'
  message?: string
  description?: string
  progress?: number
  progressLabel?: string
  indeterminate?: boolean
  showProgress?: boolean
  cancellable?: boolean
  persistent?: boolean
  fullscreen?: boolean
  size?: number | string
  width?: number | string
  color?: string
  scrim?: boolean | string
}

withDefaults(defineProps<Props>(), {
  type: 'circular',
  indeterminate: true,
  showProgress: false,
  cancellable: false,
  persistent: false,
  fullscreen: false,
  size: 64,
  width: 4,
  color: 'primary',
  scrim: 'rgba(0, 0, 0, 0.5)'
})

defineEmits<{
  'cancel': []
}>()
</script>

<style scoped>
.loading-overlay {
  z-index: 9999;
}

.loading-overlay--fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 32px;
  background: rgba(var(--v-theme-surface), 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  text-align: center;
}

.loading-animation {
  position: relative;
}

.loading-spinner {
  filter: drop-shadow(0 4px 8px rgba(var(--v-theme-primary), 0.3));
}

.loading-bar {
  width: 200px;
  border-radius: 4px;
  overflow: hidden;
}

.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.loading-dot {
  width: 12px;
  height: 12px;
  background: rgb(var(--v-theme-primary));
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.loading-pulse {
  position: relative;
  width: 64px;
  height: 64px;
}

.pulse-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 64px;
  height: 64px;
  background: rgb(var(--v-theme-primary));
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s ease-in-out infinite;
}

.loading-message {
  max-width: 300px;
}

.loading-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.loading-description {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin: 0;
  line-height: 1.4;
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.progress-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(var(--v-theme-primary));
  line-height: 1;
}

.progress-label {
  font-size: 0.75rem;
  color: rgba(var(--v-theme-on-surface), 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.loading-actions {
  margin-top: 8px;
}

/* Animations */
@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .loading-content {
    padding: 24px 20px;
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .loading-title {
    font-size: 1.125rem;
  }
  
  .loading-bar {
    width: 150px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .loading-content {
    background: rgba(var(--v-theme-surface), 0.98);
    border-color: rgba(var(--v-theme-outline), 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
}
</style>
