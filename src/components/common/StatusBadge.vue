<template>
  <v-chip
    :color="badgeColor"
    :variant="variant"
    :size="size"
    :prepend-icon="icon"
    :class="[
      'status-badge',
      `status-badge--${status.toLowerCase()}`,
      { 'status-badge--animated': animated }
    ]"
  >
    <span class="status-text">{{ displayText }}</span>
    
    <!-- Pulse animation for active states -->
    <div
      v-if="animated && (status === 'active' || status === 'online')"
      class="pulse-indicator"
    />
  </v-chip>
</template>

<script setup lang="ts">
import { computed } from 'vue'

type StatusType = 
  | 'active' | 'inactive' | 'pending' | 'approved' | 'rejected' | 'cancelled'
  | 'online' | 'offline' | 'away' | 'busy'
  | 'success' | 'error' | 'warning' | 'info'
  | 'draft' | 'published' | 'archived'
  | 'new' | 'processing' | 'completed' | 'failed'

interface Props {
  status: StatusType
  text?: string
  variant?: 'flat' | 'tonal' | 'outlined' | 'text' | 'elevated' | 'plain'
  size?: 'x-small' | 'small' | 'default' | 'large' | 'x-large'
  animated?: boolean
  customColor?: string
  customIcon?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'tonal',
  size: 'small',
  animated: false
})

const statusConfig = {
  // Activity States
  active: { color: 'success', icon: 'mdi-check-circle', text: 'Active' },
  inactive: { color: 'grey', icon: 'mdi-pause-circle', text: 'Inactive' },
  pending: { color: 'warning', icon: 'mdi-clock-outline', text: 'Pending' },
  
  // Approval States
  approved: { color: 'success', icon: 'mdi-check-circle', text: 'Approved' },
  rejected: { color: 'error', icon: 'mdi-close-circle', text: 'Rejected' },
  cancelled: { color: 'grey', icon: 'mdi-cancel', text: 'Cancelled' },
  
  // Online States
  online: { color: 'success', icon: 'mdi-circle', text: 'Online' },
  offline: { color: 'grey', icon: 'mdi-circle-outline', text: 'Offline' },
  away: { color: 'warning', icon: 'mdi-circle-half-full', text: 'Away' },
  busy: { color: 'error', icon: 'mdi-minus-circle', text: 'Busy' },
  
  // General States
  success: { color: 'success', icon: 'mdi-check-circle', text: 'Success' },
  error: { color: 'error', icon: 'mdi-alert-circle', text: 'Error' },
  warning: { color: 'warning', icon: 'mdi-alert', text: 'Warning' },
  info: { color: 'info', icon: 'mdi-information', text: 'Info' },
  
  // Content States
  draft: { color: 'grey', icon: 'mdi-file-document-outline', text: 'Draft' },
  published: { color: 'success', icon: 'mdi-publish', text: 'Published' },
  archived: { color: 'grey', icon: 'mdi-archive', text: 'Archived' },
  
  // Process States
  new: { color: 'info', icon: 'mdi-new-box', text: 'New' },
  processing: { color: 'warning', icon: 'mdi-cog', text: 'Processing' },
  completed: { color: 'success', icon: 'mdi-check-circle', text: 'Completed' },
  failed: { color: 'error', icon: 'mdi-alert-circle', text: 'Failed' }
}

const config = computed(() => statusConfig[props.status] || statusConfig.info)

const badgeColor = computed(() => props.customColor || config.value.color)
const icon = computed(() => props.customIcon || config.value.icon)
const displayText = computed(() => props.text || config.value.text)
</script>

<style scoped>
.status-badge {
  position: relative;
  font-weight: 500;
  letter-spacing: 0.025em;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.status-text {
  position: relative;
  z-index: 1;
}

.pulse-indicator {
  position: absolute;
  top: 50%;
  left: 8px;
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: 50%;
  transform: translateY(-50%);
  animation: pulse 2s ease-in-out infinite;
}

.status-badge--animated .pulse-indicator {
  animation: pulse 2s ease-in-out infinite;
}

/* Status-specific styling */
.status-badge--active,
.status-badge--online,
.status-badge--success,
.status-badge--approved,
.status-badge--completed,
.status-badge--published {
  background: linear-gradient(135deg, 
    rgba(76, 175, 80, 0.1) 0%, 
    rgba(139, 195, 74, 0.1) 100%) !important;
}

.status-badge--pending,
.status-badge--warning,
.status-badge--away,
.status-badge--processing {
  background: linear-gradient(135deg, 
    rgba(255, 193, 7, 0.1) 0%, 
    rgba(255, 152, 0, 0.1) 100%) !important;
}

.status-badge--error,
.status-badge--rejected,
.status-badge--failed,
.status-badge--busy {
  background: linear-gradient(135deg, 
    rgba(244, 67, 54, 0.1) 0%, 
    rgba(233, 30, 99, 0.1) 100%) !important;
}

.status-badge--info,
.status-badge--new {
  background: linear-gradient(135deg, 
    rgba(33, 150, 243, 0.1) 0%, 
    rgba(103, 58, 183, 0.1) 100%) !important;
}

.status-badge--inactive,
.status-badge--offline,
.status-badge--cancelled,
.status-badge--archived,
.status-badge--draft {
  background: linear-gradient(135deg, 
    rgba(158, 158, 158, 0.1) 0%, 
    rgba(117, 117, 117, 0.1) 100%) !important;
}

/* Animation keyframes */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translateY(-50%) scale(1.2);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}

/* Size variations */
.status-badge.v-chip--size-x-small {
  font-size: 0.625rem;
  height: 20px;
}

.status-badge.v-chip--size-small {
  font-size: 0.75rem;
  height: 24px;
}

.status-badge.v-chip--size-default {
  font-size: 0.875rem;
  height: 28px;
}

.status-badge.v-chip--size-large {
  font-size: 1rem;
  height: 32px;
}

.status-badge.v-chip--size-x-large {
  font-size: 1.125rem;
  height: 36px;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .status-badge:hover {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility improvements */
.status-badge:focus-visible {
  outline: 2px solid rgb(var(--v-theme-primary));
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .status-badge {
    border: 1px solid currentColor;
  }
}
</style>
