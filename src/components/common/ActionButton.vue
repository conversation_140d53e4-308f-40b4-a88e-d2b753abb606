<template>
  <v-btn
    :variant="variant"
    :color="color"
    :size="size"
    :prepend-icon="prependIcon"
    :append-icon="appendIcon"
    :loading="loading"
    :disabled="disabled"
    :class="[
      'action-button',
      `action-button--${actionType}`,
      { 'action-button--elevated': elevated }
    ]"
    @click="handleClick"
  >
    <template v-if="$slots.prepend" #prepend>
      <slot name="prepend" />
    </template>
    
    <span class="action-button__text">
      <slot>{{ text }}</slot>
    </span>
    
    <template v-if="$slots.append" #append>
      <slot name="append" />
    </template>
    
    <!-- Loading overlay -->
    <div v-if="loading" class="action-button__loading">
      <v-progress-circular
        :size="16"
        :width="2"
        indeterminate
        color="current"
      />
    </div>
  </v-btn>
</template>

<script setup lang="ts">
import { computed } from 'vue'

type ActionType = 
  | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  | 'create' | 'edit' | 'delete' | 'save' | 'cancel' | 'submit'
  | 'view' | 'download' | 'upload' | 'share' | 'copy'

interface Props {
  actionType?: ActionType
  text?: string
  variant?: 'flat' | 'tonal' | 'outlined' | 'text' | 'elevated' | 'plain'
  color?: string
  size?: 'x-small' | 'small' | 'default' | 'large' | 'x-large'
  prependIcon?: string
  appendIcon?: string
  loading?: boolean
  disabled?: boolean
  elevated?: boolean
  confirmAction?: boolean
  confirmMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  actionType: 'primary',
  variant: 'flat',
  size: 'default',
  loading: false,
  disabled: false,
  elevated: false,
  confirmAction: false,
  confirmMessage: 'Are you sure you want to perform this action?'
})

const emit = defineEmits<{
  'click': [event: MouseEvent]
  'confirm': []
  'cancel': []
}>()

const actionConfig = {
  // Basic types
  primary: { color: 'primary', icon: null },
  secondary: { color: 'secondary', icon: null },
  success: { color: 'success', icon: 'mdi-check' },
  warning: { color: 'warning', icon: 'mdi-alert' },
  error: { color: 'error', icon: 'mdi-alert-circle' },
  info: { color: 'info', icon: 'mdi-information' },
  
  // Action types
  create: { color: 'success', icon: 'mdi-plus' },
  edit: { color: 'primary', icon: 'mdi-pencil' },
  delete: { color: 'error', icon: 'mdi-delete' },
  save: { color: 'success', icon: 'mdi-content-save' },
  cancel: { color: 'grey', icon: 'mdi-cancel' },
  submit: { color: 'primary', icon: 'mdi-check' },
  
  // Utility types
  view: { color: 'info', icon: 'mdi-eye' },
  download: { color: 'primary', icon: 'mdi-download' },
  upload: { color: 'primary', icon: 'mdi-upload' },
  share: { color: 'primary', icon: 'mdi-share' },
  copy: { color: 'primary', icon: 'mdi-content-copy' }
}

const config = computed(() => actionConfig[props.actionType] || actionConfig.primary)

const color = computed(() => props.color || config.value.color)
const prependIcon = computed(() => {
  if (props.prependIcon !== undefined) return props.prependIcon
  return config.value.icon
})

const handleClick = async (event: MouseEvent) => {
  if (props.confirmAction) {
    const confirmed = await showConfirmDialog()
    if (confirmed) {
      emit('confirm')
      emit('click', event)
    } else {
      emit('cancel')
    }
  } else {
    emit('click', event)
  }
}

const showConfirmDialog = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const confirmed = window.confirm(props.confirmMessage)
    resolve(confirmed)
  })
}
</script>

<style scoped>
.action-button {
  position: relative;
  font-weight: 500;
  letter-spacing: 0.025em;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.action-button:hover::before {
  opacity: 1;
}

.action-button--elevated {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-button--elevated:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-button__text {
  position: relative;
  z-index: 1;
}

.action-button__loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

/* Action type specific styling */
.action-button--create {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-success)) 0%, 
    rgba(var(--v-theme-success), 0.8) 100%) !important;
}

.action-button--edit {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-primary)) 0%, 
    rgba(var(--v-theme-primary), 0.8) 100%) !important;
}

.action-button--delete {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-error)) 0%, 
    rgba(var(--v-theme-error), 0.8) 100%) !important;
}

.action-button--save {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-success)) 0%, 
    rgba(var(--v-theme-success), 0.8) 100%) !important;
}

.action-button--cancel {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-surface-variant)) 0%, 
    rgba(var(--v-theme-surface-variant), 0.8) 100%) !important;
  color: rgb(var(--v-theme-on-surface-variant)) !important;
}

.action-button--warning {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-warning)) 0%, 
    rgba(var(--v-theme-warning), 0.8) 100%) !important;
}

.action-button--info {
  background: linear-gradient(135deg, 
    rgb(var(--v-theme-info)) 0%, 
    rgba(var(--v-theme-info), 0.8) 100%) !important;
}

/* Loading state */
.action-button[loading] .action-button__text {
  opacity: 0.6;
}

/* Disabled state */
.action-button[disabled] {
  opacity: 0.6;
  pointer-events: none;
}

/* Focus state */
.action-button:focus-visible {
  outline: 2px solid rgb(var(--v-theme-primary));
  outline-offset: 2px;
}

/* Size variations */
.action-button.v-btn--size-x-small {
  font-size: 0.625rem;
  min-height: 24px;
  padding: 0 8px;
}

.action-button.v-btn--size-small {
  font-size: 0.75rem;
  min-height: 28px;
  padding: 0 12px;
}

.action-button.v-btn--size-default {
  font-size: 0.875rem;
  min-height: 36px;
  padding: 0 16px;
}

.action-button.v-btn--size-large {
  font-size: 1rem;
  min-height: 44px;
  padding: 0 20px;
}

.action-button.v-btn--size-x-large {
  font-size: 1.125rem;
  min-height: 52px;
  padding: 0 24px;
}

/* Ripple effect enhancement */
.action-button :deep(.v-ripple__container) {
  border-radius: inherit;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .action-button--elevated {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .action-button--elevated:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .action-button {
    border: 1px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .action-button {
    transition: none;
  }
  
  .action-button--elevated:hover {
    transform: none;
  }
}
</style>
