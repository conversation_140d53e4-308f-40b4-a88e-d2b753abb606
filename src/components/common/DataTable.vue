<template>
  <div class="data-table">
    <!-- Table Header -->
    <div v-if="title || $slots.header" class="table-header">
      <div class="table-title-section">
        <h3 v-if="title" class="table-title">{{ title }}</h3>
        <p v-if="subtitle" class="table-subtitle">{{ subtitle }}</p>
      </div>
      <div class="table-actions">
        <slot name="header" />
      </div>
    </div>

    <!-- Search and Filters -->
    <div v-if="searchable || $slots.filters" class="table-controls">
      <div v-if="searchable" class="search-section">
        <v-text-field
          v-model="searchQuery"
          :placeholder="searchPlaceholder"
          prepend-inner-icon="mdi-magnify"
          variant="outlined"
          density="compact"
          hide-details
          clearable
          class="search-field"
        />
      </div>
      <div v-if="$slots.filters" class="filters-section">
        <slot name="filters" />
      </div>
    </div>

    <!-- Data Table -->
    <v-data-table
      v-model:items-per-page="itemsPerPageLocal"
      v-model:page="currentPageLocal"
      v-model:sort-by="sortByLocal"
      :headers="headers"
      :items="filteredItems"
      :loading="loading"
      :items-per-page-options="itemsPerPageOptions"
      :show-current-page="showCurrentPage"
      :show-expand="expandable"
      :expand-on-click="expandOnClick"
      class="modern-data-table"
      :class="{ 'table-dense': dense }"
    >
      <!-- Loading slot -->
      <template #loading>
        <div class="table-loading">
          <v-progress-circular indeterminate color="primary" size="32" />
          <span class="loading-text">{{ loadingText }}</span>
        </div>
      </template>

      <!-- No data slot -->
      <template #no-data>
        <div class="table-no-data">
          <v-icon icon="mdi-database-off" size="48" color="grey" />
          <h4 class="no-data-title">{{ noDataText }}</h4>
          <p class="no-data-subtitle">{{ noDataSubtext }}</p>
          <slot name="no-data-actions" />
        </div>
      </template>

      <!-- Custom header slots -->
      <template v-for="header in headers" :key="header.key" #[`header.${header.key}`]="{ column }">
        <slot :name="`header.${header.key}`" :column="column">
          <span class="header-text">{{ column.title }}</span>
        </slot>
      </template>

      <!-- Custom item slots -->
      <template v-for="header in headers" :key="header.key" #[`item.${header.key}`]="{ item, value }">
        <slot :name="`item.${header.key}`" :item="item" :value="value">
          {{ value }}
        </slot>
      </template>

      <!-- Actions column -->
      <template v-if="showActions" #item.actions="{ item }">
        <div class="table-actions-cell">
          <slot name="actions" :item="item">
            <v-btn
              v-if="viewAction"
              icon="mdi-eye"
              size="small"
              variant="text"
              @click="$emit('view', item)"
            />
            <v-btn
              v-if="editAction"
              icon="mdi-pencil"
              size="small"
              variant="text"
              @click="$emit('edit', item)"
            />
            <v-btn
              v-if="deleteAction"
              icon="mdi-delete"
              size="small"
              variant="text"
              color="error"
              @click="$emit('delete', item)"
            />
          </slot>
        </div>
      </template>

      <!-- Expanded row content -->
      <template v-if="expandable" #expanded-row="{ item }">
        <slot name="expanded-row" :item="item" />
      </template>

      <!-- Bottom pagination -->
      <template #bottom>
        <div class="table-pagination">
          <div class="pagination-info">
            <span class="pagination-text">
              Showing {{ startItem }} to {{ endItem }} of {{ totalItems }} entries
            </span>
          </div>
          <v-pagination
            v-model="currentPageLocal"
            :length="totalPages"
            :total-visible="7"
            class="pagination-controls"
          />
        </div>
      </template>
    </v-data-table>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'

interface TableHeader {
  key: string
  title: string
  sortable?: boolean
  width?: string | number
  align?: 'start' | 'center' | 'end'
}

interface Props {
  title?: string
  subtitle?: string
  headers: TableHeader[]
  items: any[]
  loading?: boolean
  searchable?: boolean
  searchPlaceholder?: string
  expandable?: boolean
  expandOnClick?: boolean
  dense?: boolean
  showActions?: boolean
  viewAction?: boolean
  editAction?: boolean
  deleteAction?: boolean
  itemsPerPage?: number
  currentPage?: number
  totalItems?: number
  showCurrentPage?: boolean
  loadingText?: string
  noDataText?: string
  noDataSubtext?: string
  itemsPerPageOptions?: { value: number; title: string }[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: false,
  searchPlaceholder: 'Search...',
  expandable: false,
  expandOnClick: false,
  dense: false,
  showActions: false,
  viewAction: false,
  editAction: false,
  deleteAction: false,
  itemsPerPage: 10,
  currentPage: 1,
  showCurrentPage: true,
  loadingText: 'Loading data...',
  noDataText: 'No data available',
  noDataSubtext: 'There are no records to display at this time.',
  itemsPerPageOptions: () => [
    { value: 5, title: '5' },
    { value: 10, title: '10' },
    { value: 25, title: '25' },
    { value: 50, title: '50' },
    { value: -1, title: 'All' }
  ]
})

const emit = defineEmits<{
  'update:itemsPerPage': [value: number]
  'update:currentPage': [value: number]
  'update:sortBy': [value: any[]]
  'view': [item: any]
  'edit': [item: any]
  'delete': [item: any]
}>()

const searchQuery = ref('')
const itemsPerPageLocal = ref(props.itemsPerPage)
const currentPageLocal = ref(props.currentPage)
const sortByLocal = ref([])

// Add actions column if needed
const headers = computed(() => {
  const baseHeaders = [...props.headers]
  if (props.showActions) {
    baseHeaders.push({
      key: 'actions',
      title: 'Actions',
      sortable: false,
      width: 120,
      align: 'center'
    })
  }
  return baseHeaders
})

// Filter items based on search
const filteredItems = computed(() => {
  if (!props.searchable || !searchQuery.value) {
    return props.items
  }
  
  const query = searchQuery.value.toLowerCase()
  return props.items.filter(item => {
    return Object.values(item).some(value => 
      String(value).toLowerCase().includes(query)
    )
  })
})

// Pagination calculations
const totalItems = computed(() => props.totalItems || filteredItems.value.length)
const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPageLocal.value))
const startItem = computed(() => (currentPageLocal.value - 1) * itemsPerPageLocal.value + 1)
const endItem = computed(() => Math.min(currentPageLocal.value * itemsPerPageLocal.value, totalItems.value))

// Watch for prop changes
watch(() => props.itemsPerPage, (newVal) => {
  itemsPerPageLocal.value = newVal
})

watch(() => props.currentPage, (newVal) => {
  currentPageLocal.value = newVal
})

// Emit changes
watch(itemsPerPageLocal, (newVal) => {
  emit('update:itemsPerPage', newVal)
})

watch(currentPageLocal, (newVal) => {
  emit('update:currentPage', newVal)
})

watch(sortByLocal, (newVal) => {
  emit('update:sortBy', newVal)
}, { deep: true })
</script>

<style scoped>
.data-table {
  background: rgb(var(--v-theme-surface));
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid rgba(var(--v-theme-outline), 0.1);
  background: linear-gradient(135deg,
    rgba(var(--v-theme-primary), 0.02) 0%,
    rgba(var(--v-theme-secondary), 0.02) 100%);
}

.table-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
  margin: 0 0 4px 0;
}

.table-subtitle {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin: 0;
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid rgba(var(--v-theme-outline), 0.1);
  background: rgba(var(--v-theme-surface-variant), 0.3);
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.search-field {
  background: rgb(var(--v-theme-surface));
  border-radius: 8px;
}

.filters-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modern-data-table {
  background: transparent;
}

.modern-data-table :deep(.v-data-table__wrapper) {
  border-radius: 0;
}

.modern-data-table :deep(.v-data-table-header) {
  background: rgba(var(--v-theme-surface-variant), 0.5);
}

.modern-data-table :deep(.v-data-table-header th) {
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
  border-bottom: 2px solid rgba(var(--v-theme-primary), 0.2);
}

.modern-data-table :deep(.v-data-table__tr:hover) {
  background: rgba(var(--v-theme-primary), 0.04) !important;
}

.table-dense :deep(.v-data-table__tr) {
  height: 40px;
}

.table-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 48px;
}

.loading-text {
  color: rgba(var(--v-theme-on-surface), 0.7);
  font-size: 0.875rem;
}

.table-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 48px;
  text-align: center;
}

.no-data-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: rgb(var(--v-theme-on-surface));
  margin: 0;
}

.no-data-subtitle {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin: 0;
}

.table-actions-cell {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}

.table-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid rgba(var(--v-theme-outline), 0.1);
  background: rgba(var(--v-theme-surface-variant), 0.3);
}

.pagination-text {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
}

.header-text {
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
}

/* Responsive design */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .table-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .table-pagination {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .data-table {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}
</style>
