<template>
  <div class="notification-badge-wrapper">
    <slot />
    
    <v-badge
      v-if="count > 0"
      :content="displayCount"
      :color="color"
      :dot="dot"
      :inline="inline"
      :floating="floating"
      :offset-x="offsetX"
      :offset-y="offsetY"
      class="notification-badge"
      :class="{ 'badge-pulse': pulse }"
    >
      <template #badge>
        <span class="badge-content">
          {{ displayCount }}
        </span>
      </template>
    </v-badge>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  count: number
  maxCount?: number
  color?: string
  dot?: boolean
  inline?: boolean
  floating?: boolean
  pulse?: boolean
  offsetX?: number
  offsetY?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxCount: 99,
  color: 'error',
  dot: false,
  inline: false,
  floating: true,
  pulse: true,
  offsetX: 0,
  offsetY: 0
})

const displayCount = computed(() => {
  if (props.dot) return ''
  if (props.count > props.maxCount) {
    return `${props.maxCount}+`
  }
  return props.count.toString()
})
</script>

<style scoped>
.notification-badge-wrapper {
  position: relative;
  display: inline-block;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}

.badge-content {
  font-size: 0.7rem;
  font-weight: 600;
  line-height: 1;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9px;
  padding: 0 4px;
  background: rgb(var(--v-theme-error));
  color: rgb(var(--v-theme-on-error));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.badge-pulse {
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Color variations */
.notification-badge :deep(.v-badge--dot) {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .badge-content {
    border: 1px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .badge-pulse {
    animation: none;
  }
}
</style>
