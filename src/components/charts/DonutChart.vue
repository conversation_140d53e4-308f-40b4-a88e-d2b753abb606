<template>
  <div class="donut-chart">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <p v-if="subtitle" class="chart-subtitle">{{ subtitle }}</p>
    </div>
    
    <div class="chart-content">
      <div class="chart-container">
        <Doughnut
          :data="chartData"
          :options="chartOptions"
          class="donut-canvas"
        />
        
        <!-- Center content -->
        <div class="chart-center">
          <div class="center-value">{{ formattedTotal }}</div>
          <div class="center-label">{{ centerLabel }}</div>
        </div>
      </div>
      
      <!-- Legend -->
      <div class="chart-legend">
        <div
          v-for="(item, index) in legendItems"
          :key="index"
          class="legend-item"
        >
          <div
            class="legend-color"
            :style="{ backgroundColor: item.color }"
          />
          <div class="legend-content">
            <div class="legend-label">{{ item.label }}</div>
            <div class="legend-value">{{ item.formattedValue }}</div>
            <div class="legend-percentage">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Doughnut } from 'vue-chartjs'
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js'

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend)

interface ChartItem {
  label: string
  value: number
  color?: string
}

interface Props {
  title: string
  subtitle?: string
  data: ChartItem[]
  centerLabel?: string
  currency?: boolean
  colors?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  centerLabel: 'Total',
  currency: false,
  colors: () => [
    'rgb(25, 118, 210)',   // Primary blue
    'rgb(255, 107, 53)',   // Orange
    'rgb(76, 175, 80)',    // Green
    'rgb(156, 39, 176)',   // Purple
    'rgb(255, 193, 7)',    // Amber
    'rgb(96, 125, 139)',   // Blue grey
    'rgb(233, 30, 99)',    // Pink
    'rgb(121, 85, 72)'     // Brown
  ]
})

const total = computed(() => {
  return props.data.reduce((sum, item) => sum + item.value, 0)
})

const formattedTotal = computed(() => {
  if (props.currency) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(total.value)
  }
  return total.value.toLocaleString()
})

const legendItems = computed(() => {
  return props.data.map((item, index) => {
    const color = item.color || props.colors[index % props.colors.length]
    const percentage = total.value > 0 ? ((item.value / total.value) * 100).toFixed(1) : '0'
    
    let formattedValue = item.value.toLocaleString()
    if (props.currency) {
      formattedValue = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(item.value)
    }
    
    return {
      label: item.label,
      value: item.value,
      formattedValue,
      percentage,
      color
    }
  })
})

const chartData = computed(() => ({
  labels: props.data.map(item => item.label),
  datasets: [
    {
      data: props.data.map(item => item.value),
      backgroundColor: props.data.map((item, index) => 
        item.color || props.colors[index % props.colors.length]
      ),
      borderWidth: 0,
      cutout: '70%',
      borderRadius: 4,
      spacing: 2
    }
  ]
}))

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#ffffff',
      bodyColor: '#ffffff',
      borderColor: 'rgb(25, 118, 210)',
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      callbacks: {
        label: (context: any) => {
          const value = props.currency 
            ? new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
              }).format(context.parsed)
            : context.parsed.toLocaleString()
          
          const percentage = total.value > 0 
            ? ((context.parsed / total.value) * 100).toFixed(1)
            : '0'
          
          return `${context.label}: ${value} (${percentage}%)`
        }
      }
    }
  },
  elements: {
    arc: {
      borderWidth: 0
    }
  }
}))
</script>

<style scoped>
.donut-chart {
  background: rgb(var(--v-theme-surface));
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 24px;
  text-align: center;
}

.chart-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin: 0;
  line-height: 1.4;
}

.chart-content {
  display: flex;
  align-items: center;
  gap: 32px;
}

.chart-container {
  position: relative;
  width: 200px;
  height: 200px;
  flex-shrink: 0;
}

.donut-canvas {
  width: 100%;
  height: 100%;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.center-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(var(--v-theme-primary));
  line-height: 1.2;
  margin-bottom: 2px;
}

.center-label {
  font-size: 0.75rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chart-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(var(--v-theme-surface-variant), 0.3);
}

.legend-item:hover {
  background: rgba(var(--v-theme-primary), 0.08);
  transform: translateX(4px);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-content {
  flex: 1;
  min-width: 0;
}

.legend-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(var(--v-theme-on-surface));
  line-height: 1.2;
  margin-bottom: 2px;
}

.legend-value {
  font-size: 0.75rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  line-height: 1.2;
}

.legend-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgb(var(--v-theme-primary));
  line-height: 1.2;
}

/* Responsive design */
@media (max-width: 768px) {
  .donut-chart {
    padding: 16px;
  }
  
  .chart-content {
    flex-direction: column;
    gap: 24px;
  }
  
  .chart-container {
    width: 160px;
    height: 160px;
  }
  
  .center-value {
    font-size: 1.25rem;
  }
  
  .chart-legend {
    max-height: none;
    width: 100%;
  }
}

/* Scrollbar styling for legend */
.chart-legend::-webkit-scrollbar {
  width: 4px;
}

.chart-legend::-webkit-scrollbar-track {
  background: transparent;
}

.chart-legend::-webkit-scrollbar-thumb {
  background: rgba(var(--v-theme-on-surface), 0.2);
  border-radius: 2px;
}

.chart-legend::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--v-theme-on-surface), 0.3);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .donut-chart {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}
</style>
