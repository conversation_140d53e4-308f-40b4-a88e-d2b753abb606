<template>
  <div class="mini-chart">
    <div class="mini-chart-header">
      <div class="chart-info">
        <h4 class="chart-title">{{ title }}</h4>
        <div class="chart-value">{{ formattedValue }}</div>
        <div class="chart-change" :class="changeClass">
          <v-icon :icon="changeIcon" size="14" />
          {{ changeText }}
        </div>
      </div>
      <div class="chart-icon">
        <v-icon :icon="icon" :color="iconColor" size="24" />
      </div>
    </div>
    <div class="mini-chart-container">
      <Line
        :data="chartData"
        :options="chartOptions"
        class="mini-chart-canvas"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Filler,
} from "chart.js";

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Filler);

interface Props {
  title: string;
  value: number;
  change: number;
  data: number[];
  icon?: string;
  iconColor?: string;
  color?: string;
  currency?: boolean;
  suffix?: string;
  showDetails?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  icon: "mdi-chart-line",
  iconColor: "primary",
  color: "rgb(25, 118, 210)",
  currency: false,
  suffix: "",
  showDetails: true,
});

const formattedValue = computed(() => {
  if (props.currency) {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(props.value);
  }
  return `${props.value.toLocaleString()}${props.suffix}`;
});

const changeClass = computed(() => {
  if (props.change > 0) return "positive";
  if (props.change < 0) return "negative";
  return "neutral";
});

const changeIcon = computed(() => {
  if (props.change > 0) return "mdi-trending-up";
  if (props.change < 0) return "mdi-trending-down";
  return "mdi-minus";
});

const changeText = computed(() => {
  const absChange = Math.abs(props.change);
  const sign = props.change > 0 ? "+" : props.change < 0 ? "-" : "";
  return `${sign}${absChange.toFixed(1)}%`;
});

const chartData = computed(() => ({
  labels: props.data.map((_, index) => index.toString()),
  datasets: [
    {
      data: props.data,
      borderColor: props.color,
      backgroundColor: `${props.color}20`,
      borderWidth: 2,
      fill: true,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 0,
    },
  ],
}));

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      enabled: false,
    },
  },
  scales: {
    x: {
      display: false,
    },
    y: {
      display: false,
    },
  },
  elements: {
    point: {
      radius: 0,
    },
  },
  interaction: {
    intersect: false,
  },
}));
</script>

<style scoped>
.mini-chart {
  background: rgb(var(--v-theme-surface));
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  transition: all 0.2s ease;
  height: 140px;
  display: flex;
  flex-direction: column;
}

.mini-chart:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mini-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.chart-info {
  flex: 1;
}

.chart-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.chart-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(var(--v-theme-on-surface));
  line-height: 1.2;
  margin-bottom: 4px;
}

.chart-change {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 0.75rem;
  font-weight: 600;
}

.chart-change.positive {
  color: rgb(var(--v-theme-success));
}

.chart-change.negative {
  color: rgb(var(--v-theme-error));
}

.chart-change.neutral {
  color: rgba(var(--v-theme-on-surface), 0.6);
}

.chart-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(var(--v-theme-primary), 0.1);
  border-radius: 10px;
  flex-shrink: 0;
}

.mini-chart-container {
  flex: 1;
  position: relative;
  min-height: 60px;
}

.mini-chart-canvas {
  height: 100%;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .mini-chart:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}
</style>
