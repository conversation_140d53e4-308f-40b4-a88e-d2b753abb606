<template>
  <div class="commission-trends-chart">
    <!-- Chart Header -->
    <div class="chart-header">
      <div class="chart-title-section">
        <h3 class="chart-title">Commission Trends</h3>
        <p class="chart-subtitle">Monthly commission performance overview</p>
      </div>

      <div class="chart-controls">
        <v-btn-toggle
          v-model="selectedPeriod"
          variant="outlined"
          density="compact"
          class="period-toggle"
        >
          <v-btn value="6m" size="small">6M</v-btn>
          <v-btn value="1y" size="small">1Y</v-btn>
          <v-btn value="2y" size="small">2Y</v-btn>
        </v-btn-toggle>

        <v-menu>
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              variant="outlined"
              size="small"
              prepend-icon="mdi-download"
            >
              Export
            </v-btn>
          </template>
          <v-list density="compact">
            <v-list-item @click="exportChart('png')">
              <v-list-item-title>Export as PNG</v-list-item-title>
            </v-list-item>
            <v-list-item @click="exportChart('pdf')">
              <v-list-item-title>Export as PDF</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </div>

    <!-- Chart Container -->
    <div class="chart-container" :class="{ 'chart-loading': loading }">
      <div v-if="loading" class="chart-loading-overlay">
        <v-progress-circular indeterminate color="primary" size="32" />
        <span class="loading-text">Loading chart data...</span>
      </div>

      <Line
        v-else
        ref="chartRef"
        :data="chartData"
        :options="chartOptions"
        class="commission-chart"
      />
    </div>

    <!-- Chart Stats -->
    <div class="chart-stats">
      <div class="stat-item">
        <div class="stat-value">{{ formatCurrency(totalCommissions) }}</div>
        <div class="stat-label">Total Commissions</div>
        <div class="stat-change positive">
          <v-icon size="16">mdi-trending-up</v-icon>
          +12.5%
        </div>
      </div>

      <div class="stat-item">
        <div class="stat-value">{{ formatCurrency(avgMonthly) }}</div>
        <div class="stat-label">Avg Monthly</div>
        <div class="stat-change positive">
          <v-icon size="16">mdi-trending-up</v-icon>
          +8.3%
        </div>
      </div>

      <div class="stat-item">
        <div class="stat-value">{{ formatCurrency(highestMonth) }}</div>
        <div class="stat-label">Highest Month</div>
        <div class="stat-change neutral">
          <v-icon size="16">mdi-minus</v-icon>
          0%
        </div>
      </div>

      <div class="stat-item">
        <div class="stat-value">{{ growthRate }}%</div>
        <div class="stat-label">Growth Rate</div>
        <div class="stat-change positive">
          <v-icon size="16">mdi-trending-up</v-icon>
          ****%
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
);

interface Props {
  loading?: boolean;
  data?: number[];
  labels?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  data: () => [],
  labels: () => [],
});

const chartRef = ref();
const selectedPeriod = ref("1y");

// Mock data - replace with real data from props or API
const mockData = {
  "6m": {
    labels: [
      "Jul 2024",
      "Aug 2024",
      "Sep 2024",
      "Oct 2024",
      "Nov 2024",
      "Dec 2024",
    ],
    data: [45000, 52000, 48000, 61000, 58000, 67000],
  },
  "1y": {
    labels: [
      "Jan 2024",
      "Feb 2024",
      "Mar 2024",
      "Apr 2024",
      "May 2024",
      "Jun 2024",
      "Jul 2024",
      "Aug 2024",
      "Sep 2024",
      "Oct 2024",
      "Nov 2024",
      "Dec 2024",
    ],
    data: [
      42000, 38000, 45000, 51000, 47000, 53000, 45000, 52000, 48000, 61000,
      58000, 67000,
    ],
  },
  "2y": {
    labels: [
      "Jan 2023",
      "Mar 2023",
      "May 2023",
      "Jul 2023",
      "Sep 2023",
      "Nov 2023",
      "Jan 2024",
      "Mar 2024",
      "May 2024",
      "Jul 2024",
      "Sep 2024",
      "Nov 2024",
    ],
    data: [
      35000, 32000, 38000, 41000, 39000, 44000, 42000, 45000, 47000, 45000,
      48000, 58000,
    ],
  },
};

const currentData = computed(() => {
  const period = selectedPeriod.value as keyof typeof mockData;
  return mockData[period] || mockData["1y"];
});

const chartData = computed(() => ({
  labels: props.labels.length ? props.labels : currentData.value.labels,
  datasets: [
    {
      label: "Commission Amount",
      data: props.data.length ? props.data : currentData.value.data,
      borderColor: "rgb(25, 118, 210)",
      backgroundColor: "rgba(25, 118, 210, 0.1)",
      borderWidth: 3,
      fill: true,
      tension: 0.4,
      pointBackgroundColor: "rgb(25, 118, 210)",
      pointBorderColor: "#ffffff",
      pointBorderWidth: 2,
      pointRadius: 6,
      pointHoverRadius: 8,
      pointHoverBackgroundColor: "rgb(25, 118, 210)",
      pointHoverBorderColor: "#ffffff",
      pointHoverBorderWidth: 3,
    },
  ],
}));

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: "index" as const,
  },
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      titleColor: "#ffffff",
      bodyColor: "#ffffff",
      borderColor: "rgb(25, 118, 210)",
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: false,
      callbacks: {
        label: (context: any) =>
          `Commission: ${formatCurrency(context.parsed.y)}`,
      },
    },
  },
  scales: {
    x: {
      grid: {
        display: false,
      },
      border: {
        display: false,
      },
      ticks: {
        color: "rgba(0, 0, 0, 0.6)",
        font: {
          size: 12,
          weight: "500",
        },
      },
    },
    y: {
      grid: {
        color: "rgba(0, 0, 0, 0.1)",
        drawBorder: false,
      },
      border: {
        display: false,
      },
      ticks: {
        color: "rgba(0, 0, 0, 0.6)",
        font: {
          size: 12,
          weight: "500",
        },
        callback: (value: any) => formatCurrency(value, true),
      },
    },
  },
}));

// Computed stats
const totalCommissions = computed(() => {
  const data = props.data.length ? props.data : currentData.value.data;
  return data.reduce((sum, value) => sum + value, 0);
});

const avgMonthly = computed(() => {
  const data = props.data.length ? props.data : currentData.value.data;
  return Math.round(totalCommissions.value / data.length);
});

const highestMonth = computed(() => {
  const data = props.data.length ? props.data : currentData.value.data;
  return Math.max(...data);
});

const growthRate = computed(() => {
  const data = props.data.length ? props.data : currentData.value.data;
  if (data.length < 2) return 0;
  const firstHalf = data.slice(0, Math.floor(data.length / 2));
  const secondHalf = data.slice(Math.floor(data.length / 2));
  const firstAvg =
    firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
  const secondAvg =
    secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
  return Math.round(((secondAvg - firstAvg) / firstAvg) * 100);
});

// Utility functions
const formatCurrency = (value: number, short = false) => {
  if (short && value >= 1000) {
    return `$${(value / 1000).toFixed(0)}k`;
  }
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

const exportChart = (format: "png" | "pdf") => {
  if (chartRef.value?.chart) {
    const canvas = chartRef.value.chart.canvas;
    const url = canvas.toDataURL("image/png");
    const link = document.createElement("a");
    link.download = `commission-trends.${format}`;
    link.href = url;
    link.click();
  }
};

// Watch for period changes
watch(selectedPeriod, () => {});

onMounted(() => {
  // Initialize chart or fetch data
});
</script>

<style scoped>
.commission-trends-chart {
  background: rgb(var(--v-theme-surface));
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
}

.chart-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24px;
  gap: 16px;
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin: 0;
  line-height: 1.4;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.period-toggle {
  border-radius: 8px;
}

.chart-container {
  position: relative;
  height: 300px;
  margin-bottom: 24px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 0.02) 0%,
    rgba(var(--v-theme-secondary), 0.02) 100%
  );
}

.chart-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: rgba(var(--v-theme-surface), 0.9);
  backdrop-filter: blur(4px);
  z-index: 10;
}

.loading-text {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
}

.commission-chart {
  height: 100%;
}

.chart-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(var(--v-theme-outline), 0.1);
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: rgba(var(--v-theme-surface-variant), 0.3);
  border-radius: 12px;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(var(--v-theme-primary));
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-change.positive {
  color: rgb(var(--v-theme-success));
}

.stat-change.negative {
  color: rgb(var(--v-theme-error));
}

.stat-change.neutral {
  color: rgba(var(--v-theme-on-surface), 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .commission-trends-chart {
    padding: 16px;
  }

  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .chart-controls {
    justify-content: space-between;
  }

  .chart-container {
    height: 250px;
  }

  .chart-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-value {
    font-size: 1.25rem;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .commission-trends-chart {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}
</style>
