<template>
  <div class="user-profile-section">
    <div class="profile-card" :class="{ 'profile-card--rail': rail }">
      <!-- User Avatar with Status Indicator -->
      <div class="avatar-container">
        <v-avatar :size="rail ? 44 : 64" class="user-avatar" color="primary">
          <v-img v-if="user?.avatar" :src="user.avatar" :alt="user.fullName" />
          <v-icon v-else :size="rail ? 24 : 36" color="white">
            mdi-account
          </v-icon>
        </v-avatar>
        <!-- Online Status Indicator -->
        <div class="status-indicator"></div>
      </div>

      <!-- User Info (hidden when rail mode) -->
      <div v-if="!rail" class="user-info">
        <div class="user-details">
          <div class="user-name">
            {{ displayName }}
          </div>
          <div class="user-email">
            {{ user?.email || "a.galgalo@premier..." }}
          </div>
        </div>

        <!-- Status Badge -->
        <div class="status-section">
          <v-chip
            size="small"
            color="success"
            variant="elevated"
            prepend-icon="mdi-circle-small"
            class="status-chip"
          >
            Online
          </v-chip>
        </div>
      </div>

      <!-- Quick Actions Menu -->
      <div v-if="!rail" class="user-actions">
        <v-menu location="bottom end" offset="12">
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              class="user-menu-btn"
              size="small"
              variant="text"
              icon
            >
              <div class="menu-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
              </div>
            </v-btn>
          </template>

          <v-card class="user-menu-card" elevation="8">
            <v-list density="comfortable" class="user-menu-list">
              <v-list-item
                prepend-icon="mdi-account-edit"
                title="Edit Profile"
                subtitle="Update your information"
                class="menu-item"
                @click="$emit('edit-profile')"
              >
                <template #prepend>
                  <v-icon color="primary" size="20">mdi-account-edit</v-icon>
                </template>
              </v-list-item>

              <v-divider class="my-2" />

              <v-list-item
                prepend-icon="mdi-logout"
                title="Logout"
                subtitle="Sign out of your account"
                class="menu-item menu-item--logout"
                @click="$emit('logout')"
              >
                <template #prepend>
                  <v-icon color="error" size="20">mdi-logout</v-icon>
                </template>
              </v-list-item>
            </v-list>
          </v-card>
        </v-menu>
      </div>
    </div>

    <!-- Rail Mode Tooltip -->
    <v-tooltip
      v-if="rail"
      activator="parent"
      location="end"
      :text="displayName"
    />
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/auth";

defineProps<{
  rail?: boolean;
}>();

defineEmits<{
  "edit-profile": [];
  logout: [];
}>();

const authStore = useAuthStore();
const user = computed(() => authStore.user);

const displayName = computed(() => {
  if (user.value?.fullName) {
    return user.value.fullName;
  }
  return "User";
});
</script>

<style scoped>
.user-profile-section {
  padding: 20px 16px;
  position: relative;
}

.profile-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 0.08) 0%,
    rgba(var(--v-theme-secondary), 0.05) 100%
  );
  border-radius: 20px;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.profile-card:hover {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 0.12) 0%,
    rgba(var(--v-theme-secondary), 0.08) 100%
  );
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--v-theme-primary), 0.15);
}

.profile-card--rail {
  justify-content: center;
  padding: 12px;
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.user-avatar {
  border: 3px solid rgba(var(--v-theme-surface), 0.9);
  box-shadow: 0 4px 15px rgba(var(--v-theme-primary), 0.2);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  border-color: rgb(var(--v-theme-primary));
  box-shadow: 0 6px 20px rgba(var(--v-theme-primary), 0.3);
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  border: 2px solid rgb(var(--v-theme-surface));
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
  }
  50% {
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.8);
  }
  100% {
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
  }
}

.user-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: rgb(var(--v-theme-on-surface));
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.user-email {
  font-size: 0.8rem;
  color: rgba(var(--v-theme-on-surface), 0.6);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.status-section {
  display: flex;
  align-items: center;
}

.status-chip {
  font-size: 0.7rem !important;
  height: 22px !important;
  font-weight: 600 !important;
  border-radius: 11px !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.status-chip :deep(.v-chip__prepend) {
  margin-inline-end: 4px;
}

.user-actions {
  flex-shrink: 0;
}

.user-menu-btn {
  background: rgba(var(--v-theme-surface), 0.7) !important;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  transition: all 0.3s ease;
  border-radius: 10px !important;
  width: 36px !important;
  height: 36px !important;
}

.user-menu-btn:hover {
  background: rgba(var(--v-theme-primary), 0.1) !important;
  border-color: rgba(var(--v-theme-primary), 0.2);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(var(--v-theme-primary), 0.15);
}

.menu-dots {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
  justify-content: center;
}

.dot {
  width: 3px;
  height: 3px;
  background: rgba(var(--v-theme-on-surface), 0.6);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.user-menu-btn:hover .dot {
  background: rgb(var(--v-theme-primary));
  transform: scale(1.3);
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .user-profile-section {
    padding: 16px 12px;
  }

  .profile-card {
    padding: 12px;
    border-radius: 16px;
  }

  .user-name {
    font-size: 1rem;
  }

  .user-email {
    font-size: 0.75rem;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .profile-card {
    background: linear-gradient(
      135deg,
      rgba(var(--v-theme-primary), 0.1) 0%,
      rgba(var(--v-theme-secondary), 0.06) 100%
    );
    border-color: rgba(var(--v-theme-outline), 0.15);
  }

  .user-avatar {
    border-color: rgba(var(--v-theme-surface), 0.8);
  }
}

.user-menu-card {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.user-menu-list {
  padding: 8px;
  background: rgba(var(--v-theme-surface), 0.95);
}

.menu-item {
  border-radius: 8px;
  margin: 2px 0;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background: rgba(var(--v-theme-primary), 0.08);
  transform: translateX(2px);
}

.menu-item--logout:hover {
  background: rgba(var(--v-theme-error), 0.08);
}

.menu-item :deep(.v-list-item__content) {
  padding: 4px 0;
}

.menu-item :deep(.v-list-item-title) {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.2;
}

.menu-item :deep(.v-list-item-subtitle) {
  font-size: 0.75rem;
  opacity: 0.7;
  line-height: 1.2;
}

.user-actions {
  flex-shrink: 0;
}

/* Animation for status indicator */
.status-chip :deep(.v-icon) {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
