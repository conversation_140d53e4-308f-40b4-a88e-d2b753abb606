<template>
  <v-list-item
    :value="item.key"
    :prepend-icon="item.icon"
    :title="item.label"
    :active="isActive"
    class="modern-nav-item"
    :class="{ 
      'nav-item-rail': rail,
      'nav-item-active': isActive,
      'nav-item-logout': item.isAction
    }"
    rounded="xl"
    @click="$emit('click')"
  >
    <!-- Custom Icon with Active State -->
    <template #prepend>
      <div class="nav-icon-wrapper">
        <v-icon 
          :color="isActive ? 'primary' : 'default'"
          :class="{ 'nav-icon-active': isActive }"
        >
          {{ item.icon }}
        </v-icon>
      </div>
    </template>

    <!-- Badge for notifications -->
    <template v-if="item.badge && !rail" #append>
      <div class="nav-badge-container">
        <v-chip
          :text="item.badge"
          size="x-small"
          color="error"
          variant="flat"
          class="nav-badge"
        />
      </div>
    </template>

    <!-- Tooltip for rail mode -->
    <v-tooltip
      v-if="rail"
      activator="parent"
      location="end"
      :text="item.label"
    >
      <div class="tooltip-content">
        <div class="font-weight-medium">{{ item.label }}</div>
        <div v-if="item.description" class="text-caption opacity-80">
          {{ item.description }}
        </div>
        <v-badge
          v-if="item.badge"
          :content="item.badge"
          color="error"
          inline
          class="mt-1"
        />
      </div>
    </v-tooltip>

    <!-- Subtitle for expanded mode -->
    <template v-if="!rail && item.description" #subtitle>
      <span class="nav-subtitle">{{ item.description }}</span>
    </template>

    <!-- Title with better typography -->
    <template #title>
      <span class="nav-title">{{ item.label }}</span>
    </template>
  </v-list-item>
</template>

<script setup lang="ts">
defineProps<{
  item: {
    key: string
    label: string
    icon: string
    badge?: string | null
    description?: string
    isAction?: boolean
  }
  rail?: boolean
  isActive?: boolean
}>()

defineEmits<{
  'click': []
}>()
</script>

<style scoped>
.modern-nav-item {
  margin: 4px 8px;
  border-radius: 12px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(25, 118, 210, 0.1) 0%,
    rgba(255, 107, 53, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.modern-nav-item:hover::before {
  opacity: 1;
}

.modern-nav-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}

.nav-item-active {
  background: linear-gradient(135deg,
    rgba(25, 118, 210, 0.12) 0%,
    rgba(255, 107, 53, 0.12) 100%) !important;
  border-left: 3px solid rgb(var(--v-theme-primary));
}

.nav-item-active::before {
  opacity: 1;
}

.nav-item-logout {
  margin-top: 16px;
  border-top: 1px solid rgba(var(--v-theme-outline), 0.2);
  padding-top: 8px;
}

.nav-item-logout:hover {
  background: rgba(var(--v-theme-error), 0.1) !important;
  border-left-color: rgb(var(--v-theme-error));
}

.nav-item-logout:hover .nav-icon-wrapper .v-icon {
  color: rgb(var(--v-theme-error)) !important;
}

.nav-icon-wrapper {
  position: relative;
  z-index: 1;
  transition: transform 0.2s ease;
}

.nav-icon-active {
  transform: scale(1.1);
}

.modern-nav-item:hover .nav-icon-wrapper {
  transform: scale(1.05);
}

.nav-badge-container {
  display: flex;
  align-items: center;
  z-index: 1;
}

.nav-badge {
  font-size: 0.65rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  animation: badgeGlow 2s ease-in-out infinite;
}

@keyframes badgeGlow {
  0%, 100% {
    box-shadow: 0 0 4px rgba(var(--v-theme-error), 0.3);
  }
  50% {
    box-shadow: 0 0 8px rgba(var(--v-theme-error), 0.6);
  }
}

.nav-title {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.3;
  color: rgb(var(--v-theme-on-surface));
}

.nav-item-active .nav-title {
  font-weight: 600;
  color: rgb(var(--v-theme-primary));
}

.nav-subtitle {
  font-size: 0.75rem;
  opacity: 0.7;
  line-height: 1.2;
  font-weight: 400;
}

.nav-item-rail {
  justify-content: center;
  min-height: 48px;
}

.tooltip-content {
  max-width: 200px;
}

/* Ripple effect enhancement */
.modern-nav-item :deep(.v-ripple__container) {
  border-radius: 12px;
}

/* Active state animation */
.nav-item-active .nav-icon-wrapper {
  animation: activeIcon 0.6s ease-out;
}

@keyframes activeIcon {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1.1); }
}

/* Hover state for rail mode */
.nav-item-rail:hover {
  transform: scale(1.05);
}
</style>
