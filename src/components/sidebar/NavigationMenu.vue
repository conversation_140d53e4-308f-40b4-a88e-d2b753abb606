<template>
  <div class="navigation-section">
    <!-- Main Navigation -->
    <div class="nav-container">
      <!-- Dashboard -->
      <div class="nav-group">
        <div
          class="nav-item"
          :class="{ 'nav-item--active': isActiveRoute('/dashboard') }"
          @click="handleItemClick({ key: '/dashboard' })"
        >
          <div class="nav-item__content">
            <div class="nav-item__icon-wrapper">
              <v-icon class="nav-item__icon" size="20"
                >mdi-view-dashboard-variant</v-icon
              >
            </div>
            <div v-if="!rail" class="nav-item__text">
              <span class="nav-item__title">Dashboard</span>
              <span class="nav-item__subtitle">Overview & Analytics</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Commissions Group -->
      <div class="nav-group">
        <div
          class="nav-item nav-item--expandable"
          :class="{
            'nav-item--active': isCommissionsActive,
            'nav-item--expanded': commissionsExpanded,
          }"
          @click="toggleCommissions"
        >
          <div class="nav-item__content">
            <div class="nav-item__icon-wrapper">
              <v-icon class="nav-item__icon" size="20"
                >mdi-calculator-variant</v-icon
              >
            </div>
            <div v-if="!rail" class="nav-item__text">
              <span class="nav-item__title">Commissions</span>
              <span class="nav-item__subtitle">Calculate & Manage</span>
            </div>
            <v-icon
              v-if="!rail"
              class="nav-item__expand-icon"
              :class="{ 'nav-item__expand-icon--rotated': commissionsExpanded }"
              size="16"
            >
              mdi-chevron-down
            </v-icon>
          </div>
        </div>

        <!-- Commissions Submenu -->
        <v-expand-transition>
          <div v-if="commissionsExpanded && !rail" class="nav-submenu">
            <div
              class="nav-subitem"
              :class="{ 'nav-subitem--active': isActiveRoute('/calculator') }"
              @click="handleItemClick({ key: '/calculator' })"
            >
              <div class="nav-subitem__content">
                <div class="nav-subitem__indicator"></div>
                <v-icon class="nav-subitem__icon" size="16"
                  >mdi-calculator</v-icon
                >
                <span class="nav-subitem__title">Calculator</span>
                <v-chip
                  v-if="!rail"
                  size="x-small"
                  color="success"
                  variant="elevated"
                  class="nav-subitem__badge"
                >
                  NEW
                </v-chip>
              </div>
            </div>
          </div>
        </v-expand-transition>
      </div>

      <!-- Other Navigation Items -->
      <div class="nav-group">
        <div
          v-for="item in otherNavigationItems"
          :key="item.key"
          class="nav-item"
          :class="{ 'nav-item--active': isActiveRoute(item.key) }"
          @click="handleItemClick(item)"
        >
          <div class="nav-item__content">
            <div class="nav-item__icon-wrapper">
              <v-icon class="nav-item__icon" size="20">{{ item.icon }}</v-icon>
            </div>
            <div v-if="!rail" class="nav-item__text">
              <span class="nav-item__title">{{ item.label }}</span>
              <span class="nav-item__subtitle">{{ item.description }}</span>
            </div>
            <v-chip
              v-if="item.badge && !rail"
              size="x-small"
              :color="item.badgeColor || 'primary'"
              variant="elevated"
              class="nav-item__badge"
            >
              {{ item.badge }}
            </v-chip>
          </div>
        </div>
      </div>

      <!-- Logout -->
      <div class="nav-group nav-group--bottom">
        <div
          class="nav-item nav-item--logout"
          @click="handleItemClick({ key: 'logout', isAction: true })"
        >
          <div class="nav-item__content">
            <div class="nav-item__icon-wrapper">
              <v-icon class="nav-item__icon" size="20">mdi-logout</v-icon>
            </div>
            <div v-if="!rail" class="nav-item__text">
              <span class="nav-item__title">Logout</span>
              <span class="nav-item__subtitle">Sign out safely</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  rail?: boolean;
}>();

const emit = defineEmits<{
  logout: [];
}>();

const route = useRoute();
const router = useRouter();

// Commissions dropdown state
const commissionsExpanded = ref(false);

// Other navigation items (excluding dashboard and commissions)
const otherNavigationItems = [
  {
    key: "/users",
    label: "Users",
    icon: "mdi-account-group-outline",
    badge: null,
    description: "User management",
  },
  {
    key: "/clients",
    label: "Clients",
    icon: "mdi-account-multiple-outline",
    badge: null,
    description: "Client accounts",
  },
  {
    key: "/reports",
    label: "Reports",
    icon: "mdi-chart-line",
    badge: null,
    description: "Analytics and reports",
    badgeColor: "info",
  },
  {
    key: "/settings",
    label: "Settings",
    icon: "mdi-cog-outline",
    badge: null,
    description: "Application settings",
  },
];

// Computed properties
const isCommissionsActive = computed(() => {
  return route.path === "/calculator" || route.path.startsWith("/calculator/");
});

// Auto-expand commissions if we're on a commissions-related route
watchEffect(() => {
  if (isCommissionsActive.value) {
    commissionsExpanded.value = true;
  }
});

const isActiveRoute = (key: string) => {
  if (key === "logout") return false;
  return route.path === key || route.path.startsWith(key + "/");
};

const toggleCommissions = () => {
  commissionsExpanded.value = !commissionsExpanded.value;
};

const handleItemClick = (item: any) => {
  if (item.key === "logout") {
    emit("logout");
  } else if (!item.isAction) {
    router.push(item.key);
  }
};
</script>

<style scoped>
.navigation-section {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.nav-group--bottom {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid rgba(var(--v-theme-outline), 0.1);
}

/* Main Navigation Items */
.nav-item {
  position: relative;
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  background: transparent;
  margin-bottom: 4px;
}

.nav-item:hover {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 0.1) 0%,
    rgba(var(--v-theme-primary), 0.05) 100%
  );
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(var(--v-theme-primary), 0.1);
}

.nav-item--active {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 0.18) 0%,
    rgba(var(--v-theme-primary), 0.1) 100%
  );
  border: 1px solid rgba(var(--v-theme-primary), 0.25);
  box-shadow: 0 6px 20px rgba(var(--v-theme-primary), 0.15);
}

.nav-item--active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  background: linear-gradient(
    180deg,
    rgb(var(--v-theme-primary)) 0%,
    rgba(var(--v-theme-primary), 0.8) 100%
  );
  border-radius: 0 3px 3px 0;
  box-shadow: 2px 0 8px rgba(var(--v-theme-primary), 0.3);
}

.nav-item--logout {
  background: rgba(var(--v-theme-error), 0.05);
  border: 1px solid rgba(var(--v-theme-error), 0.1);
}

.nav-item--logout:hover {
  background: rgba(var(--v-theme-error), 0.1);
  transform: translateX(2px);
}

.nav-item__content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
  position: relative;
}

.nav-item__icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-surface-variant), 0.6) 0%,
    rgba(var(--v-theme-surface-variant), 0.3) 100%
  );
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  border: 1px solid rgba(var(--v-theme-outline), 0.08);
}

.nav-item:hover .nav-item__icon-wrapper {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 0.15) 0%,
    rgba(var(--v-theme-primary), 0.08) 100%
  );
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(var(--v-theme-primary), 0.2);
}

.nav-item--active .nav-item__icon-wrapper {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-primary), 0.2) 0%,
    rgba(var(--v-theme-primary), 0.12) 100%
  );
  border-color: rgba(var(--v-theme-primary), 0.3);
  box-shadow: 0 6px 20px rgba(var(--v-theme-primary), 0.25);
  transform: scale(1.02);
}

.nav-item--logout .nav-item__icon-wrapper {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-error), 0.12) 0%,
    rgba(var(--v-theme-error), 0.06) 100%
  );
  border-color: rgba(var(--v-theme-error), 0.15);
}

.nav-item--logout:hover .nav-item__icon-wrapper {
  background: linear-gradient(
    135deg,
    rgba(var(--v-theme-error), 0.18) 0%,
    rgba(var(--v-theme-error), 0.1) 100%
  );
  box-shadow: 0 4px 15px rgba(var(--v-theme-error), 0.2);
}

.nav-item__icon {
  color: rgba(var(--v-theme-on-surface), 0.7);
  transition: all 0.3s ease;
}

.nav-item:hover .nav-item__icon {
  color: rgb(var(--v-theme-primary));
  transform: scale(1.1);
}

.nav-item--active .nav-item__icon {
  color: rgb(var(--v-theme-primary));
  transform: scale(1.05);
}

.nav-item--logout .nav-item__icon {
  color: rgb(var(--v-theme-error));
}

.nav-item--logout:hover .nav-item__icon {
  transform: scale(1.1);
}

.nav-item__text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.nav-item__title {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
  line-height: 1.2;
}

.nav-item__subtitle {
  font-size: 0.75rem;
  color: rgba(var(--v-theme-on-surface), 0.6);
  line-height: 1.2;
}

.nav-item__badge {
  margin-left: auto;
  font-size: 0.7rem !important;
  height: 20px !important;
  min-width: 20px !important;
}

.nav-item__expand-icon {
  margin-left: auto;
  color: rgba(var(--v-theme-on-surface), 0.5);
  transition: all 0.3s ease;
}

.nav-item__expand-icon--rotated {
  transform: rotate(180deg);
}

/* Submenu Styles */
.nav-submenu {
  margin-left: 24px;
  padding-left: 16px;
  border-left: 2px solid rgba(var(--v-theme-primary), 0.1);
  margin-top: 4px;
  margin-bottom: 8px;
}

.nav-subitem {
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 2px;
}

.nav-subitem:hover {
  background: rgba(var(--v-theme-primary), 0.05);
}

.nav-subitem--active {
  background: rgba(var(--v-theme-primary), 0.1);
}

.nav-subitem__content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
}

.nav-subitem__indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(var(--v-theme-on-surface), 0.3);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.nav-subitem--active .nav-subitem__indicator {
  background: rgb(var(--v-theme-primary));
  box-shadow: 0 0 8px rgba(var(--v-theme-primary), 0.4);
}

.nav-subitem__icon {
  color: rgba(var(--v-theme-on-surface), 0.6);
  flex-shrink: 0;
}

.nav-subitem--active .nav-subitem__icon {
  color: rgb(var(--v-theme-primary));
}

.nav-subitem__title {
  font-size: 0.85rem;
  font-weight: 500;
  color: rgb(var(--v-theme-on-surface));
  flex: 1;
}

.nav-subitem__badge {
  margin-left: auto;
  font-size: 0.65rem !important;
  height: 18px !important;
  min-width: 18px !important;
}

/* Scrollbar styling */
.navigation-section::-webkit-scrollbar {
  width: 4px;
}

.navigation-section::-webkit-scrollbar-track {
  background: transparent;
}

.navigation-section::-webkit-scrollbar-thumb {
  background: rgba(var(--v-theme-on-surface), 0.15);
  border-radius: 2px;
}

.navigation-section::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--v-theme-on-surface), 0.25);
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .navigation-section {
    padding: 12px;
  }

  .nav-item__content {
    padding: 10px 12px;
  }

  .quick-stats {
    margin-top: 12px;
    padding: 12px;
  }
}

/* Animation for expand/collapse */
.nav-item--expandable .nav-item__content {
  position: relative;
}

.nav-item--expanded {
  background: rgba(var(--v-theme-primary), 0.05);
}

/* Smooth transitions */
* {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
