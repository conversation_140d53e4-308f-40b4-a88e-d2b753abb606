<template>
	<!-- <v-card class="mx-auto" max-width="448"> -->

	<v-app-bar
		color="background"
		elevation="0"
		density="comfortable"
		class="modern-header"
		style="border-bottom: 1px solid rgba(0,0,0,0.08);"
	>
		<v-app-bar-nav-icon
			v-if="isAuthenticated"
			@click="toggleDrawer"
			class="ml-2"
			color="primary"
		/>

		<div class="d-flex align-center">
			<div class="logo-container">
				<v-icon color="primary" size="28" class="mr-3">mdi-chart-line-variant</v-icon>
			</div>
			<div class="brand-section">
				<v-app-bar-title class="brand-title">
					CommissionPro
				</v-app-bar-title>
				<div class="brand-subtitle">
					Smart Commission Management
				</div>
			</div>
		</div>

		<v-spacer />

		<!-- User Profile Section -->
		<div v-if="isAuthenticated" class="d-flex align-center">
			<v-menu offset-y>
				<template v-slot:activator="{ props }">
					<v-btn
						v-bind="props"
						variant="text"
						class="text-none mr-2"
						color="primary"
					>
						<v-avatar size="32" class="mr-2">
							<v-icon>mdi-account-circle</v-icon>
						</v-avatar>
						<span class="d-none d-sm-inline">{{ user?.fullName || 'User' }}</span>
						<v-icon size="16" class="ml-1">mdi-chevron-down</v-icon>
					</v-btn>
				</template>

				<v-list class="modern-menu">
					<v-list-item
						v-for="item in userMenuItems"
						:key="item.key"
						:to="item.key === 'logout' ? '' : item.key"
						@click="item.key === 'logout' ? handleLogout() : null"
						:prepend-icon="item.icon"
					>
						<v-list-item-title>{{ item.label }}</v-list-item-title>
					</v-list-item>
				</v-list>
			</v-menu>
		</div>

	</v-app-bar>


	<!-- </v-card> -->
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';

const props = defineProps<{
	toggleSidebar: () => void
}>()

const router = useRouter()
const authStore = useAuthStore()
const { isAuthenticated, user } = storeToRefs(authStore)

const userMenuItems = [
	{
		key: '/profile',
		label: 'Profile',
		icon: 'mdi-account-outline'
	},
	{
		key: '/settings',
		label: 'Settings',
		icon: 'mdi-cog-outline'
	},
	{
		key: 'logout',
		label: 'Logout',
		icon: 'mdi-logout'
	}
]

const toggleDrawer = () => {
	props.toggleSidebar()
}

const handleLogout = async () => {
	await authStore.logout()
	router.push('/auth/login')
}
</script>

<style scoped>
.modern-header {
	backdrop-filter: blur(10px);
	background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.logo-container {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 44px;
	height: 44px;
	background: linear-gradient(135deg, rgb(25, 118, 210) 0%, rgb(33, 150, 243) 100%);
	border-radius: 12px;
	box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
	transition: all 0.3s ease;
}

.logo-container:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

.logo-container .v-icon {
	color: white !important;
}

.brand-section {
	margin-left: 16px;
}

.brand-title {
	font-size: 1.25rem !important;
	font-weight: 700 !important;
	color: rgb(25, 118, 210) !important;
	line-height: 1.2 !important;
	letter-spacing: -0.02em;
	background: linear-gradient(135deg, rgb(25, 118, 210) 0%, rgb(33, 150, 243) 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.brand-subtitle {
	font-size: 0.75rem;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.6);
	line-height: 1;
	margin-top: 2px;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}
.modern-menu {
	border-radius: 12px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-menu .v-list-item {
	border-radius: 8px;
	margin: 4px 8px;
	transition: all 0.2s ease;
}

.modern-menu .v-list-item:hover {
	background: rgba(25, 118, 210, 0.08);
	transform: translateX(4px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
	.modern-header {
		background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
	}

	.brand-subtitle {
		color: rgba(255, 255, 255, 0.7);
	}
}

/* Responsive design */

@media (max-width: 600px) {
	.brand-title {
		font-size: 1.1rem !important;
	}

	.brand-subtitle {
		font-size: 0.7rem;
	}

	.logo-container {
		width: 40px;
		height: 40px;
	}

	.brand-section {
		margin-left: 12px;
	}
}
</style>