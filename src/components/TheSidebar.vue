<template>
  <v-navigation-drawer
    v-model="drawerLocal"
    :rail="rail"
    width="280"
    color="surface"
    permanent
    class="modern-sidebar"
    elevation="1"
  >
    <!-- User Profile Section -->
    <UserProfileSection
      :rail="rail"
      @logout="handleLogout"
      @edit-profile="handleEditProfile"
    />

    <v-divider class="sidebar-divider" />

    <!-- Navigation Menu -->
    <NavigationMenu :rail="rail" @logout="handleLogout" />
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/auth";
import UserProfileSection from "./sidebar/UserProfileSection.vue";
import NavigationMenu from "./sidebar/NavigationMenu.vue";

const props = defineProps<{
  modelValue: boolean;
  rail?: boolean;
}>();

const emit = defineEmits<{
  "update:modelValue": [value: boolean];
}>();

const router = useRouter();
const authStore = useAuthStore();

const drawerLocal = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

const rail = computed(() => props.rail);

const handleLogout = async () => {
  await authStore.logout();
  router.push("/auth/login");
};

const handleEditProfile = () => {
  router.push("/profile");
};
</script>

<style scoped>
.modern-sidebar {
  border-right: 1px solid rgba(var(--v-theme-outline), 0.08);
  background: linear-gradient(
    180deg,
    rgba(var(--v-theme-surface), 0.95) 0%,
    rgba(var(--v-theme-surface-variant), 0.4) 50%,
    rgba(var(--v-theme-primary), 0.02) 100%
  );
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .modern-sidebar {
    width: 100% !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modern-sidebar {
    background: linear-gradient(
      180deg,
      rgb(var(--v-theme-surface)) 0%,
      rgba(var(--v-theme-surface-variant), 0.2) 100%
    );
    border-right-color: rgba(var(--v-theme-outline), 0.2);
  }
}

/* Sidebar divider */
.sidebar-divider {
  margin: 8px 16px;
  opacity: 0.3;
}

/* Smooth animations */
.modern-sidebar * {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
