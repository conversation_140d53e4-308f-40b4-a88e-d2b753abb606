<template>
	<v-col :cols="column">
		<v-btn color="primary" depressed @click="headBack()">
			<v-icon left> mdi-backburger </v-icon>
			Back
		</v-btn>
	</v-col>
</template>

<script>
export default {
	name: 'TheBack',
	props: {
		pathname: {
			type: String,
			default: 'admin',
		},
		column: {
			type: Number,
			default: 6,
		},
	},
	methods: {
		headBack() {
			this.$router.push({ name: this.pathname });
		},
	},
};
</script>
