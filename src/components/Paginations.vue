<template>
	<v-row class="text-center px-3 py-3 align-center" wrap>
		<v-col class="text-truncate" cols="12" md="2">
			Total {{ totalPages }} pages
		</v-col>

		<v-col cols="12" md="6">
			<v-pagination v-model="page" :length="totalPages" :total-visible="7" next-icon="mdi-menu-right"
				prev-icon="mdi-menu-left" @update:model-value="handlePageChange"></v-pagination>
		</v-col>

		<v-col cols="4" sm="3">
			<v-select v-model="pageSize" :items="pageSizes" label="Items per Page" dense outlined hide-details
				@update:model-value="handlePageSizeChange"></v-select>
		</v-col>

		<v-col cols="6" md="1">
			<v-text-field v-model="page" label="Go to page" type="number" outlined hide-details dense
				@update:model-value="handleGotoPage" min="1"></v-text-field>
		</v-col>
	</v-row>
</template>

<script setup>
import { ref, watch } from 'vue';

// Props
const props = defineProps({
	totalPages: {
		type: Number,
		required: true,
	},
	currentPage: {
		type: Number,
		required: true,
	},
	limit: {
		type: Number,
		required: true,
	}
});

// Emits
const emit = defineEmits(['pageSizeChange']);

// Reactive variables and constants
const pageSizes = ref([10, 20, 50, 100, 200, 300, 500, 600, 700, 800, 900, 1000]);
const pageSize = ref(props.limit || 10);
const page = ref(props.currentPage || 1);

// Methods
const handlePageChange = (val) => {
	page.value = val;
	emit('pageSizeChange', { pageSize: pageSize.value, page: page.value });
};

const handlePageSizeChange = (size) => {
	pageSize.value = size;
	emit('pageSizeChange', { pageSize: pageSize.value, page: page.value });
};

const handleGotoPage = ($event) => {
	page.value = parseInt($event, 10);
	emit('pageSizeChange', { pageSize: pageSize.value, page: page.value });
};

// Watches
watch(
	() => props.currentPage,
	(newVal) => {
		if (newVal) {
			page.value = parseInt(newVal);
		}
	},
	{ immediate: true }
);

watch(
	() => props.limit,
	(newVal) => {
		if (newVal) {
			pageSize.value = parseInt(newVal);
		}
	},
	{ immediate: true }
);
</script>