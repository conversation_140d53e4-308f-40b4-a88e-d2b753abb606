<template>
  <div class="page-header">
    <!-- Main Header Section -->
    <div class="header-main">
      <div class="header-content">
        <!-- Title Section -->
        <div class="title-section">
          <div v-if="icon" class="header-icon">
            <v-icon :icon="icon" size="32" color="primary" />
          </div>

          <div class="title-content">
            <h1 v-if="title" class="page-title">
              {{ title }}
            </h1>
            <p v-if="subtitle" class="page-subtitle">
              {{ subtitle }}
            </p>
          </div>
        </div>

        <!-- Actions Section -->
        <div class="header-actions">
          <slot name="actions" />
          <slot />
        </div>
      </div>
    </div>

    <!-- Breadcrumbs Section (if provided) -->
    <div v-if="breadcrumbs && breadcrumbs.length" class="breadcrumbs-section">
      <v-breadcrumbs :items="breadcrumbs" class="pa-0">
        <template #prepend>
          <v-icon icon="mdi-home" size="16" />
        </template>
        <template #divider>
          <v-icon icon="mdi-chevron-right" size="16" />
        </template>
      </v-breadcrumbs>
    </div>

    <!-- Stats Section (if provided) -->
    <div v-if="stats && stats.length" class="stats-section">
      <div class="stats-grid">
        <div
          v-for="stat in stats"
          :key="stat.label"
          class="stat-card"
        >
          <div class="stat-icon">
            <v-icon :icon="stat.icon" :color="stat.color" size="20" />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Breadcrumb {
  title: string
  to?: string
  disabled?: boolean
}

interface Stat {
  label: string
  value: string | number
  icon: string
  color?: string
}

defineProps<{
  title?: string
  subtitle?: string
  icon?: string
  breadcrumbs?: Breadcrumb[]
  stats?: Stat[]
}>()
</script>

<style scoped>
.page-header {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(var(--v-theme-outline), 0.08);
  margin-bottom: 24px;
}

.header-main {
  padding: 24px 32px 16px;
}

.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 24px;
}

.title-section {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg,
    rgba(var(--v-theme-primary), 0.1) 0%,
    rgba(var(--v-theme-secondary), 0.1) 100%);
  border-radius: 16px;
  border: 1px solid rgba(var(--v-theme-primary), 0.2);
}

.title-content {
  flex: 1;
  min-width: 0;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  color: rgb(var(--v-theme-on-surface));
  margin: 0 0 8px 0;
  background: linear-gradient(135deg,
    rgb(var(--v-theme-primary)) 0%,
    rgb(var(--v-theme-secondary)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.4;
  color: rgba(var(--v-theme-on-surface), 0.7);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.breadcrumbs-section {
  padding: 0 32px 8px;
  border-bottom: 1px solid rgba(var(--v-theme-outline), 0.05);
}

.stats-section {
  padding: 16px 32px;
  background: rgba(var(--v-theme-surface-variant), 0.3);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(var(--v-theme-surface), 0.8);
  border-radius: 12px;
  border: 1px solid rgba(var(--v-theme-outline), 0.1);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--v-theme-shadow), 0.15);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(var(--v-theme-primary), 0.1);
  border-radius: 10px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: rgb(var(--v-theme-on-surface));
  line-height: 1.2;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), 0.7);
  line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-main {
    padding: 16px 20px 12px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .title-section {
    gap: 12px;
  }

  .header-icon {
    width: 48px;
    height: 48px;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .breadcrumbs-section,
  .stats-section {
    padding-left: 20px;
    padding-right: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .page-header {
    background: linear-gradient(135deg,
      rgba(var(--v-theme-surface), 0.95) 0%,
      rgba(var(--v-theme-surface-variant), 0.3) 100%);
    border-bottom-color: rgba(var(--v-theme-outline), 0.2);
  }

  .stat-card {
    background: rgba(var(--v-theme-surface-variant), 0.5);
    border-color: rgba(var(--v-theme-outline), 0.2);
  }
}
</style>
