<template>
  <v-dialog v-model="showDialog" max-width="900px" persistent class="modern-dialog">
    <v-card class="card-modern" elevation="0">
      <div class="dialog-header">
        <div class="d-flex align-center">
          <v-icon size="32" color="primary" class="mr-3">mdi-account-plus</v-icon>
          <div>
            <h2 class="text-h5 font-weight-bold text-primary">Create New User</h2>
            <p class="text-body-2 text-medium-emphasis mt-1">Add a new user to the commission system</p>
          </div>
        </div>
        <div class="header-actions">
          <v-btn
            variant="text"
            @click="handleSubmit"
            :loading="loading"
            color="primary"
            class="btn-modern mr-2"
            size="large"
          >
            <v-icon start>mdi-content-save</v-icon>
            Save User
          </v-btn>
          <v-btn
            icon
            variant="text"
            @click="close"
            class="ml-2"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
      </div>

      <v-card-text class="pa-8">
        <v-form ref="formRef" @submit.prevent="handleSubmit">
          <!-- Basic Information Section -->
          <div class="form-section mb-8">
            <div class="section-header mb-6">
              <h3 class="text-h6 font-weight-medium text-primary">
                <v-icon class="mr-2">mdi-account-outline</v-icon>
                Basic Information
              </h3>
              <p class="text-body-2 text-medium-emphasis">
                Enter the user's basic account details
              </p>
            </div>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.email"
                  label="Email Address"
                  type="email"
                  :rules="emailRules"
                  required
                  variant="outlined"
                  class="modern-input"
                  prepend-inner-icon="mdi-email-outline"
                  color="primary"
                  placeholder="<EMAIL>"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.password"
                  label="Password"
                  :rules="passwordRules"
                  required
                  variant="outlined"
                  class="modern-input"
                  prepend-inner-icon="mdi-lock-outline"
                  :append-inner-icon="show1 ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                  @click:append-inner="show1 = !show1"
                  :type="show1 ? 'text' : 'password'"
                  color="primary"
                  placeholder="Enter secure password"
                />
              </v-col>
            </v-row>
          </div>

            <v-divider class="my-6"></v-divider>

            <!-- Personal Information -->
            <div class="text-h6 mb-4">Personal Information</div>
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field v-model="form.fullName" label="Full Name"
                  :rules="[v => !!v || 'Full name is required']" required variant="outlined"></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-select
                  v-model="form.tenantId"
                  label="Tenant"
                  :items="tenantOptions"
                  item-title="title"
                  item-value="value"
                  :rules="[v => !!v || 'Tenant is required']"
                  :loading="isLoadingTenants"
                  required
                  variant="outlined"
                  placeholder="Select a tenant..."
                ></v-select>
              </v-col>
            </v-row>

            <v-divider class="my-6"></v-divider>

            <!-- User Settings -->
            <div class="text-h6 mb-4">User Settings</div>
            <v-row>
              <v-col cols="12" md="6">
                <v-checkbox v-model="form.isActive" label="Is Active"></v-checkbox>
              </v-col>
              <v-col cols="12" md="6">
                <v-checkbox v-model="form.acceptTerms" label="I accept the terms and conditions"
                  :rules="[v => !!v || 'You must accept the terms and conditions']" required></v-checkbox>
              </v-col>
            </v-row>

            <v-divider class="my-6"></v-divider>

            <!-- Submit Button -->
            <v-row>
              <v-col cols="12" class="text-right">
                <v-btn color="primary" @click.prevent="handleSubmit">Submit
                  <v-icon icon="mdi-checkbox-marked-circle" end></v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { CreateUserDto } from '@/types/user.dto';
import { useTenantStore } from '@/stores/tenant';
import { storeToRefs } from 'pinia';

const props = defineProps<{
  modelValue: boolean
  loading?: boolean
}>()
const tenantStore = useTenantStore()
const { tenants, isLoading: isLoadingTenants } = storeToRefs(tenantStore)
const emit = defineEmits(['update:modelValue', 'submit'])

const showDialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const show1 = ref(false)
const formRef = ref<any>(null)

const form = ref<CreateUserDto>({
  fullName: 'John Doe',
  email: '<EMAIL>',
  password: 'Password123',
  tenantId: '',
  isActive: true,
  acceptTerms: false
})

// Computed tenant options for the dropdown
const tenantOptions = computed(() => {
  return tenants.value.map(tenant => ({
    title: `${tenant.name} (${tenant.subdomain})`,
    value: tenant.id
  }))
})



const emailRules = [
  (v: string) => !!v || 'Email is required',
  (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid'
]

const passwordRules = [
  (v: string) => !!v || 'Password is required',
  (v: string) => v.length >= 8 || 'Password must be at least 8 characters'
]



const handleSubmit = async () => {
  const { valid } = await formRef.value?.validate()
  if (valid) {
    emit('submit', form.value)
  }
}

const close = () => {
  form.value = {
    fullName: '',
    email: '',
    password: '',
    tenantId: '',
    isActive: true,
    acceptTerms: false
  }
  emit('update:modelValue', false)
}

// Fetch tenants when component mounts
onMounted(() => {
  tenantStore.fetchTenants()
})
</script>

<style scoped>
.modern-dialog :deep(.v-overlay__content) {
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  max-height: 90vh;
  overflow-y: auto;
}

.card-modern {
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 2rem 1rem 2rem;
  background: linear-gradient(135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%);
  border-radius: 16px 16px 0 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.header-actions {
  display: flex;
  align-items: center;
}

.form-section {
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-header h3 {
  background: linear-gradient(135deg, var(--v-theme-primary), var(--v-theme-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-input :deep(.v-field) {
  border-radius: 12px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.8);
}

.modern-input :deep(.v-field:hover) {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.modern-input :deep(.v-field--focused) {
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.25);
  background: rgba(255, 255, 255, 1);
}

.btn-modern {
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3) !important;
  transition: all 0.2s ease !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
}

.btn-modern:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(25, 118, 210, 0.4) !important;
}

/* Date picker styling */
:deep(.dp__input) {
  padding: 8px 12px;
  border-radius: 12px !important;
  border: 1px solid rgba(0, 0, 0, 0.23);
  font-size: 16px;
  width: 100%;
  height: 56px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
}

:deep(.dp__input:hover) {
  border-color: rgba(25, 118, 210, 0.5);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

:deep(.dp__input:focus) {
  border-color: rgb(var(--v-theme-primary));
  border-width: 2px;
  outline: none;
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.25);
  background: rgba(255, 255, 255, 1);
}

:deep(.dp__input_icon) {
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

:deep(.dp__main) {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

/* Custom scrollbar */
.v-card-text::-webkit-scrollbar {
  width: 6px;
}

.v-card-text::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.v-card-text::-webkit-scrollbar-thumb {
  background: rgba(25, 118, 210, 0.3);
  border-radius: 3px;
}

.v-card-text::-webkit-scrollbar-thumb:hover {
  background: rgba(25, 118, 210, 0.5);
}

/* Responsive design */
@media (max-width: 960px) {
  .dialog-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .form-section {
    padding: 1rem;
  }
}

@media (max-width: 600px) {
  .modern-dialog :deep(.v-overlay__content) {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }

  .dialog-header {
    padding: 1.5rem 1rem 1rem 1rem;
  }

  .v-card-text {
    padding: 1rem !important;
  }
}
</style>