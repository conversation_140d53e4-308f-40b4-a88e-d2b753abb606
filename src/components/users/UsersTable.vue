<template>
  <div class="modern-table-container">
    <v-data-table
      :page="currentPage"
      :headers="headers"
      :items="users"
      :loading="loading"
      :items-per-page="itemsPerPage"
      :items-length="totalItems"
      @update:page="$emit('update:currentPage', $event)"
      @update:options="$emit('update:options', $event)"
      class="modern-data-table"
      hover
    >
      <!-- Status Column -->
      <template v-slot:item.isActive="{ item }">
        <v-chip
          :color="item.isActive ? 'success' : 'error'"
          size="small"
          variant="tonal"
          class="modern-chip"
        >
          <v-icon start size="12">
            {{ item.isActive ? "mdi-check-circle" : "mdi-close-circle" }}
          </v-icon>
          {{ item.isActive ? "Active" : "Inactive" }}
        </v-chip>
      </template>

      <!-- Email Verified Column -->
      <template v-slot:item.emailVerified="{ item }">
        <v-chip
          :color="item.emailVerified ? 'info' : 'warning'"
          size="small"
          variant="tonal"
          class="modern-chip"
        >
          <v-icon start size="12">
            {{ item.emailVerified ? "mdi-email-check" : "mdi-email-alert" }}
          </v-icon>
          {{ item.emailVerified ? "Verified" : "Pending" }}
        </v-chip>
      </template>

      <!-- Actions Column -->
      <template v-slot:item.actions="{ item }">
        <div class="action-buttons">
          <v-tooltip text="View User" location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                v-bind="props"
                icon
                size="small"
                variant="text"
                class="action-btn mr-1"
                @click="$emit('view', item)"
                color="info"
              >
                <v-icon size="18">mdi-eye-outline</v-icon>
              </v-btn>
            </template>
          </v-tooltip>

          <v-tooltip text="Edit User" location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                v-bind="props"
                icon
                size="small"
                variant="text"
                class="action-btn mr-1"
                @click="$emit('edit', item)"
                color="primary"
              >
                <v-icon size="18">mdi-pencil-outline</v-icon>
              </v-btn>
            </template>
          </v-tooltip>

          <v-tooltip text="Change Password" location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                v-bind="props"
                icon
                size="small"
                variant="text"
                class="action-btn mr-1"
                @click="$emit('changePassword', item)"
                color="warning"
              >
                <v-icon size="18">mdi-lock-reset</v-icon>
              </v-btn>
            </template>
          </v-tooltip>

          <v-tooltip text="Change Email" location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                v-bind="props"
                icon
                size="small"
                variant="text"
                class="action-btn mr-1"
                @click="$emit('changeEmail', item)"
                color="accent"
              >
                <v-icon size="18">mdi-email-edit-outline</v-icon>
              </v-btn>
            </template>
          </v-tooltip>

          <v-tooltip text="Delete User" location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                v-bind="props"
                icon
                size="small"
                variant="text"
                class="action-btn"
                @click="$emit('delete', item)"
                color="error"
              >
                <v-icon size="18">mdi-delete-outline</v-icon>
              </v-btn>
            </template>
          </v-tooltip>
        </div>
      </template>

      <!-- Loading slot -->
      <template v-slot:loading>
        <div class="loading-container">
          <v-progress-circular
            indeterminate
            color="primary"
            size="48"
          ></v-progress-circular>
          <p class="text-body-1 text-medium-emphasis mt-4">Loading users...</p>
        </div>
      </template>

      <!-- No data slot -->
      <template v-slot:no-data>
        <div class="no-data-container">
          <v-icon size="64" color="medium-emphasis" class="mb-4"
            >mdi-account-search</v-icon
          >
          <h3 class="text-h6 text-medium-emphasis mb-2">No Users Found</h3>
          <p class="text-body-2 text-medium-emphasis">
            Try adjusting your filters or create a new user.
          </p>
        </div>
      </template>
    </v-data-table>
  </div>
</template>

<script setup lang="ts">
import type { User } from "@/types/user";

const props = defineProps<{
  users: User[];
  loading: boolean;
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
}>();

const emit = defineEmits([
  "update:currentPage",
  "update:options",
  "view",
  "edit",
  "changePassword",
  "changeEmail",
  "delete",
]);

const headers = [
  { title: "Full Name", key: "fullName" },
  { title: "Email", key: "email" },
  { title: "Tenant ID", key: "tenantId" },
  { title: "Status", key: "isActive" },
  { title: "Email Verified", key: "emailVerified" },
  { title: "Actions", key: "actions", sortable: false },
];
</script>

<style scoped>
.modern-table-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.modern-data-table {
  background: transparent !important;
}

.modern-data-table :deep(.v-table) {
  background: transparent !important;
}

.modern-data-table :deep(.v-table__wrapper) {
  border-radius: 0 !important;
}

.modern-data-table :deep(.v-data-table-header) {
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%
  ) !important;
}

.modern-data-table :deep(.v-data-table-header th) {
  font-weight: 600 !important;
  color: var(--v-theme-primary) !important;
  border-bottom: 2px solid rgba(25, 118, 210, 0.1) !important;
  padding: 16px !important;
}

.modern-data-table :deep(.v-data-table__tr:hover) {
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.03) 0%,
    rgba(255, 107, 53, 0.03) 100%
  ) !important;
}

.modern-data-table :deep(.v-data-table__td) {
  padding: 16px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.modern-chip {
  font-weight: 500 !important;
  border-radius: 8px !important;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.action-btn:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
}

.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

/* Pagination styling */
.modern-data-table :deep(.v-data-table-footer) {
  background: rgba(248, 250, 252, 0.8) !important;
  border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 16px !important;
}

.modern-data-table :deep(.v-pagination) {
  justify-content: center;
}

.modern-data-table :deep(.v-pagination__item) {
  border-radius: 8px !important;
  margin: 0 2px !important;
  transition: all 0.2s ease !important;
}

.modern-data-table :deep(.v-pagination__item:hover) {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3) !important;
}

.modern-data-table :deep(.v-pagination__item--is-active) {
  background: linear-gradient(
    135deg,
    var(--v-theme-primary),
    var(--v-theme-accent)
  ) !important;
  color: white !important;
}

/* Responsive design */
@media (max-width: 960px) {
  .modern-data-table :deep(.v-data-table__td),
  .modern-data-table :deep(.v-data-table-header th) {
    padding: 12px 8px !important;
    font-size: 0.875rem !important;
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .action-btn {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .modern-table-container {
    margin: 0 -8px;
    border-radius: 12px;
  }

  .modern-data-table :deep(.v-data-table__td),
  .modern-data-table :deep(.v-data-table-header th) {
    padding: 8px 4px !important;
    font-size: 0.8rem !important;
  }
}
</style>
