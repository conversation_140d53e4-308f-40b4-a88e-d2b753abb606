<template>
  <v-chip
    :color="statusColor"
    :text-color="statusTextColor"
    size="small"
    label
  >
    {{ status.toUpperCase() }}
  </v-chip>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { UserState } from '@/types/user'

const props = defineProps<{
  status: UserState
}>()

const statusColor = computed(() => {
  const colors: Record<UserState, string> = {
    [UserState.PARTIAL_APPLICATION]: 'info',
    [UserState.PENDING_APPROVAL]: 'secondary',
    [UserState.ACTIVE]: 'success',
    [UserState.INACTIVE]: 'grey',
    [UserState.REJECTED]: 'error',
    [UserState.EXITED]: 'grey-darken-1',
    [UserState.BLACKLISTED]: 'red-darken-4'
  }
  return colors[props.status] || 'default'
})

const statusTextColor = computed(() => {
  return [UserState.EXITED, UserState.BLACKLISTED].includes(props.status) ? 'white' : ''
})
</script> 