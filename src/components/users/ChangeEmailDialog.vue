<template>
  <v-dialog v-model="showDialog" max-width="600px" persistent class="modern-dialog">
    <v-card class="card-modern" elevation="0">
      <div class="dialog-header">
        <div class="d-flex align-center">
          <v-icon size="32" color="primary" class="mr-3">mdi-email-edit</v-icon>
          <div>
            <h2 class="text-h5 font-weight-bold text-primary">Change Email</h2>
            <p class="text-body-2 text-medium-emphasis mt-1">Update user email address</p>
          </div>
        </div>
        <v-btn icon variant="text" @click="close" class="ml-auto">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </div>

      <v-card-text class="pa-6">
        <v-form ref="formRef" @submit.prevent="handleSubmit">
          <div class="text-subtitle-1 mb-4">
            Change email for: <strong>{{ user?.fullName }}</strong>
          </div>

          <div class="mb-4">
            <div class="text-body-2 text-medium-emphasis mb-2">Current Email:</div>
            <div class="text-body-1">{{ user?.email }}</div>
          </div>

          <v-text-field
            v-model="form.email"
            label="New Email Address"
            type="email"
            :rules="emailRules"
            required
            variant="outlined"
            class="mb-4"
          ></v-text-field>

          <v-text-field
            v-model="form.confirmEmail"
            label="Confirm New Email Address"
            type="email"
            :rules="confirmEmailRules"
            required
            variant="outlined"
            class="mb-4"
          ></v-text-field>

          <v-alert
            v-if="errorMessage"
            type="error"
            class="mb-4"
            closable
            @click:close="errorMessage = ''"
          >
            {{ errorMessage }}
          </v-alert>

          <v-alert
            type="warning"
            variant="tonal"
            class="mb-4"
          >
            <div class="text-body-2">
              <strong>Important:</strong> Changing the email address will require the user to verify their new email address.
              The user may need to log in again after this change.
            </div>
          </v-alert>
        </v-form>
      </v-card-text>

      <v-card-actions class="pa-6 pt-0">
        <v-spacer></v-spacer>
        <v-btn
          variant="text"
          @click="close"
          :disabled="loading"
        >
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          @click="handleSubmit"
          :loading="loading"
        >
          Change Email
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { User } from '@/types/user'
import { useAuthStore } from '@/stores/auth'
import { useSnackbar } from '@/composables/useSnackbar'

const props = defineProps<{
  modelValue: boolean
  user: User | null
  loading?: boolean
}>()

const emit = defineEmits(['update:modelValue', 'success'])

const authStore = useAuthStore()
const { showSuccess, showError } = useSnackbar()

const showDialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<any>(null)
const errorMessage = ref('')
const loading = ref(false)

const form = ref({
  email: '',
  confirmEmail: ''
})

const emailRules = [
  (v: string) => !!v || 'Email is required',
  (v: string) => /.+@.+\..+/.test(v) || 'Email must be valid',
  (v: string) => v !== props.user?.email || 'New email must be different from current email'
]

const confirmEmailRules = [
  (v: string) => !!v || 'Please confirm your email',
  (v: string) => v === form.value.email || 'Email addresses do not match'
]

const handleSubmit = async () => {
  if (!props.user) return

  const { valid } = await formRef.value?.validate()
  if (!valid) return

  try {
    loading.value = true
    errorMessage.value = ''
    
    await authStore.changeEmail(props.user.id, form.value.email)
    
    showSuccess('Email changed successfully')
    emit('success')
    close()
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || 'Failed to change email'
    showError('Failed to change email')
  } finally {
    loading.value = false
  }
}

const close = () => {
  form.value = {
    email: '',
    confirmEmail: ''
  }
  errorMessage.value = ''
  emit('update:modelValue', false)
}
</script>

<style scoped>
.modern-dialog :deep(.v-overlay__content) {
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-modern {
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  background: linear-gradient(135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%);
  border-radius: 16px 16px 0 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.v-card-text {
  max-height: 70vh;
  overflow-y: auto;
  padding: 2rem !important;
}

.modern-input :deep(.v-field) {
  border-radius: 12px;
  transition: all 0.2s ease;
}

.modern-input :deep(.v-field:hover) {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}

.modern-input :deep(.v-field--focused) {
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.25);
}

.btn-modern {
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3) !important;
  transition: all 0.2s ease !important;
}

.btn-modern:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(25, 118, 210, 0.4) !important;
}

.v-card-actions {
  padding: 1rem 2rem 2rem 2rem !important;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 0 0 16px 16px;
}

/* Custom scrollbar */
.v-card-text::-webkit-scrollbar {
  width: 6px;
}

.v-card-text::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.v-card-text::-webkit-scrollbar-thumb {
  background: rgba(25, 118, 210, 0.3);
  border-radius: 3px;
}

.v-card-text::-webkit-scrollbar-thumb:hover {
  background: rgba(25, 118, 210, 0.5);
}
</style>
