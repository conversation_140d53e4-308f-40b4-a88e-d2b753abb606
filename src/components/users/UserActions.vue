<template>
  <div class="d-flex gap-2">
    <!-- <v-btn
      icon
      size="small"
      color="primary"
      variant="text"
      @click="$emit('view', user)"
    >
      <v-icon icon="mdi-eye"></v-icon>
    </v-btn> -->

    <v-menu location="bottom end">
      <template v-slot:activator="{ props }">
        <v-btn
          icon
          size="small"
          color="primary"
          variant="text"
          v-bind="props"
        >
          <v-icon>mdi-dots-vertical</v-icon>
        </v-btn>
      </template>

      <v-list density="compact">
        <v-list-item
          v-for="transition in availableTransitions"
          :key="transition"
          @click="handleStateChange(transition)"
        >
          <v-list-item-title>
            {{ formatTransitionLabel(transition) }}
          </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script setup lang="ts">
import type { User } from '@/types/user'
import { UserState } from '@/types/user'

const props = defineProps<{
  user: User
  availableTransitions: UserState[]
}>()

const emit = defineEmits<{
  view: [user: User]
  'state-change': [userId: number, newState: UserState, currentState: UserState]
}>()

const handleStateChange = (newState: UserState) => {
  emit('state-change', props.user.id, newState, props.user.accountState)
}

const formatTransitionLabel = (state: UserState): string => {
  return `Change to ${state.toLocaleUpperCase().replace(/_/g, ' ')}`
}
</script> 