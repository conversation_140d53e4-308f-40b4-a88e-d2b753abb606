<template>
    <v-dialog :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" max-width="500px">
        <v-card>
            <v-card-title class="text-h5">
                Confirm State Change
            </v-card-title>

            <v-card-text>
                {{ message }}
            </v-card-text>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="grey-darken-1" variant="text" @click="onCancel">
                    Cancel
                </v-btn>
                <v-btn color="primary" variant="text" @click="onConfirm">
                    Confirm
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script setup lang="ts">
defineProps<{
    modelValue: boolean
    message: string
}>()

const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'cancel': []
    'confirm': []
}>()

const onCancel = () => emit('cancel')
const onConfirm = () => emit('confirm')
</script>