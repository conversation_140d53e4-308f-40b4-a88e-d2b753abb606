<template>
  <div class="filters-section">
    <div class="filters-header mb-4">
      <h3 class="text-h6 font-weight-medium text-primary">
        <v-icon class="mr-2">mdi-filter-outline</v-icon>
        Filter Users
      </h3>
      <p class="text-body-2 text-medium-emphasis">
        Search and filter users by various criteria
      </p>
    </div>

    <v-form @submit.prevent="handleFilter">
      <v-row>
        <v-col cols="12" sm="6" md="4">
          <v-text-field
            v-model="filters.email"
            label="Email Address"
            placeholder="Search by email..."
            variant="outlined"
            density="comfortable"
            hide-details
            class="modern-input"
            prepend-inner-icon="mdi-email-outline"
            color="primary"
          />
        </v-col>

        <v-col cols="12" sm="6" md="4">
          <v-select
            v-model="filters.accountState"
            :items="userLifeCycleOptions"
            label="Account Status"
            variant="outlined"
            density="comfortable"
            clearable
            hide-details
            class="modern-input"
            prepend-inner-icon="mdi-account-check-outline"
            color="primary"
          />
        </v-col>

        <v-col cols="12" md="4" class="d-flex align-center gap-3">
          <v-btn
            color="primary"
            size="large"
            @click="handleFilter"
            class="btn-modern flex-grow-1"
            prepend-icon="mdi-magnify"
          >
            Filter
          </v-btn>
          <v-btn
            variant="outlined"
            size="large"
            @click="resetFilters"
            class="btn-modern flex-grow-1"
            prepend-icon="mdi-refresh"
          >
            Reset
          </v-btn>
        </v-col>
      </v-row>
    </v-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { UserLifeCycle as UserState } from '@/types/user'

const emit = defineEmits(['filter', 'reset'])

const filters = ref({
  email: '',
  // userType: [] as UserType[],
  accountState: undefined as UserState | undefined
})

// const userTypeOptions = Object.values(UserType).map(type => ({
//   title: type.replace(/_/g, ' '),
//   value: type
// }))

const userLifeCycleOptions = Object.values(UserState)

// const formatUserType = (type: string) => type.replace(/_/g, ' ')

const handleFilter = () => {
  emit('filter', filters.value)
}

const resetFilters = () => {
  filters.value = {
    email: '',
    // userType: [],
    accountState: undefined
  }
  emit('reset')
}
</script>

<style scoped>
.filters-section {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.filters-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.filters-header h3 {
  background: linear-gradient(135deg, var(--v-theme-primary), var(--v-theme-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-input :deep(.v-field) {
  border-radius: 12px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.8);
}

.modern-input :deep(.v-field:hover) {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.modern-input :deep(.v-field--focused) {
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.25);
  background: rgba(255, 255, 255, 1);
}

.btn-modern {
  border-radius: 12px !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.btn-modern:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

.gap-3 {
  gap: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .filters-section {
    padding: 1rem;
  }

  .gap-3 {
    flex-direction: column;
    width: 100%;
  }

  .gap-3 .v-btn {
    width: 100%;
  }
}
</style>