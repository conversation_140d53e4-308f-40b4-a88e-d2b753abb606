<template>
  <v-dialog
    v-model="showDialog"
    max-width="800px"
    persistent
    class="modern-dialog"
  >
    <v-card class="card-modern" elevation="0">
      <div class="dialog-header">
        <div class="d-flex align-center">
          <v-icon size="32" color="primary" class="mr-3"
            >mdi-account-edit</v-icon
          >
          <div>
            <h2 class="text-h5 font-weight-bold text-primary">Edit User</h2>
            <p class="text-body-2 text-medium-emphasis mt-1">
              Update user information
            </p>
          </div>
        </div>
        <div class="header-actions">
          <v-btn
            variant="text"
            @click="handleSubmit"
            :loading="loading"
            color="primary"
            class="btn-modern mr-2"
            size="large"
          >
            <v-icon start>mdi-content-save</v-icon>
            Save Changes
          </v-btn>
          <v-btn icon variant="text" @click="close" class="ml-2">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
      </div>

      <v-card-text class="dialog-content">
        <v-form ref="formRef" v-model="isFormValid">
          <div class="form-section">
            <h3 class="section-title">Basic Information</h3>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.fullName"
                  label="Full Name"
                  :rules="nameRules"
                  required
                  variant="outlined"
                  class="modern-input"
                  prepend-inner-icon="mdi-account-outline"
                  color="primary"
                  placeholder="Enter full name"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="form.email"
                  label="Email Address"
                  type="email"
                  :rules="emailRules"
                  variant="outlined"
                  class="modern-input"
                  prepend-inner-icon="mdi-email-outline"
                  color="primary"
                  placeholder="<EMAIL>"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  :value="user?.tenantId"
                  label="Tenant ID"
                  readonly
                  variant="outlined"
                  class="modern-input"
                  prepend-inner-icon="mdi-domain"
                  color="primary"
                />
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  :value="user?.id"
                  label="User ID"
                  readonly
                  variant="outlined"
                  class="modern-input"
                  prepend-inner-icon="mdi-identifier"
                  color="primary"
                />
              </v-col>
            </v-row>
          </div>

          <div class="form-section">
            <h3 class="section-title">Account Settings</h3>

            <v-row>
              <v-col cols="12" md="6">
                <v-switch
                  v-model="form.isActive"
                  label="Account Active"
                  color="primary"
                  class="modern-switch"
                  hide-details
                />
                <p class="text-caption text-medium-emphasis mt-1">
                  Enable or disable user account access
                </p>
              </v-col>
              <v-col cols="12" md="6">
                <v-switch
                  v-model="form.emailVerified"
                  label="Email Verified"
                  color="info"
                  class="modern-switch"
                  hide-details
                />
                <p class="text-caption text-medium-emphasis mt-1">
                  Mark email address as verified
                </p>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-switch
                  v-model="form.verified"
                  label="Account Verified"
                  color="success"
                  class="modern-switch"
                  hide-details
                />
                <p class="text-caption text-medium-emphasis mt-1">
                  Mark account as verified
                </p>
              </v-col>
              <v-col cols="12" md="6">
                <v-switch
                  v-model="form.isAdministrator"
                  label="Administrator"
                  color="warning"
                  class="modern-switch"
                  hide-details
                />
                <p class="text-caption text-medium-emphasis mt-1">
                  Grant administrator privileges
                </p>
              </v-col>
            </v-row>
          </div>

          <v-alert
            v-if="errorMessage"
            type="error"
            variant="tonal"
            class="mt-4"
            closable
            @click:close="errorMessage = ''"
          >
            {{ errorMessage }}
          </v-alert>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useUsersStore } from "@/stores/users";
import { useSnackbar } from "@/composables/useSnackbar";
import type { User } from "@/types/user";
import type { UpdateUserBioDataDto } from "@/types/user.dto";

const props = defineProps<{
  modelValue: boolean;
  user: User | null;
}>();

const emit = defineEmits(["update:modelValue", "success"]);

const usersStore = useUsersStore();
const { showSuccess, showError } = useSnackbar();

const showDialog = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const formRef = ref<any>(null);
const isFormValid = ref(false);
const loading = ref(false);
const errorMessage = ref("");

const form = ref({
  fullName: "",
  email: "",
  isActive: true,
  emailVerified: false,
  verified: false,
  isAdministrator: false,
});

// Validation rules
const nameRules = [
  (v: string) => !!v || "Full name is required",
  (v: string) => v.length >= 2 || "Full name must be at least 2 characters",
];

const emailRules = [
  (v: string) => !v || /.+@.+\..+/.test(v) || "Email must be valid",
];

// Initialize form when user changes
watch(
  () => props.user,
  (newUser) => {
    if (newUser) {
      form.value = {
        fullName: newUser.fullName || "",
        email: newUser.email || "",
        isActive: newUser.isActive,
        emailVerified: newUser.emailVerified,
        verified: newUser.verified,
        isAdministrator: newUser.isAdministrator,
      };
    }
  },
  { immediate: true },
);

const handleSubmit = async () => {
  if (!props.user) return;

  const { valid } = await formRef.value?.validate();
  if (!valid) return;

  try {
    loading.value = true;
    errorMessage.value = "";

    const updateData = {
      fullName: form.value.fullName,
      email: form.value.email || undefined,
      isActive: form.value.isActive,
      emailVerified: form.value.emailVerified,
      verified: form.value.verified,
      isAdministrator: form.value.isAdministrator,
    };

    await usersStore.updateUserFull(props.user.id, updateData);

    showSuccess("User updated successfully");
    emit("success");
    close();
  } catch (error: any) {
    errorMessage.value =
      error.response?.data?.message || "Failed to update user";
    showError("Failed to update user");
  } finally {
    loading.value = false;
  }
};

const close = () => {
  errorMessage.value = "";
  emit("update:modelValue", false);
};
</script>

<style scoped>
.modern-dialog :deep(.v-overlay__content) {
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-modern {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  background: linear-gradient(
    135deg,
    rgba(25, 118, 210, 0.05) 0%,
    rgba(255, 107, 53, 0.05) 100%
  );
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-modern {
  border-radius: 12px;
  text-transform: none;
  font-weight: 600;
  padding: 0 24px;
}

.dialog-content {
  padding: 32px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--v-theme-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(25, 118, 210, 0.1);
}

.modern-input :deep(.v-field) {
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
}

.modern-input :deep(.v-field--focused) {
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.modern-switch :deep(.v-switch__track) {
  border-radius: 16px;
}

.modern-switch :deep(.v-switch__thumb) {
  border-radius: 50%;
}

/* Responsive design */
@media (max-width: 600px) {
  .dialog-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .dialog-content {
    padding: 24px 16px;
  }
}
</style>
