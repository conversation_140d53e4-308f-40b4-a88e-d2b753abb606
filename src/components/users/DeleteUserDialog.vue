<template>
  <v-dialog
    :model-value="show"
    @update:model-value="$emit('cancel')"
    max-width="500"
    persistent
  >
    <v-card class="modern-dialog">
      <v-card-title class="d-flex align-center pa-6 pb-4">
        <v-icon color="error" size="28" class="mr-3">mdi-delete-alert</v-icon>
        <span class="text-h6 font-weight-medium">Delete User</span>
      </v-card-title>

      <v-card-text class="pa-6 pt-2">
        <div class="text-body-1 mb-4">
          Are you sure you want to delete this user? This action cannot be
          undone.
        </div>

        <v-card v-if="user" variant="outlined" class="pa-4 bg-error-lighten-5">
          <div class="d-flex align-center">
            <v-avatar size="40" class="mr-3">
              <v-icon>mdi-account</v-icon>
            </v-avatar>
            <div>
              <div class="font-weight-medium">{{ user.fullName }}</div>
              <div class="text-body-2 text-medium-emphasis">
                {{ user.email }}
              </div>
            </div>
          </div>
        </v-card>

        <v-alert type="warning" variant="tonal" class="mt-4" density="compact">
          <template #prepend>
            <v-icon>mdi-alert-circle</v-icon>
          </template>
          This will permanently remove the user and all associated data.
        </v-alert>
      </v-card-text>

      <v-card-actions class="pa-6 pt-0">
        <v-spacer />
        <v-btn variant="text" @click="$emit('cancel')" :disabled="loading">
          Cancel
        </v-btn>
        <v-btn
          color="error"
          variant="flat"
          @click="$emit('confirm')"
          :loading="loading"
          :disabled="loading"
        >
          Delete User
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import type { User } from "@/types/user";

defineProps<{
  show: boolean;
  user: User | null;
  loading?: boolean;
}>();

defineEmits<{
  cancel: [];
  confirm: [];
}>();
</script>

<style scoped>
.modern-dialog {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
}

.bg-error-lighten-5 {
  background-color: rgba(244, 67, 54, 0.05) !important;
  border-color: rgba(244, 67, 54, 0.2) !important;
}
</style>
