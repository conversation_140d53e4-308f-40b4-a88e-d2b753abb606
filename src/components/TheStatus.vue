<template>
    <v-chip
        :color="getStatusColor(status)"
        dark>{{ status }}</v-chip>
</template>

<script setup lang="ts">
const {status } = defineProps(['status'])

function getStatusColor(value: string) {
    switch (value) {
        case 'open':
            return 'primary'
        case 'inprogress':
            return 'warning'
        case 'closed':
            return 'success'
        default:
            return 'grey'
    }
}
</script>