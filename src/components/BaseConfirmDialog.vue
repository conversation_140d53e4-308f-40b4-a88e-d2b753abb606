<template>
	<!-- confirmDialog delete target dialog -->
	<v-dialog v-model="toggler" persistent max-width="400">
		<v-card>
			<v-card-title class="text-h5 text-center">
				<v-icon color="red" medium> mdi-alert </v-icon>
			</v-card-title>
			<v-card-text>{{ message }} </v-card-text>
			<v-card-actions>
				<v-spacer></v-spacer>
				<v-btn color="green darken-1" text @click="handleClose"> No </v-btn>
				<v-btn color="red darken-1" text @click="handleConfirm"> Yes </v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
	<!-- confirmDialog delete targer dialog -->
</template>

<script>
export default {
	name: 'BaseConfirmDialog',
	emits: ['toggleDialog', 'update:toggle', 'confirmDialog'],
	props: {
		message: {
			type: String,
			default: 'Are you sure you want to delete ?',
		},
		toggle: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		toggler: {
			get() {
				return this.toggle;
			},
			set(val) {
				this.$emit('update:toggle', val);
			},
		},
	},
	data: function () {
		return {};
	},
	methods: {
		handleClose() {
			this.$emit('update:toggle', false);
		},
		handleConfirm() {
			this.$emit('confirmDialog');
		},
	},
};
</script>
