<template>
  <v-dialog v-model="showDialog" max-width="600px" persistent>
    <v-card>
      <v-card-title>
        <span class="text-h5">{{ isEdit ? 'Edit Tenant' : 'Create New Tenant' }}</span>
      </v-card-title>

      <v-card-text>
        <v-container>
          <v-form ref="formRef" @submit.prevent="handleSubmit">
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="form.name"
                  label="Tenant Name"
                  :rules="nameRules"
                  required
                  variant="outlined"
                  placeholder="e.g., Acme Corporation"
                ></v-text-field>
              </v-col>
              
              <v-col cols="12">
                <v-text-field
                  v-model="form.subdomain"
                  label="Subdomain"
                  :rules="subdomainRules"
                  required
                  variant="outlined"
                  placeholder="e.g., acme-corp"
                  hint="Only lowercase letters, numbers, and hyphens allowed"
                  persistent-hint
                  @input="formatSubdomain"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn variant="text" @click="close">Cancel</v-btn>
        <v-btn 
          color="primary" 
          :loading="loading" 
          @click="handleSubmit"
        >
          {{ isEdit ? 'Update' : 'Create' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { CreateTenantDto, UpdateTenantDto, Tenant } from '@/types/tenant';

const props = defineProps<{
  modelValue: boolean
  loading?: boolean
  tenant?: Tenant | null
}>()

const emit = defineEmits(['update:modelValue', 'submit'])

const showDialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.tenant)

const formRef = ref<any>(null)

const form = ref<CreateTenantDto>({
  name: '',
  subdomain: ''
})

const nameRules = [
  (v: string) => !!v || 'Tenant name is required',
  (v: string) => (v && v.length >= 2) || 'Name must be at least 2 characters',
  (v: string) => (v && v.length <= 100) || 'Name must be less than 100 characters'
]

const subdomainRules = [
  (v: string) => !!v || 'Subdomain is required',
  (v: string) => (v && v.length >= 2) || 'Subdomain must be at least 2 characters',
  (v: string) => (v && v.length <= 50) || 'Subdomain must be less than 50 characters',
  (v: string) => /^[a-z0-9-]+$/.test(v) || 'Subdomain must contain only lowercase letters, numbers, and hyphens',
  (v: string) => !v.startsWith('-') && !v.endsWith('-') || 'Subdomain cannot start or end with a hyphen'
]

const formatSubdomain = () => {
  // Convert to lowercase and replace invalid characters
  form.value.subdomain = form.value.subdomain
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, '')
    .replace(/--+/g, '-') // Replace multiple hyphens with single hyphen
}

const handleSubmit = async () => {
  const { valid } = await formRef.value?.validate()
  if (valid) {
    if (isEdit.value) {
      const updateData: UpdateTenantDto = {
        name: form.value.name,
        subdomain: form.value.subdomain
      }
      emit('submit', updateData)
    } else {
      emit('submit', form.value)
    }
  }
}

const close = () => {
  form.value = {
    name: '',
    subdomain: ''
  }
  emit('update:modelValue', false)
}

// Watch for tenant prop changes (for edit mode)
watch(() => props.tenant, (newTenant) => {
  if (newTenant) {
    form.value = {
      name: newTenant.name,
      subdomain: newTenant.subdomain
    }
  } else {
    form.value = {
      name: '',
      subdomain: ''
    }
  }
}, { immediate: true })

// Auto-generate subdomain from name when creating new tenant
watch(() => form.value.name, (newName) => {
  if (!isEdit.value && newName && !form.value.subdomain) {
    form.value.subdomain = newName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/--+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
  }
})
</script>
