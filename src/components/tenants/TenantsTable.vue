<template>
  <v-card>
    <v-data-table
      :page="currentPage"
      :headers="headers"
      :items="tenants"
      :loading="loading"
      :items-per-page="itemsPerPage"
      :items-length="totalItems"
      @update:page="$emit('update:currentPage', $event)"
      @update:options="$emit('update:options', $event)"
    >
  
      <!-- Subdomain Column -->
      <template v-slot:item.subdomain="{ item }">
        <v-chip color="primary" size="small" variant="outlined">
          {{ item.subdomain }}
        </v-chip>
      </template>

      <!-- Created Date Column -->
      <template v-slot:item.createdAt="{ item }">
        {{ formatDate(item.createdAt) }}
      </template>

      <!-- Updated Date Column -->
      <template v-slot:item.updatedAt="{ item }">
        {{ formatDate(item.updatedAt) }}
      </template>

      <!-- Actions Column -->
      <template v-slot:item.actions="{ item }">
        <v-btn icon size="small" class="mr-2" @click="$emit('view', item)">
          <v-icon>mdi-eye</v-icon>
        </v-btn>
        <v-btn icon size="small" class="mr-2" @click="$emit('edit', item)">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn icon size="small" color="error" @click="$emit('delete', item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>
  </v-card>
</template>

<script setup lang="ts">
import type { Tenant } from '@/types/tenant';

const props = defineProps<{
  tenants: Tenant[]
  loading: boolean
  currentPage: number
  itemsPerPage: number
  totalItems: number
}>()

const emit = defineEmits(['update:currentPage', 'update:options', 'view', 'edit', 'delete'])

const headers = [
  { title: 'Name', key: 'name', sortable: true },
  { title: 'Subdomain', key: 'subdomain', sortable: true },
  { title: 'Created', key: 'createdAt', sortable: true },
  { title: 'Updated', key: 'updatedAt', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false }
]

const formatDate = (date: Date | string) => {
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
