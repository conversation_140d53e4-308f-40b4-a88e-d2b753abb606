<template>
  <v-card class="mb-4">
    <v-card-title>
      <v-icon class="mr-2">mdi-filter</v-icon>
      Filters
    </v-card-title>
    
    <v-card-text>
      <v-form @submit.prevent="applyFilters">
        <v-row>
          <v-col cols="12" md="4">
            <v-text-field
              v-model="filters.name"
              label="Tenant Name"
              variant="outlined"
              density="compact"
              clearable
              placeholder="Search by name..."
            ></v-text-field>
          </v-col>
          
          <v-col cols="12" md="4">
            <v-text-field
              v-model="filters.subdomain"
              label="Subdomain"
              variant="outlined"
              density="compact"
              clearable
              placeholder="Search by subdomain..."
            ></v-text-field>
          </v-col>
          
          <v-col cols="12" md="2">
            <v-select
              v-model="filters.sortBy"
              label="Sort By"
              :items="sortByOptions"
              variant="outlined"
              density="compact"
            ></v-select>
          </v-col>
          
          <v-col cols="12" md="2">
            <v-select
              v-model="filters.sortOrder"
              label="Order"
              :items="sortOrderOptions"
              variant="outlined"
              density="compact"
            ></v-select>
          </v-col>
        </v-row>
        
        <v-row>
          <v-col cols="12" md="2">
            <v-select
              v-model="filters.limit"
              label="Items per page"
              :items="limitOptions"
              variant="outlined"
              density="compact"
            ></v-select>
          </v-col>
          
          <v-col cols="12" md="10" class="d-flex align-center">
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              class="mr-2"
              @click="applyFilters"
            >
              <v-icon class="mr-1">mdi-magnify</v-icon>
              Search
            </v-btn>
            <v-btn
              variant="outlined"
              @click="resetFilters"
            >
              <v-icon class="mr-1">mdi-refresh</v-icon>
              Reset
            </v-btn>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { TenantQuery } from '@/types/tenant'

const emit = defineEmits(['filter', 'reset'])

const filters = reactive<TenantQuery>({
  name: '',
  subdomain: '',
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

const sortByOptions = [
  { title: 'Name', value: 'name' },
  { title: 'Subdomain', value: 'subdomain' },
  { title: 'Created Date', value: 'createdAt' },
  { title: 'Updated Date', value: 'updatedAt' }
]

const sortOrderOptions = [
  { title: 'Ascending', value: 'asc' },
  { title: 'Descending', value: 'desc' }
]

const limitOptions = [
  { title: '5', value: 5 },
  { title: '10', value: 10 },
  { title: '25', value: 25 },
  { title: '50', value: 50 },
  { title: '100', value: 100 }
]

const applyFilters = () => {
  // Remove empty string values
  const cleanFilters: TenantQuery = {
    page: 1, // Reset to first page when filtering
    limit: filters.limit,
    sortBy: filters.sortBy,
    sortOrder: filters.sortOrder
  }
  
  if (filters.name?.trim()) {
    cleanFilters.name = filters.name.trim()
  }
  
  if (filters.subdomain?.trim()) {
    cleanFilters.subdomain = filters.subdomain.trim()
  }
  
  emit('filter', cleanFilters)
}

const resetFilters = () => {
  filters.name = ''
  filters.subdomain = ''
  filters.page = 1
  filters.limit = 10
  filters.sortBy = 'createdAt'
  filters.sortOrder = 'desc'
  
  emit('reset')
}
</script>
