/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActionButton: typeof import('./components/common/ActionButton.vue')['default']
    AlertDialog: typeof import('./components/ui/alert-dialog/AlertDialog.vue')['default']
    AlertDialogAction: typeof import('./components/ui/alert-dialog/AlertDialogAction.vue')['default']
    AlertDialogCancel: typeof import('./components/ui/alert-dialog/AlertDialogCancel.vue')['default']
    AlertDialogContent: typeof import('./components/ui/alert-dialog/AlertDialogContent.vue')['default']
    AlertDialogDescription: typeof import('./components/ui/alert-dialog/AlertDialogDescription.vue')['default']
    AlertDialogFooter: typeof import('./components/ui/alert-dialog/AlertDialogFooter.vue')['default']
    AlertDialogHeader: typeof import('./components/ui/alert-dialog/AlertDialogHeader.vue')['default']
    AlertDialogTitle: typeof import('./components/ui/alert-dialog/AlertDialogTitle.vue')['default']
    AlertDialogTrigger: typeof import('./components/ui/alert-dialog/AlertDialogTrigger.vue')['default']
    AppDrawer: typeof import('./components/AppDrawer.vue')['default']
    BaseConfirmDialog: typeof import('./components/BaseConfirmDialog.vue')['default']
    Button: typeof import('./components/ui/button/Button.vue')['default']
    ChangeEmailDialog: typeof import('./components/users/ChangeEmailDialog.vue')['default']
    ChangePasswordDialog: typeof import('./components/users/ChangePasswordDialog.vue')['default']
    CommissionTrendsChart: typeof import('./components/charts/CommissionTrendsChart.vue')['default']
    CreateTenantDialog: typeof import('./components/tenants/CreateTenantDialog.vue')['default']
    CreateUserDialog: typeof import('./components/users/CreateUserDialog.vue')['default']
    DataTable: typeof import('./components/common/DataTable.vue')['default']
    DeleteUserDialog: typeof import('./components/users/DeleteUserDialog.vue')['default']
    DonutChart: typeof import('./components/charts/DonutChart.vue')['default']
    EditUserDialog: typeof import('./components/users/EditUserDialog.vue')['default']
    GoogleTable: typeof import('./components/GoogleTable.vue')['default']
    HelloWorld: typeof import('./components/HelloWorld.vue')['default']
    IconCommunity: typeof import('./components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./components/icons/IconTooling.vue')['default']
    LoadingOverlay: typeof import('./components/common/LoadingOverlay.vue')['default']
    MiniChart: typeof import('./components/charts/MiniChart.vue')['default']
    NavigationItem: typeof import('./components/sidebar/NavigationItem.vue')['default']
    NavigationMenu: typeof import('./components/sidebar/NavigationMenu.vue')['default']
    NotificationBadge: typeof import('./components/common/NotificationBadge.vue')['default']
    Paginations: typeof import('./components/Paginations.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StateChangeDialog: typeof import('./components/users/StateChangeDialog.vue')['default']
    StatusBadge: typeof import('./components/common/StatusBadge.vue')['default']
    TenantFilters: typeof import('./components/tenants/TenantFilters.vue')['default']
    TenantsTable: typeof import('./components/tenants/TenantsTable.vue')['default']
    TheBack: typeof import('./components/TheBack.vue')['default']
    TheHeader: typeof import('./components/TheHeader.vue')['default']
    ThePageHeader: typeof import('./components/ThePageHeader.vue')['default']
    TheRefresh: typeof import('./components/TheRefresh.vue')['default']
    TheSidebar: typeof import('./components/TheSidebar.vue')['default']
    TheStatus: typeof import('./components/TheStatus.vue')['default']
    TheWelcome: typeof import('./components/TheWelcome.vue')['default']
    UserActions: typeof import('./components/users/UserActions.vue')['default']
    UserFilters: typeof import('./components/users/UserFilters.vue')['default']
    UserProfileSection: typeof import('./components/sidebar/UserProfileSection.vue')['default']
    UsersTable: typeof import('./components/users/UsersTable.vue')['default']
    UserStateConfirmDialog: typeof import('./components/users/UserStateConfirmDialog.vue')['default']
    UserStatusChip: typeof import('./components/users/UserStatusChip.vue')['default']
    WelcomeItem: typeof import('./components/WelcomeItem.vue')['default']
  }
}
