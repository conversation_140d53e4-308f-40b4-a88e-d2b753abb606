import { ResultsPaginated } from "@/types";
import apiClient from "./api.client";
import axios from "axios";
import type { User, UsersQuery } from "@/types/user";
import type {
  UpdateUserStateDto,
  UpdateUserBioDataDto,
  CreateUserDto,
} from "@/types/user.dto";

const API_URL = "/user";
const JSON_SERVER_URL = "http://localhost:3001/users";

// Create a separate client for json-server operations
const jsonServerClient = axios.create({
  baseURL: "http://localhost:3001",
  headers: {
    "Content-Type": "application/json",
  },
});

export const usersApi = {
  async searchUsers(
    query: Partial<UsersQuery>,
  ): Promise<ResultsPaginated<User>> {
    // Ensure numeric fields are properly typed and filter only active users
    const cleanQuery = {
      ...query,
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 10,
      tenantId: query.tenantId ? Number(query.tenantId) : undefined,
      isActive: true, // Only show active users (exclude soft-deleted users)
    };

    const response = await apiClient.post(`${API_URL}/search`, cleanQuery);
    return response.data;
  },

  async getUserById(id: number): Promise<User> {
    const response = await apiClient.get(`${API_URL}/${id}`);
    return response.data;
  },

  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get(`${API_URL}/me`);
    return response.data;
  },

  async updateUser(id: number, data: UpdateUserBioDataDto): Promise<User> {
    const response = await apiClient.patch(`${API_URL}/${id}/profile`, data);
    return response.data;
  },

  async updateUserState(id: number, data: UpdateUserStateDto): Promise<User> {
    const response = await apiClient.patch(
      `${API_URL}/${id}/verification`,
      data,
    );
    return response.data;
  },

  async deleteUser(id: number): Promise<void> {
    // Use backend soft delete (sets isActive: false)
    await apiClient.delete(`${API_URL}/${id}`);
  },

  async createUser(data: CreateUserDto): Promise<User> {
    const response = await apiClient.post(API_URL, data);
    return response.data;
  },

  async updateProfile(id: number, data: UpdateUserBioDataDto): Promise<User> {
    const response = await apiClient.patch(`${API_URL}/${id}/profile`, data);
    return response.data;
  },

  async updateUserFull(id: number, data: any): Promise<User> {
    const response = await apiClient.patch(`${API_URL}/${id}`, data);
    return response.data;
  },
};
