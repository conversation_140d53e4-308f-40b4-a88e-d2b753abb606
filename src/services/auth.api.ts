import apiClient from './api.client'
import type { LoginAuthDto } from '@/types/auth'

const API_URL = '/auth'

interface CreateAuthDto {
    fullName: string
    email?: string
    password: string
    tenantId: string
    isActive?: boolean
    acceptTerms?: boolean
}

export const authApi = {
    async login(data: LoginAuthDto) {
        const response = await apiClient.post(`${API_URL}/signin`, data)
        return response.data
    },

    async logout(userId: string) {
        await apiClient.post(`${API_URL}/logout`, { userId })
    },

    async forgotPassword(email: string) {
        const response = await apiClient.post(`${API_URL}/forgot-password`, { email })
        return response.data
    },

    async resetPassword(token: string, password: string, confirmPassword: string) {
        const response = await apiClient.post(`${API_URL}/reset-password`, {
            token,
            password,
            confirmPassword
        })
        return response.data
    },

    async signup(data: CreateAuthDto) {
        const response = await apiClient.post(`${API_URL}/signup`, data)
        return response.data
    },

    async refreshTokens(userId: string, refreshToken: string) {
        const response = await apiClient.post(`${API_URL}/refresh-token`, {
            userId,
            refreshToken
        })
        return response.data
    },

    async verifyEmail(token: string) {
        const response = await apiClient.get(`${API_URL}/verify-email?token=${token}`)
        return response.data
    },

    async changePassword(userId: string, password: string) {
        const response = await apiClient.post(`${API_URL}/change-password`, {
            userId: (userId),
            password
        })
        return response.data
    },

    async changeEmail(userId: string, email: string) {
        const response = await apiClient.post(`${API_URL}/change-email`, {
            userId: (userId),
            email
        })
        return response.data
    }
}
