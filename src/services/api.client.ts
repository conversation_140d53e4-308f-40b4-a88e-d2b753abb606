import axios from 'axios'
import config from '@/config';
import type { AxiosInstance } from 'axios'
import { useSnackbar } from '@/composables/useSnackbar'
// const appStore = useAppStore()
const { showError } = useSnackbar()
const apiClient: AxiosInstance = axios.create({
    baseURL: config['BACKEND_SERVICE'],
    headers: {
        'Content-Type': 'application/json'
    }
})

// Request interceptor
apiClient.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('access_token')
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }
        // appStore.setLoading(true)
        return config
    },
    (error) => {
        const message =  error.response?.data?.message || error.message
        if (typeof message === 'string') {
            showError('Request error: ' + message)
        }else if(Array.isArray(message)){
            showError('Request error: ' + message.join(', '))
        }
        // appStore.setLoading(false)
        return Promise.reject(error)
    }
)

// Response interceptor
apiClient.interceptors.response.use(
    (response) => response,
    async (error) => {
        const message = error.response?.data?.message || error.message
        if (typeof message === 'string') {
            showError('Request error: ' + message)
        } else if (Array.isArray(message)) {
            showError('Request error: ' + message.join(', '))
        }
        const originalRequest = error.config

        // If the error status is 401 and there is no originalRequest._retry flag,
        // it means the token has expired and we need to refresh it
        if (error.response.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true

            try {
                const refreshToken = localStorage.getItem('refresh_token')
                const response = await axios.post(`${config['BACKEND_SERVICE']}/auth/refresh`, {
                    refreshToken
                })

                const { access_token } = response.data

                localStorage.setItem('access_token', access_token)

                // Retry the original request with the new token
                originalRequest.headers.Authorization = `Bearer ${access_token}`
                return axios(originalRequest)
            } catch (error) {
                // If refresh token fails, redirect to login
                localStorage.removeItem('access_token')
                localStorage.removeItem('refresh_token')
                window.location.href = '/auth/login'
                return Promise.reject(error)
            }
        }

        return Promise.reject(error)
    }
)

export default apiClient 