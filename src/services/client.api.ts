import { ResultsPaginated } from '@/types'
import { ClientEntity, ClientQueryDto, CreateClientDto } from '@/types/client.types'

// Mock data for clients since the backend doesn't have a client module yet
const mockClients: ClientEntity[] = [
    {
        id: 1,
        firstName: '<PERSON>',
        lastName: 'Doe',
        middleName: '<PERSON>',
        birthDate: '1985-06-15',
        maritalStatus: 'Married',
        accountState: 'Active' as any,
        acceptTerms: true,
        entityType: 'Individual' as any,
        email: '<EMAIL>',
        emailVerified: true,
        verified: true,
        userState: 'Active',
        mobilePhone: '+**********',
        residence: 'New York',
        county: 'Manhattan',
        subCounty: 'Upper East Side',
        ward: 'Ward 1',
        creationDate: new Date('2024-01-01'),
        lastModifiedDate: new Date('2024-01-15'),
        deletedAt: null
    },
    {
        id: 2,
        firstName: 'Jane',
        lastName: '<PERSON>',
        middleName: null,
        birthDate: '1990-03-22',
        maritalStatus: 'Single',
        accountState: 'Active' as any,
        acceptTerms: true,
        entityType: 'Individual' as any,
        email: '<EMAIL>',
        emailVerified: true,
        verified: true,
        userState: 'Active',
        mobilePhone: '+**********',
        residence: 'Los Angeles',
        county: 'LA County',
        subCounty: 'Beverly Hills',
        ward: null,
        creationDate: new Date('2024-01-02'),
        lastModifiedDate: new Date('2024-01-16'),
        deletedAt: null
    },
    {
        id: 3,
        firstName: 'Robert',
        lastName: 'Johnson',
        middleName: 'William',
        birthDate: '1978-11-08',
        maritalStatus: 'Divorced',
        accountState: 'Pending' as any,
        acceptTerms: true,
        entityType: 'Individual' as any,
        email: '<EMAIL>',
        emailVerified: false,
        verified: false,
        userState: 'Pending',
        mobilePhone: '+**********',
        residence: 'Chicago',
        county: 'Cook County',
        subCounty: 'Downtown',
        ward: 'Ward 3',
        creationDate: new Date('2024-01-03'),
        lastModifiedDate: new Date('2024-01-17'),
        deletedAt: null
    }
]

export const clientApi = {
    async createClient(client: CreateClientDto): Promise<ClientEntity> {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500))

        const newClient: ClientEntity = {
            id: mockClients.length + 1,
          
        }

        mockClients.push(newClient)
        return newClient
    },

    async getClientById(id: number): Promise<ClientEntity> {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 300))

        const client = mockClients.find(c => c.id === id)
        if (!client) {
            throw new Error('Client not found')
        }
        return client
    },

    async getClients(query: Partial<ClientQueryDto>): Promise<ResultsPaginated<ClientEntity>> {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500))

        let filteredClients = [...mockClients]

        // Apply filters
        if (query.email) {
            filteredClients = filteredClients.filter(c =>
                c.email.toLowerCase().includes(query.email!.toLowerCase())
            )
        }

        if (query.county) {
            filteredClients = filteredClients.filter(c =>
                c.county.toLowerCase().includes(query.county!.toLowerCase())
            )
        }

        if (query.subCounty) {
            filteredClients = filteredClients.filter(c =>
                c.subCounty.toLowerCase().includes(query.subCounty!.toLowerCase())
            )
        }

        // Pagination
        const page = query.page || 1
        const limit = query.limit || 10
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        const paginatedClients = filteredClients.slice(startIndex, endIndex)

        return {
            results: paginatedClients,
            totalItems: filteredClients.length,
            currentPage: page,
            totalPages: Math.ceil(filteredClients.length / limit),
            hasNextPage: endIndex < filteredClients.length,
            hasPreviousPage: page > 1,
            nextPage: endIndex < filteredClients.length ? page + 1 : null
        }
    },

    async updateClient(id: number, client: Partial<CreateClientDto>): Promise<ClientEntity> {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500))

        const clientIndex = mockClients.findIndex(c => c.id === id)
        if (clientIndex === -1) {
            throw new Error('Client not found')
        }

        const updatedClient = {
            ...mockClients[clientIndex],
            ...client,
            lastModifiedDate: new Date()
        }

        mockClients[clientIndex] = updatedClient
        return updatedClient
    },

}