import { ResultsPaginated } from '@/types'
import apiClient from './api.client'
import type {
    Tenant,
    CreateTenantDto,
    UpdateTenantDto,
    UpdateTenantProfileDto,
    TenantQuery,
    TenantSearchDto,
    TenantStatistics,
} from '@/types/tenant'

const API_URL = '/tenant'

export const tenantApi = {
    /**
     * Get all tenants with filtering and pagination
     */
    async getTenants(query?: TenantQuery): Promise<ResultsPaginated<Tenant>> {
        const params = new URLSearchParams()
        
        if (query?.name) params.append('name', query.name)
        if (query?.subdomain) params.append('subdomain', query.subdomain)
        if (query?.page) params.append('page', query.page.toString())
        if (query?.limit) params.append('limit', query.limit.toString())
        if (query?.sortBy) params.append('sortBy', query.sortBy)
        if (query?.sortOrder) params.append('sortOrder', query.sortOrder)

        const url = params.toString() ? `${API_URL}?${params.toString()}` : API_URL
        const response = await apiClient.get(url)
        return response.data
    },

    /**
     * Get tenant by ID
     */
    async getTenantById(id: string): Promise<Tenant> {
        const response = await apiClient.get(`${API_URL}/${id}`)
        return response.data
    },

    /**
     * Get tenant by subdomain
     */
    async getTenantBySubdomain(subdomain: string): Promise<Tenant | null> {
        const response = await apiClient.get(`${API_URL}/subdomain/${subdomain}`)
        return response.data
    },

    /**
     * Create a new tenant
     */
    async createTenant(data: CreateTenantDto): Promise<Tenant> {
        const response = await apiClient.post(API_URL, data)
        return response.data
    },

    /**
     * Update tenant
     */
    async updateTenant(id: string, data: UpdateTenantDto): Promise<Tenant> {
        const response = await apiClient.patch(`${API_URL}/${id}`, data)
        return response.data
    },

    /**
     * Update tenant profile (name only)
     */
    async updateTenantProfile(id: string, data: UpdateTenantProfileDto): Promise<Tenant> {
        const response = await apiClient.patch(`${API_URL}/${id}/profile`, data)
        return response.data
    },

    /**
     * Search tenants with advanced filtering
     */
    async searchTenants(searchDto: TenantSearchDto): Promise<ResultsPaginated<Tenant>> {
        const response = await apiClient.post(`${API_URL}/search`, searchDto)
        return response.data
    },

    /**
     * Get tenant statistics
     */
    async getTenantStatistics(id: string): Promise<TenantStatistics> {
        const response = await apiClient.get(`${API_URL}/${id}/statistics`)
        return response.data
    },

    /**
     * Get total tenant count
     */
    async getTenantCount(): Promise<{ count: number }> {
        const response = await apiClient.get(`${API_URL}/admin/count`)
        return response.data
    },

    /**
     * Delete tenant (soft delete)
     */
    async deleteTenant(id: string): Promise<{ message: string }> {
        const response = await apiClient.delete(`${API_URL}/${id}`)
        return response.data
    },

    /**
     * Admin test endpoint
     */
    async adminTest(): Promise<{ message: string; timestamp: string; module: string }> {
        const response = await apiClient.get(`${API_URL}/admin/test`)
        return response.data
    }
}
