import { createRouter, createWeb<PERSON>istory } from "vue-router";
import { useAuthStore } from "@/stores/auth";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      redirect: "/dashboard",
    },
    {
      path: "/dashboard",
      name: "dashboard",
      component: () => import("@/pages/dashboard/DashboardPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/about",
      name: "about",
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import("../views/AboutView.vue"),
    },
    {
      path: "/auth",
      component: () => import("@/pages/auth/AuthLayout.vue"),
      children: [
        {
          path: "login",
          name: "login",
          component: () => import("@/pages/auth/Login.vue"),
        },
        {
          path: "forgot-password",
          name: "forgot-password",
          component: () => import("@/pages/auth/ForgotPassword.vue"),
        },
        {
          path: "reset-password/:token?",
          name: "reset-password",
          component: () => import("@/pages/auth/ResetPassword.vue"),
        },
      ],
    },
    {
      path: "/users",
      name: "users",
      component: () => import("@/pages/users/UsersPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/users/:id",
      name: "user-detail",
      component: () => import("@/pages/users/UserDetailPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/tenants",
      name: "tenants",
      component: () => import("@/pages/tenants/TenantsPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/profile",
      name: "profile",
      component: () => import("@/pages/profile/ProfilePage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/clients",
      name: "clients",
      component: () => import("@/pages/clients/ClientPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/commissions",
      name: "commissions",
      component: () => import("@/pages/commissions/CommissionsPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/reports",
      name: "reports",
      component: () => import("@/pages/reports/ReportsPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/calculator",
      name: "calculator",
      component: () => import("@/pages/calculator/CalculatorPage.vue"),
      meta: {
        requiresAuth: true,
      },
    },
  ],
});

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const publicPages = [
    "/auth/login",
    "/auth/forgot-password",
    "/auth/reset-password",
    "/auth/verify-otp",
    "/auth/register",
    "/",
  ];
  const authRequired = !publicPages.includes(to.path);

  if (authRequired && !authStore.isAuthenticated) {
    return next("/auth/login");
  }

  next();
});

export default router;
