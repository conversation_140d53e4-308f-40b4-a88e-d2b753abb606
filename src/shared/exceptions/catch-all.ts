import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { Response } from 'express'; // Import Response from express

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    // In certain situations `httpAdapter` might not be available in the
    // constructor method, thus we should resolve it here.
    const { httpAdapter } = this.httpAdapterHost;

    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>(); // Get Express Response

    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const stackTrace =
      exception instanceof Error
        ? process.env.NODE_ENV == 'development'
          ? exception.stack
          : null
        : null;
    const payload =
      exception instanceof HttpException ? exception.getResponse() : {};

    let responseBody = {
      statusCode: httpStatus,
      timestamp: new Date().toISOString(),
      path: httpAdapter.getRequestUrl(ctx.getRequest()),
      method: httpAdapter.getRequestMethod(ctx.getRequest()),
      stackTrace: exception instanceof Error ? exception.message : null,
    };

    if (typeof payload === 'string') {
      responseBody['message'] = payload;
    } else {
      responseBody = {
        ...responseBody,
        ...payload,
      };
    }

    console.log({ error: responseBody });

    httpAdapter.reply(response, responseBody, httpStatus);
  }
}
