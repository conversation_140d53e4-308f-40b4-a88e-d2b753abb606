import { Global, Module } from '@nestjs/common';

/**
 * SharedModule contains shared services, utilities, and components
 * that can be used across different modules in the application.
 *
 * This module is global, meaning its providers are available
 * throughout the entire application without needing to import
 * the module in every feature module.
 */
@Global()
@Module({
  imports: [],
  providers: [
    // Add shared providers here (guards, interceptors, pipes, services)
  ],
  exports: [
    // Export providers that should be available globally
  ],
})
export class SharedModule {}
