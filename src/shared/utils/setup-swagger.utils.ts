import type { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

export function setupSwagger(
  app: INestApplication,
  config: ConfigService,
): void {
  const ENV = config.get('NODE_ENV');
  const PORT = config.get('PORT');

  const options = new DocumentBuilder()
    .setTitle('Smart Commissions API Documentationl')
    .setContact('Platcorp Mis', '', '')
    .setDescription(
      `APIs for the Smart Commissions application (${ENV} environment)`,
    )
    .setVersion('1.0')
    .addBearerAuth()
    .addServer(`http://localhost:${PORT}/`, 'local server')
    .addServer(`https://apps.commissions.co.ke/`, `${ENV} server`)
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('documentation', app, document, {
    customSiteTitle: 'Smart Commissions API Documentation',
    explorer: true,
    swaggerOptions: {
      operationsSorter: 'alpha',
      tagsSorter: 'alpha',
      filter: true,
      showRequestDuration: true,
    },
  });
}
