import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsN<PERSON>ber, Min, Max, IsString, IsDateString } from 'class-validator';

export function pagination(
  currentPage: number,
  pageSize: number,
): { take: number; skip: number } {
  const take = pageSize ? +pageSize : 100;
  const skip = (currentPage - 1) * pageSize;

  return {
    take,
    skip,
  };
}

export function getPagingData(
  data: { count: number; rows: any[] },
  currentPage: number,
  pageSize: number,
) {
  const { count: totalItems, rows: results } = data;

  const totalPages = Math.ceil(totalItems / pageSize);
  const nextPage = currentPage + 1 > totalPages ? null : currentPage + 1;
  const hasPreviousPage = currentPage > 1;
  const hasNextPage = currentPage < totalPages;
  const computedCurrentPage = currentPage > totalPages ? 1 : currentPage;

  return {
    totalItems,
    results,
    totalPages,
    currentPage: computedCurrentPage,
    hasNextPage,
    hasPreviousPage,
    nextPage,
    limit: pageSize,
  };
}

export class ResultsPaginated<T> {
  @ApiProperty()
  totalItems: number;

  @ApiProperty({ type: [Object], example: [] })
  results: T[];

  @ApiProperty()
  totalPages: number;

  @ApiProperty()
  currentPage: number;

  @ApiProperty()
  hasNextPage: boolean;

  @ApiProperty()
  hasPreviousPage: boolean;

  @ApiProperty()
  nextPage: number | null;

  @ApiProperty({ default: 100 })
  limit: number;
}

export class SearchQuery {
  @ApiProperty({ default: 1 })
  @Type(() => Number)
  @IsNumber()
  page: number;

  @ApiProperty({ default: 100 })
  @IsNumber()
  @Min(0)
  @Max(1000)
  @Type(() => Number)
  limit: number;
}

export class ReportQuery {
  @IsString()
  @IsDateString()
  @ApiProperty({ default: new Date().toISOString() })
  startDate: string;

  @IsString()
  @IsDateString()
  @ApiProperty({ default: new Date().toISOString() })
  endDate: string;
}
