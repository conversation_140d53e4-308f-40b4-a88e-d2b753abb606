// Export shared constants here
export enum NODE_ENV {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  STAGING = 'staging',
}

export const APP_NAME = 'smart_commission';
export const APP_STAGE = process.env.NODE_ENV || NODE_ENV.DEVELOPMENT;
export const USER_CREATED_EVENT = 'USER_CREATED_EVENT';
export const USER_FORGOT_PASSWORD = 'USER_FORGOT_PASSWORD';
export const USER_WELCOME_EMAIL = 'USER_WELCOME_EMAIL';

export const SEND_EMAIL_NOTIFICATION = 'SEND_EMAIL_NOTIFICATION';
export const SEND_MOBILE_NOTIFICATION = 'SEND_MOBILE_NOTIFICATION';
export const DEFAULT_SMS_VENDOR = process.env.DEFAULT_SMS_VENDOR;
export const SEND_SMS_NOTIFICATION = 'SEND_SMS_NOTIFICATION';
export const PHONE_NUMBER_REGEX = /^\+?[0-9]\d{10,14}$/;
export const PHONE_NUMBER_REGEX_2 = /^[1-9]\d{10,14}$/g;
export const WEB_PORTAL = process.env.WEB_PORTAL;

export const Q_NAME = {
  disbursement: APP_NAME + '_' + APP_STAGE + '_' + +'disbursement',
} as const;

export const RABBIT_MQ_CONFIG = {
  uri: process.env.RABBIT_MQ_URL || 'amqp://localhost:5672',
} as const;
