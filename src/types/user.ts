export interface User {
  id: number;
  isAdministrator: boolean;
  acceptTerms: boolean;
  tenantId: number;
  accountState: UserLifeCycle;
  fullName: string;
  isActive: boolean;
  email: string | null;
  emailVerified: boolean;
  verified: boolean;
  passwordReset: Date | null;
  lastPasswordResetDate: Date | null;
  createbyId: number | null;
  lastLoginDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface UsersEntity extends User {}

export interface UsersQuery {
  page?: number;
  limit?: number;
  email?: string;
  fullName?: string;
  isActive?: boolean;
  emailVerified?: boolean;
  verified?: boolean;
  tenantId?: number;
}

export interface PaginatedResponse<T> {
  totalItems: number;
  items: T[];
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  nextPage: number | null;
}

export enum UserLifeCycle {
  PARTIAL_APPLICATION = "PARTIAL_APPLICATION",
  PENDING_APPROVAL = "PENDING_APPROVAL",
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  REJECTED = "REJECTED",
  EXITED = "EXITED",
  BLACKLISTED = "BLACKLISTED",
}
