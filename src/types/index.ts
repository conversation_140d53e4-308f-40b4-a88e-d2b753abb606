import { UsersEntity } from "./user";


// Types for individual state properties
export interface ModalState {
    value: boolean;
    message: string;
    color: string;
}



export class SearchQuery {
    page: number = 1;
    limit: number = 100;
}



export interface ResultsPaginated<T> {
    totalItems: number;
    results: T[];
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextPage: number | null;
    limit: number;
}


export interface Pagination {
    // Adjust as per your pagination structur    
    totalItems: number;
    totalPages: number;
    currentPage: number
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextPage: number | null;
}

export interface State {
    currentUser: any | null; // Replace `any` with the actual user type, e.g., `User | null`
    isLoggedIn: boolean;
    modalState: ModalState;
    paginations: Pagination | any;
    currentTenant: any | null; // Replace `any` with the actual tenant type, e.g., `Tenant | null`
    groupings: any[]; // Replace `any` with the actual grouping type
    loader: boolean;
    isSearching: boolean;
    tenants: any[]; // Replace `any` with the actual tenant type
    reportTypes: any[]; // Replace `any` with the actual report type
    loanBook: any[]; // Replace `any` with the actual loan book type
    userPaginations: Pagination | any;
    users: UsersEntity[]; // Replace `any` with the actual user type
}
