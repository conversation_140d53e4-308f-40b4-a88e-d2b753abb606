
import { SearchQuery } from "."
export enum AccountState {
    PARTIAL_APPLICATION = 'partialApplication',
    COMPLETE = 'complete',
    PENDING = 'pending',
    APPROVED = 'approved',
    REJECTED = 'rejected'
}
export enum EntityType {
    USER = 'USER',
    ADMIN = 'ADMIN'
}
export class ClientQueryDto extends SearchQuery {
    email?: string
    mobilePhone?: string
    county?: string
    subCounty?: string
}
// create users
export class CreateClientDto {
    fullname: string
    email: string
    password: string
    confirmPassword: string
}
export class ClientEntity {
    id: number
    firstName: string
    lastName: string
    middleName: string | null
    birthDate: string
    maritalStatus: string
    accountState: AccountState
    acceptTerms: boolean
    entityType: EntityType
    email: string
    emailVerified: boolean
    verified: boolean
    userState: string
    mobilePhone: string
    residence: string
    county: string
    subCounty: string
    ward: string | null
    creationDate: Date
    lastModifiedDate: Date
    deletedAt: Date | null
} 