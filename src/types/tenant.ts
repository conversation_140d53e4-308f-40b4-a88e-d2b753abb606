export interface Tenant {
    id: string
    name: string
    subdomain: string
    createdAt: Date
    updatedAt: Date
}

export interface CreateTenantDto {
    name: string
    subdomain: string
}

export interface UpdateTenantDto {
    name?: string
    subdomain?: string
}

export interface UpdateTenantProfileDto {
    name?: string
}

export interface TenantQuery {
    name?: string
    subdomain?: string
    page?: number
    limit?: number
    sortBy?: 'name' | 'subdomain' | 'createdAt' | 'updatedAt'
    sortOrder?: 'asc' | 'desc'
}

export interface TenantSearchDto extends TenantQuery {
    search?: string
}

export interface TenantStatistics {
    id: string
    name: string
    subdomain: string
    totalUsers: number
    activeUsers: number
    createdAt: Date
}


// Export aliases for consistency
export type TenantsEntity = Tenant
export type TenantsQuery = TenantQuery
