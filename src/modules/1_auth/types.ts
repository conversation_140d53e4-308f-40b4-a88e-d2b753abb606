import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
export class LoginSuccess {
  @ApiProperty()
  access_token: string;
  @ApiProperty()
  refresh_token?: string;
  @ApiProperty({ type: SystemUserEntity })
  user: SystemUserEntity;
  @ApiProperty()
  requiresOtp?: boolean;
  @ApiProperty()
  message?: string;
}

export type Tokens = {
  access_token: string;
  refresh_token: string;
};

export class ToType {
  email: string;
}
export class Email {
  @Type(() => ToType)
  to: ToType[];
  subject: string;
  html: string;
  from: string;
}
