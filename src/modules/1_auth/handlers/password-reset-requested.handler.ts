import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
import { MailService } from '../infra/services/mail.service';

/**
 * Event handler for password reset requested events
 * Handles email notification when password reset is requested
 */
@Injectable()
export class PasswordResetRequestedHandler {
  private readonly logger = new Logger(PasswordResetRequestedHandler.name);

  constructor(private readonly mailService: MailService) {}

  @OnEvent('password.reset.requested')
  async handlePasswordResetRequested(event: {
    user: SystemUserEntity;
    origin: string;
  }): Promise<void> {
    try {
      this.logger.log(
        `Handling password reset requested event for user: ${event.user.email}`,
      );

      await this.mailService.sendPasswordResetEmail(event.user, event.origin);

      this.logger.log(`Password reset email sent to: ${event.user.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password reset email to ${event.user.email}:`,
        error,
      );
      // Don't throw error to prevent password reset failure due to email issues
    }
  }
}
