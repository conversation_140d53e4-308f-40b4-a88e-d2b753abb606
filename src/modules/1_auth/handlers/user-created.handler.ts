import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
import { MailService } from '../infra/services/mail.service';

/**
 * Event handler for user created events
 * Handles email verification when a new user is created
 */
@Injectable()
export class UserCreatedHandler {
  private readonly logger = new Logger(UserCreatedHandler.name);

  constructor(private readonly mailService: MailService) {}

  @OnEvent('user.created')
  async handleUserCreated(event: {
    user: SystemUserEntity;
    origin: string;
  }): Promise<void> {
    try {
      this.logger.log(
        `Handling user created event for user: ${event.user.email}`,
      );

      await this.mailService.sendVerificationEmail(event.user, event.origin);

      this.logger.log(`Verification email sent to: ${event.user.email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${event.user.email}:`,
        error,
      );
      // Don't throw error to prevent registration failure due to email issues
    }
  }
}
