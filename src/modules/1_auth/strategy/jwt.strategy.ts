import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from 'src/database/database.service';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    config: ConfigService,
    private readonly prisma: DatabaseService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false, // TODO only in testing
      secretOrKey: config.get('JWT_SECRET'),
    });
  }

  async validate(payload: {
    sub: string;
    email: string;
  }): Promise<Partial<SystemUserEntity & { sub: number }>> {
    if (!payload.email) {
      throw new BadRequestException('Invalid token');
    }

    const user = await this.prisma.systemUser.findUnique({
      where: {
        email: payload.email,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return { ...user, sub: user.id };
  }
}
