import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

@Injectable()
export class RefreshStrategy extends PassportStrategy(Strategy, 'jwt-refresh') {
  constructor(config: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: config.get('JWT_REFRESH_SECRET'),
      passReqToCallback: true,
    });
  }

  async validate(
    req: Request,
    payload: { sub: string; email: string },
  ): Promise<{ refreshToken: string; sub: string; email: string; id: string }> {
    const authHeader = req.get('authorization');
    if (!authHeader) {
      throw new Error('Authorization header is missing');
    }
    const refreshToken = authHeader.replace('Bearer', '').trim();

    return {
      id: payload.sub,
      refreshToken,
      ...payload,
    };
  }
}
