import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
import { CreateAuthDto } from '../dto/create-auth.dto';
import { LoginAuthDto } from '../dto/login.dto';
import { ResetPasswordDto } from '../dto/update-auth.dto';
import { LoginSuccess } from '../types';

/**
 * Simplified interfaces for auth domain services
 * These provide contracts without over-engineering
 */

export interface IAuthenticationDomain {
  registerUser(userData: CreateAuthDto, origin: string): Promise<LoginSuccess>;
  authenticateUser(credentials: LoginAuthDto): Promise<LoginSuccess>;
  logoutUser(userId: number): Promise<boolean>;
  refreshUserTokens(
    userId: number,
    refreshToken: string,
  ): Promise<{ access_token: string; refresh_token: string }>;
}

export interface IEmailVerificationDomain {
  verifyUserEmail(token: string): Promise<{ message: string }>;
  generateVerificationToken(): string;
}

export interface IPasswordResetDomain {
  initiatePasswordReset(
    email: string,
    origin: string,
  ): Promise<{ message: string }>;
  completePasswordReset(
    resetData: ResetPasswordDto,
  ): Promise<{ message: string }>;
  validateResetToken(token: string): Promise<SystemUserEntity>;
}
