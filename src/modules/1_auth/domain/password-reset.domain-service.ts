import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
import { ResetPasswordDto } from '../dto/update-auth.dto';

import { UserRepository } from '../infra/repositories/user.repository';
import { PasswordService } from '../infra/services/password.service';
import { TokenService } from '../infra/services/token.service';
import { EventPublisherService } from '../infra/services/event-publisher.service';

/**
 * Domain service for password reset operations
 * Handles password reset business logic
 */
@Injectable()
export class PasswordResetDomainService {
  private readonly logger = new Logger(PasswordResetDomainService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly passwordService: PasswordService,
    private readonly tokenService: TokenService,
    private readonly eventPublisher: EventPublisherService,
  ) {}

  /**
   * Initiate password reset process
   */
  async initiatePasswordReset(
    email: string,
    origin: string,
  ): Promise<{ message: string }> {
    const message =
      'if your email exists you will receive instructions to reset your password';

    // Find user by email
    const user = await this.userRepository.findUserByEmail(email);

    // Always return success message to prevent email enumeration
    if (!user) {
      return { message };
    }

    // Generate reset token with 24-hour expiration
    const resetToken = this.tokenService.generateRandomToken();
    const resetTokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);

    // Update user with reset token
    const updatedUser = await this.userRepository.updateUserResetToken(
      user.id,
      resetToken,
      resetTokenExpires,
    );

    // Publish domain event for email notification
    this.eventPublisher.publishPasswordResetRequested(
      SystemUserEntity.from(updatedUser),
      origin,
    );

    return { message };
  }

  /**
   * Complete password reset with token and new password
   */
  async completePasswordReset(
    resetData: ResetPasswordDto,
  ): Promise<{ message: string }> {
    // Validate reset token
    const user = await this.validateResetToken(resetData.token);

    // Hash new password
    const passwordHash = await this.passwordService.hashPassword(
      resetData.password,
    );

    // Update user password
    await this.userRepository.updateUserPassword(user.id, passwordHash);

    return { message: 'password updated you can now log in' };
  }

  /**
   * Validate reset token and return user if valid
   */
  async validateResetToken(token: string): Promise<SystemUserEntity> {
    const currentTime = new Date().toISOString();
    this.logger.log('Validating reset token at:', currentTime);

    const user = await this.userRepository.findUserByResetToken(
      token,
      currentTime,
    );

    if (!user) {
      throw new UnauthorizedException('Verification failed, token expired');
    }

    return user;
  }
}
