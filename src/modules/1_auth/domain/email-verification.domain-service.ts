import { Injectable, NotFoundException } from '@nestjs/common';
import * as crypto from 'crypto';
import { UserRepository } from '../infra/repositories/user.repository';
import { IEmailVerificationDomain } from '../interfaces/auth-domain.interface';

/**
 * Domain service for email verification operations
 * Handles email verification business logic
 */
@Injectable()
export class EmailVerificationDomainService
  implements IEmailVerificationDomain
{
  constructor(private readonly userRepository: UserRepository) {}

  /**
   * Verify user email with verification token
   */
  async verifyUserEmail(token: string): Promise<{ message: string }> {
    const user = await this.userRepository.findUserByVerificationToken(token);

    if (!user) {
      throw new NotFoundException('verification failed');
    }

    await this.userRepository.updateUserVerification(user.id);

    return { message: 'verification successful you can now login' };
  }

  /**
   * Generate a random verification token
   */
  generateVerificationToken(): string {
    return crypto.randomBytes(40).toString('hex');
  }
}
