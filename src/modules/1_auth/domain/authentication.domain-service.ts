import {
  ConflictException,
  ForbiddenException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
import { CreateAuthDto } from '../dto/create-auth.dto';
import { LoginAuthDto } from '../dto/login.dto';
import { LoginSuccess } from '../types';
import { UserRepository } from '../infra/repositories/user.repository';
import { PasswordService } from '../infra/services/password.service';
import { TokenService } from '../infra/services/token.service';
import { EventPublisherService } from '../infra/services/event-publisher.service';
import { IAuthenticationDomain } from '../interfaces/auth-domain.interface';

/**
 * Domain service for authentication operations
 * Handles core authentication business logic without external dependencies
 */
@Injectable()
export class AuthenticationDomainService implements IAuthenticationDomain {
  private readonly logger = new Logger(AuthenticationDomainService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly passwordService: PasswordService,
    private readonly tokenService: TokenService,
    private readonly eventPublisher: EventPublisherService,
  ) {}

  /**
   * Register a new user with email verification
   */
  async registerUser(
    userData: CreateAuthDto,
    origin: string,
  ): Promise<LoginSuccess> {
    try {
      // Check if user already exists (email is required for registration)
      if (!userData.email) {
        throw new ConflictException('Email is required for registration');
      }

      const existingUser = await this.userRepository.findUserByEmail(
        userData.email,
      );
      if (existingUser) {
        throw new ConflictException('Credentials taken');
      }

      // Hash password and create user
      const passwordHash = await this.passwordService.hashPassword(
        userData.password,
      );
      const verificationToken = this.tokenService.generateRandomToken();

      const newUser = await this.userRepository.createUser({
        ...userData,
        passwordHash,
        verificationToken,
      });

      // Generate authentication tokens
      const userEmail = newUser.email || userData.email;
      if (!userEmail) {
        throw new ConflictException('User email is required');
      }

      const tokens = await this.tokenService.generateTokens(
        newUser.id,
        userEmail,
      );

      // Update refresh token
      await this.tokenService.updateRefreshToken(
        newUser.id,
        tokens.refresh_token,
      );

      // Publish domain event
      this.eventPublisher.publishUserCreated(newUser, origin);

      // Get updated user data
      const updatedUser = await this.userRepository.findUserById(newUser.id);
      if (!updatedUser) {
        throw new ConflictException('Failed to retrieve created user');
      }

      return {
        ...tokens,
        user: SystemUserEntity.from(updatedUser),
      };
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ForbiddenException('Credentials taken');
        }
      }
      throw error;
    }
  }

  /**
   * Authenticate user with email and password
   */
  async authenticateUser(credentials: LoginAuthDto): Promise<LoginSuccess> {
    // Find user by email
    const user = await this.userRepository.findUserByEmail(
      credentials.email,
      true,
    );
    if (!user) {
      throw new ForbiddenException('Credentials Incorrect');
    }

    // Verify password
    const passwordMatches = await this.passwordService.verifyPassword(
      user.passwordHash,
      credentials.password,
    );
    if (!passwordMatches) {
      throw new ForbiddenException('Credentials Incorrect');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new ForbiddenException('User is not active');
    }

    // Generate tokens
    const tokens = await this.tokenService.generateTokens(user.id, user.email!);

    // Update refresh token
    await this.tokenService.updateRefreshToken(user.id, tokens.refresh_token);

    return {
      ...tokens,
      user: SystemUserEntity.from(user),
      requiresOtp: false,
      message: 'success',
    };
  }

  /**
   * Logout user by clearing refresh token
   */
  async logoutUser(userId: number): Promise<boolean> {
    await this.userRepository.clearUserRefreshToken(userId);
    return true;
  }

  /**
   * Refresh authentication tokens
   */
  async refreshUserTokens(
    userId: number,
    refreshToken: string,
  ): Promise<{ access_token: string; refresh_token: string }> {
    const user = await this.userRepository.findUserById(userId);
    if (!user || !user.refreshHashedToken) {
      throw new ForbiddenException('User not found');
    }

    // Verify refresh token
    const tokenMatches = await this.passwordService.verifyPassword(
      user.refreshHashedToken,
      refreshToken,
    );
    if (!tokenMatches) {
      throw new ForbiddenException('Invalid refresh token');
    }

    // Generate new tokens
    const tokens = await this.tokenService.generateTokens(user.id, user.email!);

    // Update refresh token
    await this.tokenService.updateRefreshToken(user.id, tokens.refresh_token);

    return tokens;
  }
}
