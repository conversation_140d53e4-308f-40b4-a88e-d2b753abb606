import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './controllers/auth.controller';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { JwtStrategy, RefreshStrategy } from './strategy';
import { AuthRepository } from './infra/repositories/auth.repot';
import { AuthService } from './infra/services/auth.service';
import { UserModule } from '../2_user/user.module';
import { AuthenticationDomainService } from './domain/authentication.domain-service';
import { EmailVerificationDomainService } from './domain/email-verification.domain-service';
import { PasswordResetDomainService } from './domain/password-reset.domain-service';
import { TokenService } from './infra/services/token.service';
import { PasswordService } from './infra/services/password.service';
import { UserRepository } from './infra/repositories/user.repository';
import { EventPublisherService } from './infra/services/event-publisher.service';
import { UserCreatedHandler } from './handlers/user-created.handler';
import { PasswordResetRequestedHandler } from './handlers/password-reset-requested.handler';
import { MailService } from './infra/services/mail.service';

@Module({
  controllers: [AuthController],
  providers: [
    // Application Service
    AuthService,

    // Infrastructure Services (concrete implementations)
    PasswordService,
    EventPublisherService,
    MailService,
    AuthRepository,
    UserRepository,
    {
      provide: TokenService,
      useFactory: (
        userRepo: UserRepository,
        jwt: JwtService,
        config: ConfigService,
      ) => {
        return new TokenService(userRepo, jwt, config);
      },
      inject: [UserRepository, JwtService, ConfigService],
    },

    // Domain Services with explicit dependencies
    {
      provide: AuthenticationDomainService,
      useFactory: (
        userRepo: UserRepository,
        passwordService: PasswordService,
        tokenService: TokenService,
        eventPublisher: EventPublisherService,
      ) => {
        return new AuthenticationDomainService(
          userRepo,
          passwordService,
          tokenService,
          eventPublisher,
        );
      },
      inject: [
        UserRepository,
        PasswordService,
        TokenService,
        EventPublisherService,
      ],
    },
    {
      provide: EmailVerificationDomainService,
      useFactory: (userRepo: UserRepository) => {
        return new EmailVerificationDomainService(userRepo);
      },
      inject: [UserRepository],
    },
    {
      provide: PasswordResetDomainService,
      useFactory: (
        userRepo: UserRepository,
        passwordService: PasswordService,
        tokenService: TokenService,
        eventPublisher: EventPublisherService,
      ) => {
        return new PasswordResetDomainService(
          userRepo,
          passwordService,
          tokenService,
          eventPublisher,
        );
      },
      inject: [
        UserRepository,
        PasswordService,
        TokenService,
        EventPublisherService,
      ],
    },

    // Event Handlers
    UserCreatedHandler,
    PasswordResetRequestedHandler,

    // Legacy providers
    JwtStrategy,
    RefreshStrategy,
  ],
  imports: [JwtModule.register({}), UserModule],
  exports: [AuthService],
})
export class AuthModule {}
