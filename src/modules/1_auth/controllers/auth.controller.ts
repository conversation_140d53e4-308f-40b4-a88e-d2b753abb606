import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Query,
  Get,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Request } from 'express';
import { Public } from 'src/shared/decorators';
import { GetCurrentUser } from '../decorator';
import { CreateAuthDto } from '../dto/create-auth.dto';
import { LoginAuthDto } from '../dto/login.dto';
import {
  ForgotPassDto,
  ResetPasswordDto,
  UpdatePasswordDto,
} from '../dto/update-auth.dto';
import { AuthEntity } from '../entities/auth.entity';
import { JwtGuard, RtGuard } from '../guard';
import { AuthService } from '../infra/services/auth.service';
import { LoginSuccess } from '../types';
import { UpdateEmailDto } from '../dto/update-email.dto';
import { AdminGuard } from 'src/shared/guards/admin.guard';
import { UserService } from '../../2_user/user.service';
import { AuthenticationProvider } from '../infra/providers';

@ApiTags('1_auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userService: UserService,
  ) {}

  @Post('signup')
  @ApiCreatedResponse({
    type: LoginSuccess,
  })
  @ApiBody({
    type: CreateAuthDto,
    description: 'Provide data based on the user CreateUserDTO',
  })
  @Public()
  signup(
    @Body() createAuthDto: CreateAuthDto,
    @Req() request: Request,
  ): Promise<LoginSuccess> {
    const origin = request.get('origin') || '';
    return this.authService.signup(createAuthDto, origin);
  }

  /**
   *
   * login to your account using email and password
   */
  @HttpCode(HttpStatus.OK)
  @Post('signin')
  @ApiOkResponse({
    type: LoginSuccess,
  })
  @Public()
  login(@Body() loginAuthDto: LoginAuthDto): Promise<LoginSuccess> {
    return this.authService.signin(loginAuthDto);
  }

  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Post('logout')
  @UseGuards(JwtGuard)
  logout(@GetCurrentUser('id') userId: number) {
    return this.authService.logout(userId);
  }

  @ApiBearerAuth()
  @ApiOkResponse({
    type: AuthEntity,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid jwt token used for refreshtoken',
  })
  @Public()
  @HttpCode(HttpStatus.OK)
  @Post('refresh-token')
  @UseGuards(RtGuard)
  refreshToken(
    @GetCurrentUser('id') userId: number,
    //FIXME: it will be there just need to overule typescript
    //@ts-ignore
    @GetCurrentUser('refreshToken') refreshToken: string,
  ) {
    return this.authService.refreshTokens(userId, refreshToken);
  }

  @ApiOperation({ description: 'Verify email using verification token' })
  @ApiOkResponse({
    description: 'verification successfull',
  })
  @ApiResponse({
    status: 404,
    description: 'verification failed',
  })
  @Get('verify-email')
  @Public()
  verifyEmail(@Query('token') token: string) {
    return this.authService.verifyEmail(token);
  }

  @ApiOperation({
    summary: 'initiate password reset',
    description: 'provide user email to trigger password resets',
  })
  @ApiOkResponse({
    description: 'check your email for password reset instructions',
  })
  @ApiResponse({
    status: 404,
    description: 'verification failed',
  })
  @Post('forgot-password')
  @Public()
  forgotPassword(
    @Body() forgotPassDto: ForgotPassDto,
    @Req() request: Request,
  ) {
    const origin = request.get('origin') || '';
    return this.authService.forgotPassword(forgotPassDto.email, origin);
  }

  @ApiOperation({
    summary: 'reset password',
    description: 'provide the token from the email and the new password',
  })
  @ApiOkResponse({
    description: 'password updated you can now log in',
  })
  @ApiResponse({
    status: 404,
    description: 'verification failed, token expired',
  })
  @Post('reset-password')
  @Public()
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Post('change-email')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Change email for user',
    description: 'Change email for user, admin only',
  })
  @ApiBody({ type: UpdateEmailDto })
  @UseGuards(JwtGuard, AdminGuard)
  changeEmail(@Body() updateEmailDto: UpdateEmailDto) {
    return this.userService.changeUserEmail(
      updateEmailDto.userId,
      updateEmailDto.email,
    );
  }

  @Post('change-password')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Change password for user',
    description: 'Change password for user, admin only',
  })
  @ApiBody({ type: UpdatePasswordDto })
  @UseGuards(JwtGuard, AdminGuard)
  async changePassword(@Body() updatePasswordDto: UpdatePasswordDto) {
    // Hash the password before sending to user service
    const passwordHash = await AuthenticationProvider.generateHash(
      updatePasswordDto.password,
    );
    return this.userService.changeUserPassword(
      updatePasswordDto.userId,
      passwordHash,
    );
  }
}
