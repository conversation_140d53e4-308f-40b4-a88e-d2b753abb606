import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';

export const GetCurrentUser = createParamDecorator(
  (key: keyof SystemUserEntity, ctx: ExecutionContext) => {
    const request: Express.Request & { user: SystemUserEntity } = ctx
      .switchToHttp()
      .getRequest();

    if (!key) return request.user;

    return request.user[key];
  },
);
