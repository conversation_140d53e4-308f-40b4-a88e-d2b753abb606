import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';

export class CreateAuthDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  fullName: string;

  @IsEmail()
  @IsOptional()
  @ApiProperty({ required: false })
  email?: string;

  @Length(8, 50)
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  password: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  tenantId: number;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ default: true })
  isActive?: boolean = true;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ default: true })
  acceptTerms?: boolean = true;

  constructor(acceptTerms?: boolean) {
    this.acceptTerms = acceptTerms || true;
  }

  static toSystemUser(
    dto: CreateAuthDto & { passwordHash: string; verificationToken?: string },
  ) {
    return {
      tenantId: dto.tenantId,
      passwordHash: dto.passwordHash,
      fullName: dto.fullName,
      isActive: dto.isActive ?? true,
      email: dto.email,
      emailVerified: false,
      verified: false,
      verificationToken: dto.verificationToken,
    };
  }
}
