import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as argon from 'argon2';

export class AuthenticationProvider {
  static async generateHash(password: string): Promise<string> {
    return await argon.hash(password);
  }

  static async verifyHash(hash: string, password: string): Promise<boolean> {
    return await argon.verify(hash, password);
  }

  static async comparePassword(
    hash: string,
    password: string,
  ): Promise<boolean> {
    return await argon.verify(hash, password);
  }

  static async signToken(payload: {
    userId: number;
    email: string;
    config: ConfigService;
    jwt: JwtService;
  }): Promise<{ access_token: string; refresh_token: string }> {
    const { config, jwt } = payload;
    const data = {
      sub: payload.userId,
      email: payload.email,
      userId: payload.userId,
    };

    const secret = config.get('JWT_SECRET');
    const refresh_secret = config.get('JWT_REFRESH_SECRET');

    const token = jwt.signAsync(data, {
      expiresIn: '1d',
      secret,
    });

    const rf_token = jwt.signAsync(data, {
      expiresIn: '7d',
      secret: refresh_secret,
    });

    const [access_token, refresh_token] = await Promise.all([token, rf_token]);

    return {
      access_token,
      refresh_token,
    };
  }
}
