import { Injectable } from '@nestjs/common';
import { AuthenticationProvider } from '../providers';

/**
 * Infrastructure service for password operations
 * Handles password hashing and verification
 */
@Injectable()
export class PasswordService {
  /**
   * Hash a plain text password
   */
  async hashPassword(password: string): Promise<string> {
    return AuthenticationProvider.generateHash(password);
  }

  /**
   * Verify a password against its hash
   */
  async verifyPassword(hash: string, password: string): Promise<boolean> {
    return AuthenticationProvider.comparePassword(hash, password);
  }
}
