import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import * as sgMail from '@sendgrid/mail';
import { SEND_EMAIL_NOTIFICATION } from 'src/shared';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';

// Local email interface for mail service
interface Email {
  to: { email: string }[];
  subject: string;
  html: string;
  from: string;
}

export class SendEmailEvent {
  constructor(
    public readonly data: sgMail.MailDataRequired | sgMail.MailDataRequired[],
  ) {}
}
@Injectable()
export class MailService {
  private logger = new Logger(MailService.name);
  private from: string;

  constructor(
    private config: ConfigService,
    private eventEmitter: EventEmitter2,
  ) {
    sgMail.setApiKey(this.config.get<string>('EMAIL_PASSWORD') || '');
    this.from = this.config.get('SENDER_EMAIL') || '';
  }

  async sendMail(
    data: Email | sgMail.MailDataRequired | sgMail.MailDataRequired[],
  ) {
    this.eventEmitter.emit(
      SEND_EMAIL_NOTIFICATION,
      new SendEmailEvent(data as sgMail.MailDataRequired),
    );

    return 'done';
  }

  async sendAlreadyRegisteredEmail(email: string, origin: string) {
    let message: string;
    if (origin) {
      message = `<p>If you don't know your password please visit the <a href="${this.config.get<string>('WEB_PORTAL')}/forgot-password">forgot password</a> page.</p>`;
    } else {
      message = `<p>If you don't know your password you can reset it via the <code>/api/auth/forgot-password</code> api route.</p>`;
    }

    await this.sendMail({
      to: [
        {
          email,
        },
      ],
      subject: 'Sign-up Verification API - Email Already Registered',
      html: `<h4>Email Already Registered</h4>
               <p>Your email <strong>${email}</strong> is already registered.</p>
               ${message}`,
      from: this.from,
    });
  }

  async sendPasswordResetEmail(account: SystemUserEntity, origin: string) {
    let message: string;
    if (origin) {
      const _origin = this.config.get<string>('WEB_PORTAL');
      const resetUrl = `${_origin}/reset-password/${account.resetToken}`;
      message = `
            <p>This email was sent automatically in response to your request to recover your password. This is done for your protection; only you, the recipient of this email can take the next step in the password recovery process.
            To reset your password and access your account copy and paste the following link into the address bar of your browser
             <p><a href="${resetUrl}">${resetUrl}</a></p>

            This link can only be used once and expires after 1 day.</p>`;
    } else {
      message = `<p>Please use the below token to reset your password with the <code>/api/auth/reset-password</code> api route:</p>
                   <p><code>${account.resetToken}</code></p>`;
    }

    await this.sendMail({
      to: [
        {
          email: account.email || '',
        },
      ],
      subject: 'Sign-up Verification API - Reset Password',
      html: `<h4>Reset Password Email</h4>
               ${message}`,
      from: this.from,
    });
  }

  async sendVerificationEmail(account: SystemUserEntity, origin: string = '') {
    let message = '';
    message += `<p>Your login credential email: ${account.email}</p>`;
    if (origin) {
      const verifyUrl = `${this.config.get<string>('WEB_PORTAL')}/verify-email/${account.verificationToken}`;
      message += `<p>Please click the below link to verify your email address:</p>
                   <p><a href="${verifyUrl}">${verifyUrl}</a></p>`;
    } else {
      message += `<p>Please use the below token to verify your email address with the <code>/api/auth/verify-email</code> api route:</p>
                   <p><code>${account.verificationToken}</code></p>`;
    }

    await this.sendMail({
      to: [
        {
          email: account.email || '',
        },
      ],
      subject: 'Sign-up Verification API - Verify Email',
      html: `<h4>Verify Email</h4>
               <p>Welcome ${account.fullName}!</p>
               ${message}`,
      from: this.from,
    });
  }

  @OnEvent(SEND_EMAIL_NOTIFICATION)
  private async _sendEmail(payload: SendEmailEvent) {
    const { data } = payload;
    try {
      // const sent = await this.mailService.sendMail({
      //     ...data,
      //     from: data.from ? data.from : this.config.get('SENDER_EMAIL')
      // })

      const sent = await sgMail.send(data);
      this.logger.log('sendEmailDone');

      return sent;
    } catch (error) {
      this.logger.debug('sendEmail', error);
    }
  }
}
