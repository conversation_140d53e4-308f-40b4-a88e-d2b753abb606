import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as crypto from 'crypto';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
import { AuthenticationProvider } from '../providers';

import { UserRepository } from '../repositories/user.repository';

/**
 * Infrastructure service for token operations
 * Handles JWT and refresh token management
 */
@Injectable()
export class TokenService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly jwt: JwtService,
    private readonly config: ConfigService,
  ) {}

  /**
   * Generate access and refresh tokens for user
   */
  async generateTokens(
    userId: number,
    email: string,
  ): Promise<{ access_token: string; refresh_token: string }> {
    return AuthenticationProvider.signToken({
      userId,
      email,
      config: this.config,
      jwt: this.jwt,
    });
  }

  /**
   * Update user's refresh token in database
   */
  async updateRefreshToken(
    userId: number,
    refreshToken: string,
  ): Promise<SystemUserEntity> {
    const hash = await AuthenticationProvider.generateHash(refreshToken);
    const user = await this.userRepository.updateUserRefreshToken(userId, hash);
    return SystemUserEntity.from(user);
  }

  /**
   * Generate a cryptographically secure random token
   */
  generateRandomToken(): string {
    return crypto.randomBytes(40).toString('hex');
  }
}
