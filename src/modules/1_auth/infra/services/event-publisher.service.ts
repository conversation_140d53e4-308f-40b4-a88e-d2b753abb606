import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';

/**
 * Infrastructure service for publishing domain events
 * Decouples domain logic from event handling
 */
@Injectable()
export class EventPublisherService {
  constructor(private readonly eventEmitter: EventEmitter2) {}

  /**
   * Publish user created event for email verification
   */
  publishUserCreated(user: SystemUserEntity, origin: string): void {
    this.eventEmitter.emit('user.created', {
      user,
      origin,
    });
  }

  /**
   * Publish password reset requested event for email notification
   */
  publishPasswordResetRequested(user: SystemUserEntity, origin: string): void {
    this.eventEmitter.emit('password.reset.requested', {
      user,
      origin,
    });
  }
}
