import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { AuthenticationDomainService } from '../../domain/authentication.domain-service';
import { EmailVerificationDomainService } from '../../domain/email-verification.domain-service';
import { PasswordResetDomainService } from '../../domain/password-reset.domain-service';
import { CreateAuthDto } from '../../dto/create-auth.dto';
import { LoginAuthDto } from '../../dto/login.dto';

describe('AuthService (Refactored)', () => {
  let service: AuthService;
  let authenticationDomain: jest.Mocked<AuthenticationDomainService>;
  let emailVerificationDomain: jest.Mocked<EmailVerificationDomainService>;
  let passwordResetDomain: jest.Mocked<PasswordResetDomainService>;

  beforeEach(async () => {
    const mockAuthenticationDomain = {
      registerUser: jest.fn(),
      authenticateUser: jest.fn(),
      logoutUser: jest.fn(),
      refreshUserTokens: jest.fn(),
    };

    const mockEmailVerificationDomain = {
      verifyUserEmail: jest.fn(),
      generateVerificationToken: jest.fn(),
    };

    const mockPasswordResetDomain = {
      initiatePasswordReset: jest.fn(),
      completePasswordReset: jest.fn(),
      validateResetToken: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: AuthenticationDomainService,
          useValue: mockAuthenticationDomain,
        },
        {
          provide: EmailVerificationDomainService,
          useValue: mockEmailVerificationDomain,
        },
        {
          provide: PasswordResetDomainService,
          useValue: mockPasswordResetDomain,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    authenticationDomain = module.get(AuthenticationDomainService);
    emailVerificationDomain = module.get(EmailVerificationDomainService);
    passwordResetDomain = module.get(PasswordResetDomainService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('signup', () => {
    it('should delegate to authentication domain service', async () => {
      const createAuthDto: CreateAuthDto = {
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Test User',
        tenantId: 'tenant-1',
      };
      const origin = 'http://localhost:3000';
      const expectedResult = {
        access_token: 'token',
        refresh_token: 'refresh',
        user: {} as any,
      };

      authenticationDomain.registerUser.mockResolvedValue(expectedResult);

      const result = await service.signup(createAuthDto, origin);

      expect(authenticationDomain.registerUser).toHaveBeenCalledWith(
        createAuthDto,
        origin,
      );
      expect(result).toBe(expectedResult);
    });
  });

  describe('signin', () => {
    it('should delegate to authentication domain service', async () => {
      const loginDto: LoginAuthDto = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const expectedResult = {
        access_token: 'token',
        refresh_token: 'refresh',
        user: {} as any,
        requiresOtp: false,
        message: 'success',
      };

      authenticationDomain.authenticateUser.mockResolvedValue(expectedResult);

      const result = await service.signin(loginDto);

      expect(authenticationDomain.authenticateUser).toHaveBeenCalledWith(
        loginDto,
      );
      expect(result).toBe(expectedResult);
    });
  });

  describe('logout', () => {
    it('should delegate to authentication domain service', async () => {
      const userId = 'user-123';
      authenticationDomain.logoutUser.mockResolvedValue(true);

      const result = await service.logout(userId);

      expect(authenticationDomain.logoutUser).toHaveBeenCalledWith(userId);
      expect(result).toBe(true);
    });
  });
});
