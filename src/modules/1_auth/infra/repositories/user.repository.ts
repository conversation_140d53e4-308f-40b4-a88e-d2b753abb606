import { Injectable } from '@nestjs/common';
import { SystemUserEntity } from 'src/modules/2_user/entities/user.entity';
import { AuthRepository } from './auth.repot';

/**
 * Infrastructure repository for user data access
 * Delegates to AuthRepository for actual data operations
 */
@Injectable()
export class UserRepository {
  constructor(private readonly authRepository: AuthRepository) {}

  async findUserByEmail(
    email: string,
    showHash: boolean = false,
  ): Promise<SystemUserEntity | null> {
    const user = await this.authRepository.findUserByEmail(email);
    return user ? SystemUserEntity.from(user, showHash) : null;
  }

  async findUserById(id: number): Promise<SystemUserEntity | null> {
    const user = await this.authRepository.findUserById(id);
    return user ? SystemUserEntity.from(user) : null;
  }

  async findUserByVerificationToken(
    token: string,
  ): Promise<SystemUserEntity | null> {
    const user = await this.authRepository.findUserByVerificationToken(token);
    return user ? SystemUserEntity.from(user) : null;
  }

  async findUserByResetToken(
    token: string,
    currentTime: string,
  ): Promise<SystemUserEntity | null> {
    const user = await this.authRepository.validateUserByResetToken(
      token,
      currentTime,
    );
    return user ? SystemUserEntity.from(user) : null;
  }

  async createUser(userData: any): Promise<SystemUserEntity> {
    const user = await this.authRepository.createUser(userData);
    return SystemUserEntity.from(user);
  }

  async updateUserRefreshToken(
    userId: number,
    hashedToken: string,
  ): Promise<SystemUserEntity> {
    const user = await this.authRepository.updateUserRefreshToken(
      userId,
      hashedToken,
    );
    return SystemUserEntity.from(user);
  }

  async updateUserVerification(userId: number): Promise<void> {
    await this.authRepository.updateUserVerification(userId);
  }

  async updateUserResetToken(
    userId: number,
    token: string,
    expires: Date,
  ): Promise<SystemUserEntity> {
    const user = await this.authRepository.updateUserResetToken(
      userId,
      token,
      expires,
    );
    return SystemUserEntity.from(user);
  }

  async updateUserPassword(
    userId: number,
    passwordHash: string,
  ): Promise<void> {
    await this.authRepository.updateUserPassword(userId, passwordHash);
  }

  async clearUserRefreshToken(userId: number): Promise<void> {
    await this.authRepository.clearUserRefreshToken(userId);
  }
}
