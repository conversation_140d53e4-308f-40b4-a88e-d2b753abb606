import { Injectable } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { CreateAuthDto } from '../../dto/create-auth.dto';

@Injectable()
export class AuthRepository {
  constructor(private prisma: DatabaseService) {}

  async updateUserEmail(userId: number, email: string) {
    return this.prisma.systemUser.update({
      where: { id: userId },
      data: { email },
    });
  }

  async findUserById(userId: number) {
    return this.prisma.systemUser.findUnique({
      where: { id: userId },
    });
  }

  async findUserByEmail(email: string) {
    return this.prisma.systemUser.findUnique({
      where: { email },
    });
  }

  async createUser(
    data: CreateAuthDto & { passwordHash: string; verificationToken?: string },
  ) {
    return this.prisma.systemUser.create({
      data: CreateAuthDto.toSystemUser({
        ...data,
        passwordHash: data.passwordHash,
        verificationToken: data.verificationToken,
      }),
    });
  }

  async updateUserRefreshToken(userId: number, refreshHashedToken: string) {
    return this.prisma.systemUser.update({
      where: { id: userId },
      data: { refreshHashedToken },
    });
  }

  async updateUserVerification(userId: number) {
    return this.prisma.systemUser.update({
      where: { id: userId },
      data: {
        verificationToken: null,
        emailVerified: true,
        verified: true,
      },
    });
  }

  async clearUserRefreshToken(userId: number) {
    return this.prisma.systemUser.update({
      where: { id: userId },
      data: { refreshHashedToken: null },
    });
  }

  async updateUserResetToken(
    userId: number,
    resetToken: string,
    resetTokenExpires: Date,
  ) {
    return this.prisma.systemUser.update({
      where: { id: userId },
      data: {
        resetToken,
        resetTokenExpires,
      },
    });
  }

  async updateUserPassword(userId: number, passwordHash: string) {
    return this.prisma.systemUser.update({
      where: { id: userId },
      data: {
        passwordHash,
        resetToken: null,
        passwordReset: new Date(),
        lastPasswordResetDate: new Date(),
      },
    });
  }

  async validateUserByResetToken(
    resetToken: string,
    resetTokenExpires: string,
  ) {
    return this.prisma.systemUser.findFirst({
      where: {
        resetToken,
        resetTokenExpires: {
          gt: resetTokenExpires,
        },
      },
    });
  }

  async findUserByVerificationToken(token: string) {
    return this.prisma.systemUser.findFirst({
      where: { verificationToken: token },
    });
  }

  async deleteUser(userId: number) {
    return this.prisma.systemUser.delete({
      where: { id: userId },
    });
  }
}
