import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateTenantDto } from './dto/create-tenant.dto';
import {
  UpdateTenantDto,
  UpdateTenantProfileDto,
} from './dto/update-tenant.dto';
import { TenantQueryDto, TenantSearchDto } from './dto/tenant-query.dto';
import { TenantResponseEntity, TenantEntity } from './entities/tenant.entity';
import { Prisma } from '@prisma/client';
import {
  ResultsPaginated,
  pagination,
  getPagingData,
} from 'src/shared/paginations/paginations';
import { DatabaseService } from 'src/database/database.service';

/**
 * Service for managing Tenant operations
 * Implements business logic for tenant management following SOLID principles
 */
@Injectable()
export class TenantService {
  constructor(private readonly prisma: DatabaseService) {}

  /**
   * Create a new tenant
   * @param createTenantDto - Tenant creation data
   * @returns Created tenant response
   * @throws ConflictException if subdomain already exists
   */
  async create(
    createTenantDto: CreateTenantDto,
  ): Promise<TenantResponseEntity> {
    // Check if subdomain already exists
    const existingTenant = await this.findBySubdomain(
      createTenantDto.subdomain,
    );
    if (existingTenant) {
      throw new ConflictException(
        `Tenant with subdomain '${createTenantDto.subdomain}' already exists`,
      );
    }

    const tenant = await this.prisma.tenant.create({
      data: {
        name: createTenantDto.name,
        subdomain: createTenantDto.subdomain.toLowerCase(),
      },
    });

    return TenantResponseEntity.from(tenant);
  }

  /**
   * Find all tenants with optional filtering and pagination
   * @param query - Query parameters for filtering and pagination
   * @returns Paginated list of tenants
   */
  async findAll(
    query: Partial<TenantQueryDto>,
  ): Promise<ResultsPaginated<TenantEntity>> {
    const {
      page = 1,
      limit = 10,
      name,
      subdomain,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;
    const { take, skip } = pagination(page, limit);

    // Build where clause for filtering
    const where: Prisma.TenantWhereInput = {};

    if (name) {
      where.name = {
        contains: name,
        mode: 'insensitive',
      };
    }

    if (subdomain) {
      where.subdomain = {
        contains: subdomain,
        mode: 'insensitive',
      };
    }

    // Build order by clause
    const orderBy: Prisma.TenantOrderByWithRelationInput = {
      [sortBy]: sortOrder,
    };

    const [tenants, count] = await Promise.all([
      this.prisma.tenant.findMany({
        where,
        take,
        skip,
        orderBy,
      }),
      this.prisma.tenant.count({ where }),
    ]);

    const results = tenants.map((tenant) => TenantEntity.from(tenant));

    return getPagingData({ count, rows: results }, page, limit);
  }

  /**
   * Search tenants with advanced filtering
   * @param searchDto - Search parameters
   * @returns Paginated search results
   */
  async search(
    searchDto: Partial<TenantSearchDto>,
  ): Promise<ResultsPaginated<TenantEntity>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = searchDto;
    const { take, skip } = pagination(page, limit);

    // Build where clause for search
    const where: Prisma.TenantWhereInput = {};

    if (search) {
      where.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          subdomain: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Build order by clause
    const orderBy: Prisma.TenantOrderByWithRelationInput = {
      [sortBy]: sortOrder,
    };

    const [tenants, count] = await Promise.all([
      this.prisma.tenant.findMany({
        where,
        take,
        skip,
        orderBy,
      }),
      this.prisma.tenant.count({ where }),
    ]);

    const results = tenants.map((tenant) => TenantEntity.from(tenant));

    return getPagingData({ count, rows: results }, page, limit);
  }

  /**
   * Find a single tenant by ID
   * @param id - Tenant ID
   * @returns Tenant entity
   * @throws NotFoundException if tenant not found
   */
  async findOne(id: number): Promise<TenantEntity> {
    const tenant = await this.prisma.tenant.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            systemUsers: true,
          },
        },
      },
    });

    if (!tenant) {
      throw new NotFoundException(`Tenant with ID '${id}' not found`);
    }

    return TenantEntity.from(tenant);
  }

  /**
   * Find a tenant by subdomain
   * @param subdomain - Tenant subdomain
   * @returns Tenant entity or null if not found
   */
  async findBySubdomain(subdomain: string): Promise<TenantEntity | null> {
    const tenant = await this.prisma.tenant.findUnique({
      where: { subdomain: subdomain.toLowerCase() },
    });

    return tenant ? TenantEntity.from(tenant) : null;
  }

  /**
   * Update a tenant
   * @param id - Tenant ID
   * @param updateTenantDto - Update data
   * @returns Updated tenant response
   * @throws NotFoundException if tenant not found
   * @throws ConflictException if subdomain already exists
   */
  async update(
    id: number,
    updateTenantDto: UpdateTenantDto,
  ): Promise<TenantResponseEntity> {
    await this.findOne(id); // Ensure tenant exists

    // Check subdomain uniqueness if being updated
    if (updateTenantDto.subdomain) {
      const existingTenant = await this.findBySubdomain(
        updateTenantDto.subdomain,
      );
      if (existingTenant && existingTenant.id !== id) {
        throw new ConflictException(
          `Tenant with subdomain '${updateTenantDto.subdomain}' already exists`,
        );
      }
    }

    const updatedTenant = await this.prisma.tenant.update({
      where: { id },
      data: {
        ...(updateTenantDto.name && { name: updateTenantDto.name }),
        ...(updateTenantDto.subdomain && {
          subdomain: updateTenantDto.subdomain.toLowerCase(),
        }),
      },
    });

    return TenantResponseEntity.from(updatedTenant);
  }

  /**
   * Update tenant profile information only
   * @param id - Tenant ID
   * @param updateData - Profile update data
   * @returns Updated tenant response
   */
  async updateProfile(
    id: number,
    updateData: UpdateTenantProfileDto,
  ): Promise<TenantResponseEntity> {
    await this.findOne(id);

    const updatedTenant = await this.prisma.tenant.update({
      where: { id },
      data: updateData,
    });

    return TenantResponseEntity.from(updatedTenant);
  }

  /**
   * Soft delete a tenant by marking it as inactive
   * Note: This is a logical delete - we don't actually remove the tenant
   * @param id - Tenant ID
   * @returns Success message
   * @throws NotFoundException if tenant not found
   */
  async remove(id: number): Promise<{ message: string }> {
    const tenant = await this.findOne(id);

    // Check if tenant has active users
    const userCount = await this.prisma.systemUser.count({
      where: {
        tenantId: id,
        isActive: true,
      },
    });

    if (userCount > 0) {
      throw new ConflictException(
        `Cannot delete tenant '${tenant.name}' as it has ${userCount} active users`,
      );
    }

    await this.prisma.tenant.delete({
      where: { id },
    });
    // In a real implementation, you might add an 'isActive' field to the Tenant model
    return {
      message: `Tenant '${tenant.name}' delete successfully.`,
    };
  }

  /**
   * Get tenant statistics
   * @param id - Tenant ID
   * @returns Tenant statistics
   */
  async getStatistics(id: number): Promise<{
    id: number;
    name: string;
    subdomain: string;
    totalUsers: number;
    activeUsers: number;
    createdAt: Date;
  }> {
    const tenant = await this.findOne(id);

    const [totalUsers, activeUsers] = await Promise.all([
      this.prisma.systemUser.count({
        where: { tenantId: id },
      }),
      this.prisma.systemUser.count({
        where: {
          tenantId: id,
          isActive: true,
        },
      }),
    ]);

    return {
      id: tenant.id,
      name: tenant.name,
      subdomain: tenant.subdomain,
      totalUsers,
      activeUsers,
      createdAt: tenant.createdAt,
    };
  }

  /**
   * Count total tenants
   * @returns Total tenant count
   */
  async count(): Promise<number> {
    return this.prisma.tenant.count();
  }
}
