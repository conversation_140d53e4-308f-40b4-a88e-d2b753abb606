import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsN<PERSON>ber, Min, Max } from 'class-validator';

/**
 * DTO for tenant query parameters with filtering and pagination
 */
export class TenantQueryDto {
  @ApiProperty({
    description: 'Filter by tenant name (partial match, case-insensitive)',
    required: false,
    example: 'Acme',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Filter by subdomain (partial match, case-insensitive)',
    required: false,
    example: 'acme',
  })
  @IsOptional()
  @IsString()
  subdomain?: string;

  @ApiProperty({
    description: 'Page number for pagination',
    required: false,
    example: 1,
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiProperty({
    description: 'Sort field (name, subdomain, createdAt, updatedAt)',
    required: false,
    example: 'createdAt',
    default: 'createdAt',
    enum: ['name', 'subdomain', 'createdAt', 'updatedAt'],
  })
  @IsOptional()
  @IsString()
  sortBy?: 'name' | 'subdomain' | 'createdAt' | 'updatedAt' = 'createdAt';

  @ApiProperty({
    description: 'Sort order (asc or desc)',
    required: false,
    example: 'desc',
    default: 'desc',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * DTO for tenant search with advanced filtering
 */
export class TenantSearchDto extends TenantQueryDto {
  @ApiProperty({
    description: 'Search term to match against name and subdomain',
    required: false,
    example: 'acme',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

// Export aliases for backward compatibility
export const TenantQueryDto_Alias = TenantQueryDto;
export const TenantSearchDto_Alias = TenantSearchDto;
