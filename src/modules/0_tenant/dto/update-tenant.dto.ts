import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateTenantDto } from './create-tenant.dto';
import { IsString, IsOptional, Length, Matches } from 'class-validator';

/**
 * DTO for updating a Tenant
 * All fields are optional for partial updates
 */
export class UpdateTenantDto extends PartialType(CreateTenantDto) {
  @ApiProperty({
    description: 'Name of the tenant organization',
    example: 'Acme Corporation Updated',
    minLength: 2,
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(2, 100, { message: 'Name must be between 2 and 100 characters' })
  name?: string;

  @ApiProperty({
    description:
      'Unique subdomain for the tenant (lowercase, alphanumeric, hyphens allowed)',
    example: 'acme-corp-updated',
    minLength: 2,
    maxLength: 50,
    pattern: '^[a-z0-9-]+$',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(2, 50, { message: 'Subdomain must be between 2 and 50 characters' })
  @Matches(/^[a-z0-9-]+$/, {
    message:
      'Subdomain must contain only lowercase letters, numbers, and hyphens',
  })
  subdomain?: string;
}

/**
 * DTO for updating only the tenant profile information
 */
export class UpdateTenantProfileDto {
  @ApiProperty({
    description: 'Name of the tenant organization',
    example: 'Acme Corporation',
    minLength: 2,
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(2, 100, { message: 'Name must be between 2 and 100 characters' })
  name?: string;
}

// Export aliases for backward compatibility
export const UpdateTenantDto_Alias = UpdateTenantDto;
export const UpdateTenantProfileDto_Alias = UpdateTenantProfileDto;
