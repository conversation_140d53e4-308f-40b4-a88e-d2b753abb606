import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, Length, Matches } from 'class-validator';

/**
 * DTO for creating a new Tenant
 */
export class CreateTenantDto {
  @ApiProperty({
    description: 'Name of the tenant organization',
    example: 'Acme Corporation',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @Length(2, 100, { message: 'Name must be between 2 and 100 characters' })
  name: string;

  @ApiProperty({
    description:
      'Unique subdomain for the tenant (lowercase, alphanumeric, hyphens allowed)',
    example: 'acme-corp',
    minLength: 2,
    maxLength: 50,
    pattern: '^[a-z0-9-]+$',
  })
  @IsString()
  @IsNotEmpty()
  @Length(2, 50, { message: 'Subdomain must be between 2 and 50 characters' })
  @Matches(/^[a-z0-9-]+$/, {
    message:
      'Subdomain must contain only lowercase letters, numbers, and hyphens',
  })
  subdomain: string;
}

// Export alias for backward compatibility
export const CreateTenantDto_Alias = CreateTenantDto;
