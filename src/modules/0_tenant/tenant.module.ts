import { Module } from '@nestjs/common';
import { TenantController } from './tenant.controller';
import { TenantService } from './tenant.service';
import { DatabaseModule } from 'src/database/database.module';

/**
 * Tenant Module
 *
 * This module handles all tenant-related operations including:
 * - Tenant creation and management
 * - Subdomain validation and uniqueness
 * - Tenant statistics and reporting
 * - Tenant search and filtering
 *
 * Following NestJS modular architecture and SOLID principles:
 * - Single Responsibility: Each class has one clear purpose
 * - Open/Closed: Extensible through interfaces and inheritance
 * - Liskov Substitution: Proper inheritance hierarchies
 * - Interface Segregation: Focused, specific interfaces
 * - Dependency Inversion: Depends on abstractions, not concretions
 */
@Module({
  imports: [
    DatabaseModule, // Import database module for Prisma access
  ],
  controllers: [
    TenantController, // REST API endpoints for tenant operations
  ],
  providers: [
    TenantService, // Business logic for tenant management
  ],
  exports: [
    TenantService, // Export service for use in other modules
  ],
})
export class TenantModule {}
