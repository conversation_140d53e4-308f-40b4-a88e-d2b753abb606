import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import { TenantService } from './tenant.service';
import { CreateTenantDto } from './dto/create-tenant.dto';
import {
  UpdateTenantDto,
  UpdateTenantProfileDto,
} from './dto/update-tenant.dto';
import { TenantQueryDto, TenantSearchDto } from './dto/tenant-query.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiCreatedResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { ResultsPaginated } from 'src/shared/paginations';
import { TenantEntity, TenantResponseEntity } from './entities/tenant.entity';
import { JwtGuard } from '../1_auth/guard';
import { Public } from 'src/shared';

@UseGuards(JwtGuard)
@ApiBearerAuth()
@Controller('tenant')
@ApiTags('0_tenant')
export class TenantController {
  constructor(private readonly tenantService: TenantService) {}

  /**
   * Create a new tenant
   */
  @Post()
  @ApiOperation({
    summary: 'Create a new tenant',
    description: 'Creates a new tenant organization with a unique subdomain',
  })
  @ApiCreatedResponse({
    type: TenantResponseEntity,
    description: 'Tenant created successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Subdomain already exists' })
  @ApiBody({ type: CreateTenantDto })
  create(
    @Body() createTenantDto: CreateTenantDto,
  ): Promise<TenantResponseEntity> {
    return this.tenantService.create(createTenantDto);
  }

  /**
   * Get all tenants with filtering and pagination
   */
  @Get()
  @Public()
  @ApiOperation({
    summary: 'Get all tenants',
    description: 'Retrieve a paginated list of tenants with optional filtering',
  })
  @ApiOkResponse({
    type: ResultsPaginated,
    description: 'List of tenants retrieved successfully',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'Filter by tenant name',
  })
  @ApiQuery({
    name: 'subdomain',
    required: false,
    description: 'Filter by subdomain',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Sort field',
    enum: ['name', 'subdomain', 'createdAt', 'updatedAt'],
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order',
    enum: ['asc', 'desc'],
  })
  findAll(
    @Query() query: TenantQueryDto,
  ): Promise<ResultsPaginated<TenantEntity>> {
    return this.tenantService.findAll(query);
  }

  /**
   * Search tenants with advanced filtering
   */
  @Post('search')
  @ApiOperation({
    summary: 'Search tenants',
    description: 'Search tenants with advanced filtering capabilities',
  })
  @ApiOkResponse({
    type: ResultsPaginated,
    description: 'Search results retrieved successfully',
  })
  @ApiBody({ type: TenantSearchDto })
  search(
    @Body() searchDto: TenantSearchDto,
  ): Promise<ResultsPaginated<TenantEntity>> {
    return this.tenantService.search(searchDto);
  }

  /**
   * Get tenant by ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get tenant by ID',
    description: 'Retrieve a specific tenant by its unique identifier',
  })
  @ApiOkResponse({
    type: TenantEntity,
    description: 'Tenant retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  @ApiParam({ name: 'id', description: 'Tenant UUID' })
  findOne(@Param('id', ParseIntPipe) id: number): Promise<TenantEntity> {
    return this.tenantService.findOne(id);
  }

  /**
   * Get tenant by subdomain
   */
  @Get('subdomain/:subdomain')
  @ApiOperation({
    summary: 'Get tenant by subdomain',
    description: 'Retrieve a tenant by its unique subdomain',
  })
  @ApiOkResponse({
    type: TenantEntity,
    description: 'Tenant retrieved successfully',
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  @ApiParam({ name: 'subdomain', description: 'Tenant subdomain' })
  findBySubdomain(
    @Param('subdomain') subdomain: string,
  ): Promise<TenantEntity | null> {
    return this.tenantService.findBySubdomain(subdomain);
  }

  /**
   * Update tenant
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update tenant',
    description: 'Update tenant information including name and subdomain',
  })
  @ApiOkResponse({
    type: TenantResponseEntity,
    description: 'Tenant updated successfully',
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  @ApiConflictResponse({ description: 'Subdomain already exists' })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiParam({ name: 'id', description: 'Tenant UUID' })
  @ApiBody({ type: UpdateTenantDto })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTenantDto: UpdateTenantDto,
  ): Promise<TenantResponseEntity> {
    return this.tenantService.update(id, updateTenantDto);
  }

  /**
   * Update tenant profile
   */
  @Patch(':id/profile')
  @ApiOperation({
    summary: 'Update tenant profile',
    description: 'Update only the tenant profile information (name)',
  })
  @ApiOkResponse({
    type: TenantResponseEntity,
    description: 'Tenant profile updated successfully',
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiParam({ name: 'id', description: 'Tenant UUID' })
  @ApiBody({ type: UpdateTenantProfileDto })
  updateProfile(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: UpdateTenantProfileDto,
  ): Promise<TenantResponseEntity> {
    return this.tenantService.updateProfile(id, updateData);
  }

  /**
   * Get tenant statistics
   */
  @Get(':id/statistics')
  @ApiOperation({
    summary: 'Get tenant statistics',
    description:
      'Retrieve statistics for a specific tenant including user counts',
  })
  @ApiOkResponse({
    description: 'Tenant statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        subdomain: { type: 'string' },
        totalUsers: { type: 'number' },
        activeUsers: { type: 'number' },
        createdAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  @ApiParam({ name: 'id', description: 'Tenant UUID' })
  getStatistics(@Param('id', ParseIntPipe) id: number) {
    return this.tenantService.getStatistics(id);
  }

  /**
   * Get total tenant count
   */
  @Get('admin/count')
  @ApiOperation({
    summary: 'Get total tenant count',
    description: 'Retrieve the total number of tenants in the system',
  })
  @ApiOkResponse({
    description: 'Tenant count retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        count: { type: 'number' },
      },
    },
  })
  async getCount(): Promise<{ count: number }> {
    const count = await this.tenantService.count();
    return { count };
  }

  /**
   * Delete tenant (soft delete)
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete tenant',
    description:
      'Soft delete a tenant (marks for deletion, requires admin action to complete)',
  })
  @ApiOkResponse({
    description: 'Tenant marked for deletion successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Tenant not found' })
  @ApiConflictResponse({
    description: 'Cannot delete tenant with active users',
  })
  @ApiParam({ name: 'id', description: 'Tenant UUID' })
  remove(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    return this.tenantService.remove(id);
  }

  /**
   * Admin test endpoint for smoke testing
   */
  @Get('admin/test')
  @ApiOperation({
    summary: 'Admin test endpoint',
    description: 'Smoke test endpoint for tenant module health check',
  })
  @ApiOkResponse({
    description: 'Tenant module is working correctly',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        timestamp: { type: 'string' },
        module: { type: 'string' },
      },
    },
  })
  adminTest(): { message: string; timestamp: string; module: string } {
    return {
      message: 'Tenant module is working correctly',
      timestamp: new Date().toISOString(),
      module: '0_tenant',
    };
  }
}
