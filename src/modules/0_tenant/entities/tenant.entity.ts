import { Tenant } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Min, IsOptional, IsString } from 'class-validator';

/**
 * Tenant entity representing a tenant in the system
 * Maps to the Tenant model in Prisma schema
 */
export class TenantEntity implements Tenant {
  @ApiProperty({
    description: 'Unique identifier for the tenant',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Name of the tenant organization',
    example: 'Acme Corporation',
  })
  name: string;

  @ApiProperty({
    description: 'Unique subdomain for the tenant',
    example: 'acme',
  })
  subdomain: string;

  @ApiProperty({
    description: 'Date when the tenant was created',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the tenant was last updated',
  })
  updatedAt: Date;

  /**
   * Creates a TenantEntity from a Tenant object
   */
  static from(obj: Tenant): TenantEntity {
    return {
      id: obj.id,
      name: obj.name,
      subdomain: obj.subdomain,
      createdAt: obj.createdAt,
      updatedAt: obj.updatedAt,
    } as TenantEntity;
  }
}

/**
 * Response entity for Tenant with only safe fields exposed
 */
export class TenantResponseEntity {
  @ApiProperty({
    description: 'Unique identifier for the tenant',
    example: 'uuid-string',
  })
  id: number;

  @ApiProperty({
    description: 'Name of the tenant organization',
    example: 'Acme Corporation',
  })
  name: string;

  @ApiProperty({
    description: 'Unique subdomain for the tenant',
    example: 'acme',
  })
  subdomain: string;

  @ApiProperty({
    description: 'Date when the tenant was created',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the tenant was last updated',
  })
  updatedAt: Date;

  /**
   * Creates a TenantResponseEntity from a Tenant object
   */
  static from(obj: Tenant): TenantResponseEntity {
    return {
      id: obj.id,
      name: obj.name,
      subdomain: obj.subdomain,
      createdAt: obj.createdAt,
      updatedAt: obj.updatedAt,
    };
  }
}

/**
 * Filter results entity for Tenant
 */
export class TenantFilterResults extends TenantEntity {}

/**
 * Query parameters for filtering Tenants
 */
export class TenantQuery {
  @ApiProperty({
    description: 'Name to filter by',
    required: false,
    example: 'Acme',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Subdomain to filter by',
    required: false,
    example: 'acme',
  })
  @IsOptional()
  @IsString()
  subdomain?: string;

  @ApiProperty({
    description: 'Page number for pagination',
    required: false,
    example: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    required: false,
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}

// Export aliases for backward compatibility during migration
export const TenantsEntity = TenantEntity;
export const TenantResponseEntity_Alias = TenantResponseEntity;
export const TenantsQuery = TenantQuery;
export const TenantsFilterResults = TenantFilterResults;
