import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsEmail,
  IsOptional,
  IsBoolean,
  IsUUID,
  IsPositive,
} from 'class-validator';

/**
 * DTO for creating a new SystemUser
 */
export class CreateSystemUserDto {
  @ApiProperty({
    description: 'Tenant ID that the user belongs to',
    example: 'tenant-uuid',
  })
  @IsPositive()
  @IsNotEmpty()
  @IsUUID()
  tenantId: number;

  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  fullName: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Whether the user account should be active',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;

  @ApiProperty({
    description: 'Whether the email should be marked as verified',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean = false;

  @ApiProperty({
    description: 'Whether the user account should be marked as verified',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  verified?: boolean = false;

  @ApiProperty({
    description: 'Whether the user should have administrator privileges',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isAdministrator?: boolean = false;

  @ApiProperty({
    description: 'ID of the user creating this user',
    required: false,
  })
  @IsOptional()
  createbyId?: number;
}

// Export alias for backward compatibility
export const CreateUserDto = CreateSystemUserDto;
