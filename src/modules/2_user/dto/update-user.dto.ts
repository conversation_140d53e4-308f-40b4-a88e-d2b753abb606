import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateSystemUserDto } from './create-user.dto';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsEmail,
  IsBoolean,
} from 'class-validator';

/**
 * DTO for updating a SystemUser
 */
export class UpdateSystemUserDto extends PartialType(CreateSystemUserDto) {}

/**
 * DTO for updating SystemUser profile information
 */
export class UpdateSystemUserProfileDto {
  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  fullName?: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Whether the user account should be active',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

/**
 * DTO for updating SystemUser verification status
 */
export class UpdateSystemUserVerificationDto {
  @ApiProperty({
    description: 'Whether the email should be marked as verified',
    example: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  emailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user account should be marked as verified',
    example: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  verified: boolean;
}

// Export aliases for backward compatibility
export const UpdateUserDto = UpdateSystemUserDto;
export const UpdateUserBioDataDto = UpdateSystemUserProfileDto;
