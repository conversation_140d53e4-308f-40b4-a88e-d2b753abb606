import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateSystemUserDto } from './dto/create-user.dto';
import {
  UpdateSystemUserDto,
  UpdateSystemUserProfileDto,
  UpdateSystemUserVerificationDto,
} from './dto/update-user.dto';
import {
  SystemUserResponseEntity,
  SystemUserEntity,
  SystemUserQuery,
} from './entities/user.entity';
import { Prisma, SystemUser } from '@prisma/client';
import {
  ResultsPaginated,
  pagination,
  getPagingData,
} from 'src/shared/paginations';
import { DatabaseService } from 'src/database/database.service';

/**
 * Service for managing SystemUser operations
 */
@Injectable()
export class UserService {
  constructor(private readonly prisma: DatabaseService) {}

  /**
   * Create a new SystemUser
   * @param createSystemUserDto - Data for creating the user
   * @returns Created SystemUser response
   */
  async create(
    createSystemUserDto: CreateSystemUserDto,
  ): Promise<SystemUserResponseEntity> {
    // Check if email already exists
    if (createSystemUserDto.email) {
      const existingUser = await this.prisma.systemUser.findUnique({
        where: { email: createSystemUserDto.email },
      });

      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    // Check if tenant exists
    const tenant = await this.prisma.tenant.findUnique({
      where: { id: createSystemUserDto.tenantId },
    });

    if (!tenant) {
      throw new NotFoundException('Tenant not found');
    }

    const systemUser = await this.prisma.systemUser.create({
      data: {
        ...createSystemUserDto,
        passwordHash: '', // This should be set by auth service
      },
    });

    return SystemUserResponseEntity.from(systemUser);
  }

  /**
   * Find all SystemUsers with filtering and pagination
   * @param query - Query parameters for filtering and pagination
   * @returns Paginated list of SystemUsers
   */
  async findAll(
    query: Partial<SystemUserQuery>,
  ): Promise<ResultsPaginated<SystemUserEntity>> {
    const { email, fullName, isActive, emailVerified, verified, tenantId } =
      query;
    const page = query.page || 1;
    const size = query.limit || 100;

    const filters: Prisma.SystemUserWhereInput = {};

    if (email) {
      Object.assign(filters, {
        email: { contains: email, mode: 'insensitive' },
      });
    }

    if (fullName) {
      Object.assign(filters, {
        fullName: { contains: fullName, mode: 'insensitive' },
      });
    }

    if (isActive !== undefined) {
      Object.assign(filters, { isActive });
    }

    if (emailVerified !== undefined) {
      Object.assign(filters, { emailVerified });
    }

    if (verified !== undefined) {
      Object.assign(filters, { verified });
    }

    if (tenantId) {
      Object.assign(filters, { tenantId });
    }

    const { take, skip } = pagination(page, size);

    const [usersList, count] = await Promise.all([
      this.prisma.systemUser.findMany({
        where: filters,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.systemUser.count({ where: filters }),
    ]);

    const response = getPagingData(
      {
        count,
        rows: usersList.map((item) => SystemUserEntity.from(item)),
      },
      page,
      size,
    );

    return response;
  }

  /**
   * Find a SystemUser by ID
   * @param id - SystemUser ID
   * @returns SystemUser entity
   */
  async findOne(id: number): Promise<SystemUser> {
    const user = await this.prisma.systemUser.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException(`SystemUser with ID ${id} does not exist`);
    }

    return user;
  }

  /**
   * Update SystemUser profile information
   * @param id - SystemUser ID
   * @param updateData - Profile data to update
   * @returns Updated SystemUser response
   */
  async updateProfile(
    id: number,
    updateData: UpdateSystemUserProfileDto,
  ): Promise<SystemUserResponseEntity> {
    await this.findOne(id);

    // Check if email already exists (if updating email)
    if (updateData.email) {
      const existingUser = await this.prisma.systemUser.findFirst({
        where: {
          email: updateData.email,
          id: { not: id },
        },
      });

      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    const updatedUser = await this.prisma.systemUser.update({
      where: { id },
      data: updateData,
    });

    return SystemUserResponseEntity.from(updatedUser);
  }

  /**
   * Update SystemUser verification status
   * @param id - SystemUser ID
   * @param updateData - Verification data to update
   * @returns Updated SystemUser response
   */
  async updateVerification(
    id: number,
    updateData: UpdateSystemUserVerificationDto,
  ): Promise<SystemUserResponseEntity> {
    await this.findOne(id);

    const updatedUser = await this.prisma.systemUser.update({
      where: { id },
      data: updateData,
    });

    return SystemUserResponseEntity.from(updatedUser);
  }

  /**
   * Update SystemUser (general update)
   * @param id - SystemUser ID
   * @param updateData - Data to update
   * @returns Updated SystemUser response
   */
  async update(
    id: number,
    updateData: UpdateSystemUserDto,
  ): Promise<SystemUserResponseEntity> {
    await this.findOne(id);

    const updatedUser = await this.prisma.systemUser.update({
      where: { id },
      data: updateData,
    });

    return SystemUserResponseEntity.from(updatedUser);
  }

  /**
   * Soft delete a SystemUser by deactivating the account
   * @param id - SystemUser ID
   * @returns Updated SystemUser response
   */
  async remove(id: number): Promise<SystemUserResponseEntity> {
    await this.findOne(id);

    const deletedUser = await this.prisma.systemUser.update({
      where: { id },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    return SystemUserResponseEntity.from(deletedUser);
  }

  /**
   * Get SystemUser with sensitive fields masked (for auth compatibility)
   * @param id - SystemUser ID
   * @returns SystemUserEntity with sensitive fields hidden
   */
  async getUserWithoutSecrets(id: number): Promise<SystemUserEntity> {
    const user = await this.findOne(id);
    return SystemUserEntity.from(user);
  }

  /**
   * Find SystemUser by email
   * @param email - Email address
   * @returns SystemUser entity or null
   */
  async findByEmail(email: string): Promise<SystemUser | null> {
    return this.prisma.systemUser.findUnique({
      where: { email },
    });
  }

  /**
   * Find SystemUsers by tenant ID
   * @param tenantId - Tenant ID
   * @returns Array of SystemUsers
   */
  async findByTenantId(tenantId: number): Promise<SystemUser[]> {
    return this.prisma.systemUser.findMany({
      where: { tenantId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Count total SystemUsers
   * @returns Total count
   */
  async count(): Promise<number> {
    return this.prisma.systemUser.count();
  }

  /**
   * Count active SystemUsers
   * @returns Active users count
   */
  async countActive(): Promise<number> {
    return this.prisma.systemUser.count({
      where: { isActive: true },
    });
  }

  /**
   * Change SystemUser email address
   * @param userId - SystemUser ID
   * @param email - New email address
   * @returns Success message
   */
  async changeUserEmail(
    userId: number,
    email: string,
  ): Promise<{ message: string }> {
    // Ensure user exists
    await this.findOne(userId);

    // Validate new email does not exist
    const existingUser = await this.findByEmail(email);
    if (existingUser && existingUser.id !== userId) {
      throw new ConflictException('Email already exists');
    }

    await this.prisma.systemUser.update({
      where: { id: userId },
      data: { email },
    });

    return { message: 'Email updated successfully' };
  }

  /**
   * Change SystemUser password
   * @param userId - SystemUser ID
   * @param passwordHash - New password hash
   * @returns Success message
   */
  async changeUserPassword(
    userId: number,
    passwordHash: string,
  ): Promise<{ message: string }> {
    await this.findOne(userId);

    await this.prisma.systemUser.update({
      where: { id: userId },
      data: {
        passwordHash,
        resetToken: null,
        resetTokenExpires: null,
        lastPasswordResetDate: new Date(),
      },
    });

    return { message: 'Password updated successfully' };
  }
}
