import { <PERSON>User, UserLifeCycle } from '@prisma/client';
import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsOptional } from 'class-validator';

/**
 * SystemUser entity representing a user in the system
 * Maps to the SystemUser model in Prisma schema
 */
export class SystemUserEntity implements SystemUser {
  @ApiProperty({
    description: 'Whether the user is an administrator',
    example: false,
  })
  isAdministrator: boolean;

  @ApiProperty({
    description: 'Unique identifier for the system user',
    example: 'uuid-string',
  })
  id: number;

  accountState: UserLifeCycle;

  @ApiProperty({
    description: 'Whether the user has accepted the terms and conditions',
    example: true,
  })
  acceptTerms: boolean;

  @ApiProperty({
    description: 'Tenant ID that the user belongs to',
    example: 'tenant-uuid',
  })
  tenantId: number;

  @ApiHideProperty()
  passwordHash: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
  })
  fullName: string;

  @ApiProperty({
    description: 'Whether the user account is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    nullable: true,
  })
  email: string | null;

  @ApiProperty({
    description: 'Whether the email has been verified',
    example: false,
  })
  emailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user account has been verified',
    example: false,
  })
  verified: boolean;

  @ApiProperty({
    description: 'Date when password was last reset',
    nullable: true,
  })
  passwordReset: Date | null;

  @ApiHideProperty()
  resetToken: string | null;

  @ApiHideProperty()
  resetTokenExpires: Date | null;

  @ApiHideProperty()
  verificationToken: string | null;

  @ApiProperty({
    description: 'Date when password was last reset',
    nullable: true,
  })
  lastPasswordResetDate: Date | null;

  @ApiHideProperty()
  refreshHashedToken: string | null;

  @ApiProperty({
    description: 'ID of the user who created this user',
    nullable: true,
  })
  createbyId: number | null;

  @ApiProperty({
    description: 'Date when user last logged in',
    nullable: true,
  })
  lastLoginDate: Date | null;

  @ApiProperty({
    description: 'Date when the user was created',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the user was last updated',
  })
  updatedAt: Date;

  /**
   * Creates a SystemUserEntity from a SystemUser object with sensitive fields masked
   */
  static from(obj: SystemUser, showHash: boolean = false): SystemUserEntity {
    return {
      ...obj,
      passwordHash: showHash ? obj.passwordHash : '[HIDDEN]',
      resetToken: null,
      refreshHashedToken: null,
      verificationToken: null,
    } as SystemUserEntity;
  }
}

/**
 * Response entity for SystemUser with only safe fields exposed
 */
export class SystemUserResponseEntity {
  @ApiProperty({
    description: 'Unique identifier for the system user',
    example: 'uuid-string',
  })
  id: number;

  @ApiProperty({
    description: 'Tenant ID that the user belongs to',
    example: 'tenant-uuid',
  })
  tenantId: number;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  fullName: string;

  @ApiProperty({
    description: 'Whether the user account is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
    nullable: true,
  })
  email: string | null;

  @ApiProperty({
    description: 'Whether the email has been verified',
    example: false,
  })
  emailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user account has been verified',
    example: false,
  })
  verified: boolean;

  @ApiProperty({
    description: 'Date when user last logged in',
    nullable: true,
  })
  lastLoginDate: Date | null;

  @ApiProperty({
    description: 'Date when the user was created',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the user was last updated',
  })
  updatedAt: Date;

  /**
   * Creates a SystemUserResponseEntity from a SystemUser object
   */
  static from(obj: SystemUser): SystemUserResponseEntity {
    return {
      id: obj.id,
      tenantId: obj.tenantId,
      fullName: obj.fullName,
      isActive: obj.isActive,
      email: obj.email,
      emailVerified: obj.emailVerified,
      verified: obj.verified,
      lastLoginDate: obj.lastLoginDate,
      createdAt: obj.createdAt,
      updatedAt: obj.updatedAt,
    };
  }
}

/**
 * Current user entity with JWT subject
 */
export class CurrentSystemUser extends SystemUserEntity {
  @ApiProperty({
    description: 'JWT subject identifier',
  })
  sub: string;
}

/**
 * Filter results entity for SystemUser
 */
export class SystemUserFilterResults extends SystemUserEntity {}

/**
 * Query parameters for filtering SystemUsers
 */
export class SystemUserQuery {
  @ApiProperty({
    description: 'Email address to filter by',
    required: false,
    example: '<EMAIL>',
  })
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'Full name to filter by',
    required: false,
    example: 'John Doe',
  })
  @IsOptional()
  fullName?: string;

  @ApiProperty({
    description: 'Whether user is active',
    required: false,
    example: true,
  })
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'Whether email is verified',
    required: false,
    example: false,
  })
  @IsOptional()
  emailVerified?: boolean;

  @ApiProperty({
    description: 'Whether user is verified',
    required: false,
    example: false,
  })
  @IsOptional()
  verified?: boolean;

  @ApiProperty({
    description: 'Tenant ID to filter by',
    required: false,
    example: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  tenantId?: number;

  @ApiProperty({
    description: 'Page number for pagination',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    default: 100,
    minimum: 1,
    maximum: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  @Type(() => Number)
  limit?: number = 100;
}

// Export aliases for backward compatibility during migration
export const UsersEntity = SystemUserEntity;
export const UserResponseEntity = SystemUserResponseEntity;
export const UsersQuery = SystemUserQuery;
export const UsersFilterResults = SystemUserFilterResults;
