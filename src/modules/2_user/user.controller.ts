import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { UserService } from './user.service';
import { CreateSystemUserDto } from './dto/create-user.dto';
import {
  UpdateSystemUserDto,
  UpdateSystemUserProfileDto,
  UpdateSystemUserVerificationDto,
} from './dto/update-user.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiCreatedResponse,
} from '@nestjs/swagger';
import { ResultsPaginated } from 'src/shared/paginations';
import {
  SystemUserEntity,
  SystemUserResponseEntity,
  SystemUserQuery,
} from './entities/user.entity';
import { GetCurrentUser } from '../1_auth/decorator';
import { JwtGuard } from '../1_auth/guard';

@UseGuards(JwtGuard)
@ApiBearerAuth()
@Controller('user')
@ApiTags('2_user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new system user' })
  @ApiCreatedResponse({ type: SystemUserResponseEntity })
  @ApiBody({ type: CreateSystemUserDto })
  create(
    @Body() createSystemUserDto: CreateSystemUserDto,
  ): Promise<SystemUserResponseEntity> {
    return this.userService.create(createSystemUserDto);
  }

  @Post('search')
  @ApiOperation({ summary: 'Search system users' })
  @ApiOkResponse({ type: ResultsPaginated })
  @ApiBody({ type: SystemUserQuery })
  findAll(
    @Body() query: Partial<SystemUserQuery>,
  ): Promise<ResultsPaginated<SystemUserEntity>> {
    return this.userService.findAll(query);
  }

  @ApiOkResponse({
    type: SystemUserResponseEntity,
  })
  @ApiOperation({
    summary: 'Get current user details',
  })
  @Get('me')
  getMe(@GetCurrentUser() user: SystemUserEntity): SystemUserResponseEntity {
    return SystemUserResponseEntity.from(user);
  }

  @Get(':id')
  @ApiOkResponse({
    type: SystemUserResponseEntity,
  })
  @ApiOperation({
    summary: 'Get system user by ID',
  })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SystemUserResponseEntity> {
    const user = await this.userService.findOne(id);
    return SystemUserResponseEntity.from(user);
  }

  @Patch(':id/profile')
  @ApiOkResponse({
    type: SystemUserResponseEntity,
  })
  @ApiOperation({
    summary: 'Update system user profile',
  })
  @ApiBody({ type: UpdateSystemUserProfileDto })
  updateProfile(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: UpdateSystemUserProfileDto,
  ): Promise<SystemUserResponseEntity> {
    return this.userService.updateProfile(id, updateData);
  }

  @Patch(':id')
  @ApiOkResponse({
    type: SystemUserResponseEntity,
  })
  @ApiOperation({
    summary: 'Update system user',
  })
  @ApiBody({ type: UpdateSystemUserDto })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: UpdateSystemUserDto,
  ): Promise<SystemUserResponseEntity> {
    return this.userService.update(id, updateData);
  }

  @Patch(':id/verification')
  @ApiOkResponse({
    type: SystemUserResponseEntity,
  })
  @ApiOperation({
    summary: 'Update system user verification status',
  })
  @ApiBody({ type: UpdateSystemUserVerificationDto })
  updateVerification(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: UpdateSystemUserVerificationDto,
  ): Promise<SystemUserResponseEntity> {
    return this.userService.updateVerification(id, updateData);
  }

  @Delete(':id')
  @ApiOkResponse({
    type: SystemUserResponseEntity,
  })
  @ApiOperation({
    summary: 'Deactivate a system user',
  })
  remove(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SystemUserResponseEntity> {
    return this.userService.remove(id);
  }

  @Get('count')
  @ApiOperation({
    summary: 'Get total system users count',
  })
  @ApiOkResponse({
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        active: { type: 'number' },
      },
    },
  })
  async getCount() {
    const [total, active] = await Promise.all([
      this.userService.count(),
      this.userService.countActive(),
    ]);
    return { total, active };
  }

  @Get('tenant/:tenantId')
  @ApiOperation({
    summary: 'Get system users by tenant ID',
  })
  @ApiOkResponse({
    type: [SystemUserResponseEntity],
  })
  async findByTenantId(
    @Param('tenantId', ParseIntPipe) tenantId: number,
  ): Promise<SystemUserResponseEntity[]> {
    const users = await this.userService.findByTenantId(tenantId);
    return users.map((user) => SystemUserResponseEntity.from(user));
  }

  @Patch(':id/email')
  @ApiOperation({
    summary: 'Change system user email address',
  })
  @ApiOkResponse({
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', format: 'email' },
      },
      required: ['email'],
    },
  })
  changeEmail(
    @Param('id', ParseIntPipe) id: number,
    @Body('email') email: string,
  ): Promise<{ message: string }> {
    return this.userService.changeUserEmail(id, email);
  }

  @Patch(':id/password')
  @ApiOperation({
    summary: 'Change system user password',
  })
  @ApiOkResponse({
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        passwordHash: { type: 'string' },
      },
      required: ['passwordHash'],
    },
  })
  changePassword(
    @Param('id', ParseIntPipe) id: number,
    @Body('passwordHash') passwordHash: string,
  ): Promise<{ message: string }> {
    return this.userService.changeUserPassword(id, passwordHash);
  }
}
