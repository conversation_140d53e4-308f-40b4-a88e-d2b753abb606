/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/auth/AuthLayout': RouteRecordInfo<'/auth/AuthLayout', '/auth/AuthLayout', Record<never, never>, Record<never, never>>,
    '/auth/ForgotPassword': RouteRecordInfo<'/auth/ForgotPassword', '/auth/ForgotPassword', Record<never, never>, Record<never, never>>,
    '/auth/Login': RouteRecordInfo<'/auth/Login', '/auth/Login', Record<never, never>, Record<never, never>>,
    '/auth/Register': RouteRecordInfo<'/auth/Register', '/auth/Register', Record<never, never>, Record<never, never>>,
    '/auth/ResetPassword': RouteRecordInfo<'/auth/ResetPassword', '/auth/ResetPassword', Record<never, never>, Record<never, never>>,
    '/calculator/CalculatorPage': RouteRecordInfo<'/calculator/CalculatorPage', '/calculator/CalculatorPage', Record<never, never>, Record<never, never>>,
    '/calculator/components/CalculationEditor': RouteRecordInfo<'/calculator/components/CalculationEditor', '/calculator/components/CalculationEditor', Record<never, never>, Record<never, never>>,
    '/calculator/components/CommissionCalculator': RouteRecordInfo<'/calculator/components/CommissionCalculator', '/calculator/components/CommissionCalculator', Record<never, never>, Record<never, never>>,
    '/calculator/components/CommissionResults': RouteRecordInfo<'/calculator/components/CommissionResults', '/calculator/components/CommissionResults', Record<never, never>, Record<never, never>>,
    '/calculator/components/ConditionEditor': RouteRecordInfo<'/calculator/components/ConditionEditor', '/calculator/components/ConditionEditor', Record<never, never>, Record<never, never>>,
    '/calculator/components/ConfigLoader': RouteRecordInfo<'/calculator/components/ConfigLoader', '/calculator/components/ConfigLoader', Record<never, never>, Record<never, never>>,
    '/calculator/components/Header': RouteRecordInfo<'/calculator/components/Header', '/calculator/components/Header', Record<never, never>, Record<never, never>>,
    '/calculator/components/RuleEditor': RouteRecordInfo<'/calculator/components/RuleEditor', '/calculator/components/RuleEditor', Record<never, never>, Record<never, never>>,
    '/calculator/components/RulesManagement': RouteRecordInfo<'/calculator/components/RulesManagement', '/calculator/components/RulesManagement', Record<never, never>, Record<never, never>>,
    '/calculator/components/SalesDataForm': RouteRecordInfo<'/calculator/components/SalesDataForm', '/calculator/components/SalesDataForm', Record<never, never>, Record<never, never>>,
    '/calculator/components/TabNavigation': RouteRecordInfo<'/calculator/components/TabNavigation', '/calculator/components/TabNavigation', Record<never, never>, Record<never, never>>,
    '/calculator/components/TierEditor': RouteRecordInfo<'/calculator/components/TierEditor', '/calculator/components/TierEditor', Record<never, never>, Record<never, never>>,
    '/clients/ClientPage': RouteRecordInfo<'/clients/ClientPage', '/clients/ClientPage', Record<never, never>, Record<never, never>>,
    '/clients/components/ClientFilters': RouteRecordInfo<'/clients/components/ClientFilters', '/clients/components/ClientFilters', Record<never, never>, Record<never, never>>,
    '/clients/components/ClientsTable': RouteRecordInfo<'/clients/components/ClientsTable', '/clients/components/ClientsTable', Record<never, never>, Record<never, never>>,
    '/clients/components/CreateClientDialog': RouteRecordInfo<'/clients/components/CreateClientDialog', '/clients/components/CreateClientDialog', Record<never, never>, Record<never, never>>,
    '/commissions/CommissionsPage': RouteRecordInfo<'/commissions/CommissionsPage', '/commissions/CommissionsPage', Record<never, never>, Record<never, never>>,
    '/dashboard/DashboardPage': RouteRecordInfo<'/dashboard/DashboardPage', '/dashboard/DashboardPage', Record<never, never>, Record<never, never>>,
    '/profile/ProfilePage': RouteRecordInfo<'/profile/ProfilePage', '/profile/ProfilePage', Record<never, never>, Record<never, never>>,
    '/reports/ReportsPage': RouteRecordInfo<'/reports/ReportsPage', '/reports/ReportsPage', Record<never, never>, Record<never, never>>,
    '/tenants/TenantsPage': RouteRecordInfo<'/tenants/TenantsPage', '/tenants/TenantsPage', Record<never, never>, Record<never, never>>,
    '/users/UserDetailPage': RouteRecordInfo<'/users/UserDetailPage', '/users/UserDetailPage', Record<never, never>, Record<never, never>>,
    '/users/UsersPage': RouteRecordInfo<'/users/UsersPage', '/users/UsersPage', Record<never, never>, Record<never, never>>,
  }
}
