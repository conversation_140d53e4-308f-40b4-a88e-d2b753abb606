import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { UserLifeCycle, type User, type UsersQuery } from "@/types/user";
import type { CreateUserDto, UpdateUserBioDataDto } from "@/types/user.dto";
import { usersApi } from "@/services/users.api";
import { authApi } from "@/services/auth.api";
import { useAppStore } from "./app";
import { storeToRefs } from "pinia";

interface StateTransition {
  to: UserLifeCycle[];
  confirmationMessage: string;
  title: string;
}

export const STATE_TRANSITIONS: Record<UserLifeCycle, StateTransition> = {
  [UserLifeCycle.PARTIAL_APPLICATION]: {
    to: [UserLifeCycle.PENDING_APPROVAL, UserLifeCycle.REJECTED],
    title: "Change Application Status",
    confirmationMessage:
      "Are you sure you want to change the status of this partial application?",
  },
  [UserLifeCycle.PENDING_APPROVAL]: {
    to: [UserLifeCycle.ACTIVE, UserLifeCycle.REJECTED],
    title: "Update Approval Status",
    confirmationMessage: "Are you sure you want to update the approval status?",
  },
  [UserLifeCycle.ACTIVE]: {
    to: [UserLifeCycle.INACTIVE, UserLifeCycle.BLACKLISTED],
    title: "Change User Status",
    confirmationMessage:
      "Are you sure you want to change this active user's status?",
  },
  [UserLifeCycle.INACTIVE]: {
    to: [UserLifeCycle.ACTIVE, UserLifeCycle.EXITED],
    title: "Update User Status",
    confirmationMessage:
      "Are you sure you want to update this inactive user's status?",
  },
  [UserLifeCycle.REJECTED]: {
    to: [UserLifeCycle.PENDING_APPROVAL],
    title: "Reconsider Application",
    confirmationMessage:
      "Are you sure you want to reconsider this rejected application?",
  },
  [UserLifeCycle.EXITED]: {
    to: [UserLifeCycle.ACTIVE],
    title: "Reactivate User",
    confirmationMessage:
      "Are you sure you want to reactivate this exited user?",
  },
  [UserLifeCycle.BLACKLISTED]: {
    to: [UserLifeCycle.PENDING_APPROVAL, UserLifeCycle.ACTIVE],
    title: "Blacklisted User",
    confirmationMessage: "Blacklisted users cannot transition to other states.",
  },
};

export interface StateChangeConfirmation {
  show: boolean;
  title: string;
  message: string;
  userId: number | null;
  currentState: UserLifeCycle | null;
  newState: UserLifeCycle | null;
  loading: boolean;
}

export const useUsersStore = defineStore("users", () => {
  const appStore = useAppStore();
  const { loading } = storeToRefs(appStore);

  // state
  const users = ref<User[]>([]);
  const totalItems = ref(0);
  const currentPage = ref(1);
  const totalPages = ref(0);
  const hasNextPage = ref(false);
  const hasPreviousPage = ref(false);
  const nextPage = ref<number | null>(null);
  const filters = ref<Partial<UsersQuery>>({
    page: 1,
    limit: 10,
  });

  // State change confirmation dialog state
  const stateChangeConfirmation = ref<StateChangeConfirmation>({
    show: false,
    title: "",
    message: "",
    userId: null,
    currentState: null,
    newState: null,
    loading: false,
  });

  // getters
  const isLoading = computed(() => loading.value);
  const activeUsers = computed(() =>
    users.value.filter((user) => user.accountState === UserLifeCycle.ACTIVE),
  );

  // Get available state transitions for a user
  const getAvailableTransitions = (
    currentState: UserLifeCycle,
  ): UserLifeCycle[] => {
    return STATE_TRANSITIONS[currentState]?.to || [];
  };

  // Show confirmation dialog
  const showStateChangeConfirmation = (
    userId: number,
    newState: UserLifeCycle,
    currentState: UserLifeCycle,
  ) => {
    const transition = STATE_TRANSITIONS[currentState];
    if (!transition || !transition.to.includes(newState)) return;

    stateChangeConfirmation.value = {
      show: true,
      title: transition.title,
      message: transition.confirmationMessage,
      userId,
      currentState,
      newState,
      loading: false,
    };
  };

  // Cancel confirmation
  const cancelStateChange = () => {
    stateChangeConfirmation.value = {
      show: false,
      title: "",
      message: "",
      userId: null,
      currentState: null,
      newState: null,
      loading: false,
    };
  };

  // Confirm state change
  const confirmStateChange = async () => {
    const { userId, newState } = stateChangeConfirmation.value;
    if (!userId || !newState) return;

    try {
      stateChangeConfirmation.value.loading = true;
      await updateUserState(userId, newState);
      cancelStateChange();
    } catch (error) {
      console.error("Failed to update user state:", error);
    } finally {
      stateChangeConfirmation.value.loading = false;
    }
  };

  // actions
  async function fetchUsers(query?: Partial<UsersQuery>) {
    try {
      appStore.setLoading(true);
      const queryParams = { ...filters.value, ...query };
      filters.value = queryParams;

      const response = await usersApi.searchUsers(queryParams);
      users.value = response.results;
      totalItems.value = response.totalItems;
      currentPage.value = response.currentPage;
      totalPages.value = response.totalPages;
      hasNextPage.value = response.hasNextPage;
      hasPreviousPage.value = response.hasPreviousPage;
      nextPage.value = response.nextPage;
    } finally {
      appStore.setLoading(false);
    }
  }

  async function updateUserState(userId: number, accountState: UserLifeCycle) {
    try {
      appStore.setLoading(true);
      const updatedUser = await usersApi.updateUserState(userId, {
        accountState,
      });
      const index = users.value.findIndex((u) => u.id === userId);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }
      return updatedUser;
    } finally {
      appStore.setLoading(false);
    }
  }

  async function updateUser(userId: number, data: UpdateUserBioDataDto) {
    try {
      appStore.setLoading(true);
      const updatedUser = await usersApi.updateUser(userId, data);
      const index = users.value.findIndex((u) => u.id === userId);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }
      return updatedUser;
    } finally {
      appStore.setLoading(false);
    }
  }

  async function updateUserFull(userId: number, data: any) {
    try {
      appStore.setLoading(true);
      const updatedUser = await usersApi.updateUserFull(userId, data);
      const index = users.value.findIndex((u) => u.id === userId);
      if (index !== -1) {
        users.value[index] = updatedUser;
      }
      return updatedUser;
    } finally {
      appStore.setLoading(false);
    }
  }

  async function createUser(data: CreateUserDto) {
    try {
      appStore.setLoading(true);
      const updatedUser = await authApi.signup(data as any);
      // const index = users.value.findIndex(u => u.id === userId)
      // if (index !== -1) {
      //     users.value[index] = updatedUser
      // }
      return updatedUser;
    } finally {
      appStore.setLoading(false);
    }
  }

  async function deleteUser(userId: number) {
    try {
      appStore.setLoading(true);
      await usersApi.deleteUser(userId);
      users.value = users.value.filter((u) => u.id !== userId);
    } finally {
      appStore.setLoading(false);
    }
  }

  function setFilters(newFilters: Partial<UsersQuery>) {
    filters.value = { ...filters.value, ...newFilters, page: 1 };
    fetchUsers();
  }

  return {
    // state
    users,
    totalItems,
    currentPage,
    totalPages,
    filters,
    hasNextPage,
    hasPreviousPage,
    nextPage,
    stateChangeConfirmation,

    // getters
    isLoading,
    activeUsers,

    // actions
    fetchUsers,
    updateUserState,
    updateUser,
    updateUserFull,
    deleteUser,
    setFilters,
    createUser,
    getAvailableTransitions,
    showStateChangeConfirmation,
    cancelStateChange,
    confirmStateChange,
  };
});
