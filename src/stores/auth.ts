import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { authApi } from "@/services/auth.api";
import { usersApi } from "@/services/users.api";
import type { User } from "@/types/user";
import type { UpdateUserBioDataDto } from "@/types/user.dto";
import { useAppStore } from "./app";
import { useRouter } from "vue-router";

export const useAuthStore = defineStore(
  "auth",
  () => {
    const appStore = useAppStore();
    const user = ref<User | null>(null);
    const accessToken = ref<string | null>(null);
    const refreshToken = ref<string | null>(null);
    const router = useRouter();

    const isAuthenticated = computed(() => !!accessToken.value);

    function setTokens(access: string, refresh: string) {
      accessToken.value = access;
      refreshToken.value = refresh;
      localStorage.setItem("access_token", access);
      localStorage.setItem("refresh_token", refresh);
    }

    function setUser(userData: User) {
      user.value = userData;
    }

    // Initialize from localStorage on store creation
    function initializeFromStorage() {
      const storedAccessToken = localStorage.getItem("access_token");
      const storedRefreshToken = localStorage.getItem("refresh_token");
      const storedUser = localStorage.getItem("user");

      if (storedAccessToken && storedRefreshToken) {
        accessToken.value = storedAccessToken;
        refreshToken.value = storedRefreshToken;
      }

      if (storedUser) {
        user.value = JSON.parse(storedUser);
      }
    }

    async function login(email: string, password: string) {
      try {
        appStore.setLoading(true);
        const response = await authApi.login({ email, password });

        setTokens(response.access_token, response.refresh_token || "");
        setUser(response.user);

        // Store user in localStorage
        localStorage.setItem("user", JSON.stringify(response.user));

        router.push("/dashboard");
        return response;
      } finally {
        appStore.setLoading(false);
      }
    }

    async function logout() {
      try {
        appStore.setLoading(true);
        if (user.value) {
          await authApi.logout(user.value.id.toString());
        }
        // Clear both store and localStorage
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
        localStorage.removeItem("user");
        user.value = null;
        accessToken.value = null;
        refreshToken.value = null;
        router.push("/auth/login");
      } finally {
        appStore.setLoading(false);
      }
    }

    async function forgotPassword(email: string) {
      try {
        appStore.setLoading(true);
        return await authApi.forgotPassword(email);
      } finally {
        appStore.setLoading(false);
      }
    }

    async function resetPassword(
      token: string,
      password: string,
      confirmPassword: string,
    ) {
      try {
        appStore.setLoading(true);
        return await authApi.resetPassword(token, password, confirmPassword);
      } finally {
        appStore.setLoading(false);
      }
    }

    async function changePassword(userId: string, password: string) {
      try {
        appStore.setLoading(true);
        return await authApi.changePassword(userId, password);
      } finally {
        appStore.setLoading(false);
      }
    }

    async function changeEmail(userId: number, email: string) {
      try {
        appStore.setLoading(true);
        const response = await authApi.changeEmail(userId.toString(), email);

        // Update the user in the store if it's the current user
        if (user.value && user.value.id === userId) {
          user.value.email = email;
          localStorage.setItem("user", JSON.stringify(user.value));
        }

        return response;
      } finally {
        appStore.setLoading(false);
      }
    }

    async function updateProfile(data: UpdateUserBioDataDto) {
      if (!user.value) throw new Error("No user logged in");

      try {
        appStore.setLoading(true);
        const updatedUser = await usersApi.updateProfile(user.value.id, data);

        // Update the user in the store
        user.value = updatedUser;
        localStorage.setItem("user", JSON.stringify(updatedUser));

        return updatedUser;
      } finally {
        appStore.setLoading(false);
      }
    }

    // Initialize store from localStorage
    initializeFromStorage();

    return {
      user,
      isAuthenticated,
      login,
      logout,
      forgotPassword,
      resetPassword,
      changePassword,
      changeEmail,
      updateProfile,
    };
  },
  {
    persist: {
      key: "auth-store",
      storage: localStorage,
    },
  },
);
