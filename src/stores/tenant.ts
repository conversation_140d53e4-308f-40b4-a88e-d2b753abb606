import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { tenantApi } from '@/services/tenant.api'
import type {
    Tenant,
    CreateTenantDto,
    UpdateTenantDto,
    UpdateTenantProfileDto,
    TenantQuery,
    TenantSearchDto,
    TenantStatistics
} from '@/types/tenant'
import { useAppStore } from './app'

export const useTenantStore = defineStore('tenant', () => {
    const appStore = useAppStore()
    
    // State
    const tenants = ref<Tenant[]>([])
    const currentTenant = ref<Tenant | null>(null)
    const totalItems = ref(0)
    const currentPage = ref(1)
    const isLoading = ref(false)
    const error = ref<string | null>(null)
    
    // Default filters
    const filters = ref<TenantQuery>({
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
    })

    // Getters
    const totalPages = computed(() => Math.ceil(totalItems.value / (filters.value.limit || 10)))
    const hasNextPage = computed(() => currentPage.value < totalPages.value)
    const hasPreviousPage = computed(() => currentPage.value > 1)
    const filteredTenants = computed(() => tenants.value)

    // Actions
    async function fetchTenants(query?: TenantQuery) {
        try {
            isLoading.value = true
            error.value = null
            
            const queryParams = { ...filters.value, ...query }
            const response = await tenantApi.getTenants(queryParams)
            
            tenants.value = response.results
            totalItems.value = response.totalItems
            currentPage.value = response.currentPage
            
            // Update filters with the query used
            filters.value = { ...filters.value, ...query }
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to fetch tenants'
            console.error('Error fetching tenants:', err)
        } finally {
            isLoading.value = false
        }
    }

    async function fetchTenantById(id: string) {
        try {
            isLoading.value = true
            error.value = null
            
            const tenant = await tenantApi.getTenantById(id)
            currentTenant.value = tenant
            return tenant
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to fetch tenant'
            console.error('Error fetching tenant:', err)
            throw err
        } finally {
            isLoading.value = false
        }
    }

    async function fetchTenantBySubdomain(subdomain: string) {
        try {
            isLoading.value = true
            error.value = null
            
            const tenant = await tenantApi.getTenantBySubdomain(subdomain)
            if (tenant) {
                currentTenant.value = tenant
            }
            return tenant
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to fetch tenant'
            console.error('Error fetching tenant by subdomain:', err)
            throw err
        } finally {
            isLoading.value = false
        }
    }

    async function createTenant(data: CreateTenantDto) {
        try {
            appStore.setLoading(true)
            error.value = null
            
            const newTenant = await tenantApi.createTenant(data)
            
            // Add to the beginning of the list
            tenants.value.unshift(newTenant)
            totalItems.value += 1
            
            return newTenant
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to create tenant'
            console.error('Error creating tenant:', err)
            throw err
        } finally {
            appStore.setLoading(false)
        }
    }

    async function updateTenant(id: string, data: UpdateTenantDto) {
        try {
            appStore.setLoading(true)
            error.value = null
            
            const updatedTenant = await tenantApi.updateTenant(id, data)
            
            // Update in the list
            const index = tenants.value.findIndex(t => t.id === id)
            if (index !== -1) {
                tenants.value[index] = updatedTenant
            }
            
            // Update current tenant if it's the same
            if (currentTenant.value?.id === id) {
                currentTenant.value = updatedTenant
            }
            
            return updatedTenant
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to update tenant'
            console.error('Error updating tenant:', err)
            throw err
        } finally {
            appStore.setLoading(false)
        }
    }

    async function updateTenantProfile(id: string, data: UpdateTenantProfileDto) {
        try {
            appStore.setLoading(true)
            error.value = null
            
            const updatedTenant = await tenantApi.updateTenantProfile(id, data)
            
            // Update in the list
            const index = tenants.value.findIndex(t => t.id === id)
            if (index !== -1) {
                tenants.value[index] = updatedTenant
            }
            
            // Update current tenant if it's the same
            if (currentTenant.value?.id === id) {
                currentTenant.value = updatedTenant
            }
            
            return updatedTenant
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to update tenant profile'
            console.error('Error updating tenant profile:', err)
            throw err
        } finally {
            appStore.setLoading(false)
        }
    }

    async function searchTenants(searchDto: TenantSearchDto) {
        try {
            isLoading.value = true
            error.value = null
            
            const response = await tenantApi.searchTenants(searchDto)
            
            tenants.value = response.results
            totalItems.value = response.totalItems
            currentPage.value = response.currentPage
            
            return response
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to search tenants'
            console.error('Error searching tenants:', err)
            throw err
        } finally {
            isLoading.value = false
        }
    }

    async function deleteTenant(id: string) {
        try {
            appStore.setLoading(true)
            error.value = null
            
            const result = await tenantApi.deleteTenant(id)
            
            // Remove from the list
            tenants.value = tenants.value.filter(t => t.id !== id)
            totalItems.value -= 1
            
            // Clear current tenant if it's the same
            if (currentTenant.value?.id === id) {
                currentTenant.value = null
            }
            
            return result
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to delete tenant'
            console.error('Error deleting tenant:', err)
            throw err
        } finally {
            appStore.setLoading(false)
        }
    }

    async function getTenantStatistics(id: string): Promise<TenantStatistics> {
        try {
            isLoading.value = true
            error.value = null
            
            return await tenantApi.getTenantStatistics(id)
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to get tenant statistics'
            console.error('Error getting tenant statistics:', err)
            throw err
        } finally {
            isLoading.value = false
        }
    }

    async function getTenantCount(): Promise<number> {
        try {
            const result = await tenantApi.getTenantCount()
            return result.count
        } catch (err: any) {
            error.value = err.response?.data?.message || 'Failed to get tenant count'
            console.error('Error getting tenant count:', err)
            throw err
        }
    }

    function clearError() {
        error.value = null
    }

    function setCurrentTenant(tenant: Tenant | null) {
        currentTenant.value = tenant
    }

    function resetFilters() {
        filters.value = {
            page: 1,
            limit: 10,
            sortBy: 'createdAt',
            sortOrder: 'desc'
        }
        currentPage.value = 1
    }

    return {
        // State
        tenants,
        currentTenant,
        totalItems,
        currentPage,
        isLoading,
        error,
        filters,
        
        // Getters
        totalPages,
        hasNextPage,
        hasPreviousPage,
        filteredTenants,
        
        // Actions
        fetchTenants,
        fetchTenantById,
        fetchTenantBySubdomain,
        createTenant,
        updateTenant,
        updateTenantProfile,
        searchTenants,
        deleteTenant,
        getTenantStatistics,
        getTenantCount,
        clearError,
        setCurrentTenant,
        resetFilters
    }
}, {
    persist: {
        key: 'tenant-store',
        storage: localStorage
    }
})
