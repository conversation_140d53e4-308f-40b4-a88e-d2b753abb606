import { defineStore, storeToRefs } from 'pinia'
import { ref, computed } from 'vue'
import type { ResultsPaginated } from '@/types'
import type { ClientEntity, ClientQueryDto, CreateClientDto } from '@/types/client.types'
import { clientApi } from '@/services/client.api'
import { useAppStore } from './app'

export const useClientStore = defineStore('client', () => {
    const appStore = useAppStore()
    const { loading } = storeToRefs(appStore)

    // State
    const clients = ref<ClientEntity[]>([])
    const currentClient = ref<ClientEntity | null>(null)
    const isLoading = ref(false)
    const totalItems = ref(0)
    const currentPage = ref(1)
    const totalPages = ref(0)
    const hasNextPage = ref(false)
    const hasPreviousPage = ref(false)
    const nextPage = ref<number | null>(null)
    const filters = ref<Partial<ClientQueryDto>>({
        page: 1,
        limit: 10
    })

    // Getters
    const getClientById = computed(() => {
        return (id: number) => clients.value.find(client => client.id === id)
    })

    // Actions
    const fetchClients = async () => {
        try {
             appStore.setLoading(true)
            const response = await clientApi.getClients(filters.value)
            clients.value = response.results
            totalItems.value = response.totalItems
            currentPage.value = response.currentPage
            totalPages.value = response.totalPages
            hasNextPage.value = response.hasNextPage
            hasPreviousPage.value = response.hasPreviousPage
            nextPage.value = response.nextPage
        
        } finally {
            appStore.setLoading(false)
        }
    }

    const fetchClientById = async (id: number) => {
        try {
            isLoading.value = true
            const response = await clientApi.getClientById(id)
            currentClient.value = response
            const index = clients.value.findIndex(c => c.id === id)
            if (index !== -1) {
                clients.value[index] = response
            }
            return response
        } finally {
            appStore.setLoading(false)
        }
    }

    const createClient = async (clientData: CreateClientDto) => {
        try {
            appStore.setLoading(true)
            const response = await clientApi.createClient(clientData)
            clients.value.push(response)
            return response
        } finally {
            appStore.setLoading(false)
        }
    }

    const updateClient = async (id: number, clientData: Partial<CreateClientDto>) => {
        try {
            appStore.setLoading(true)
            const response = await clientApi.updateClient(id, clientData)
            const index = clients.value.findIndex(client => client.id === id)
            if (index !== -1) {
                clients.value[index] = response
            }
            return response
        } finally {
            appStore.setLoading(false)
        }
    }

    const setFilters = (newFilters: Partial<ClientQueryDto>) => {
        filters.value = {
            ...filters.value,
            ...newFilters
        }
        fetchClients()
    }

    const resetStore = () => {
        clients.value = []
        currentClient.value = null
        isLoading.value = false
        totalItems.value = 0
        currentPage.value = 1
        filters.value = {
            page: 1,
            limit: 10
        }
    }

    return {
        // State
        clients,
        currentClient,
        isLoading,
        totalItems,
        currentPage,
        filters,
        hasNextPage,
        hasPreviousPage,
        nextPage,

        // Getters
        getClientById,

        // Actions
        fetchClients,
        fetchClientById,
        createClient,
        updateClient,
        setFilters,
        resetStore
    }
}) 