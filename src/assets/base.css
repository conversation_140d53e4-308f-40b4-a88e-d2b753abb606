/* Modern Commission Calculator Color Palette */
:root {
  /* Base Colors */
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8fafc;
  --vt-c-white-mute: #f1f5f9;

  --vt-c-black: #0f172a;
  --vt-c-black-soft: #1e293b;
  --vt-c-black-mute: #334155;

  /* Primary Brand Colors */
  --vt-c-primary: #1976d2;
  --vt-c-primary-light: #42a5f5;
  --vt-c-primary-dark: #1565c0;

  /* Commission Theme Colors */
  --vt-c-accent: #ff6b35;
  --vt-c-accent-light: #ff8a65;
  --vt-c-accent-dark: #e64a19;

  /* Semantic Colors */
  --vt-c-success: #4caf50;
  --vt-c-warning: #ff9800;
  --vt-c-error: #f44336;
  --vt-c-info: #2196f3;

  /* Neutral Grays */
  --vt-c-gray-50: #f8fafc;
  --vt-c-gray-100: #f1f5f9;
  --vt-c-gray-200: #e2e8f0;
  --vt-c-gray-300: #cbd5e1;
  --vt-c-gray-400: #94a3b8;
  --vt-c-gray-500: #64748b;
  --vt-c-gray-600: #475569;
  --vt-c-gray-700: #334155;
  --vt-c-gray-800: #1e293b;
  --vt-c-gray-900: #0f172a;

  /* Dividers */
  --vt-c-divider-light-1: rgba(148, 163, 184, 0.3);
  --vt-c-divider-light-2: rgba(148, 163, 184, 0.15);
  --vt-c-divider-dark-1: rgba(148, 163, 184, 0.4);
  --vt-c-divider-dark-2: rgba(148, 163, 184, 0.2);

  /* Text Colors */
  --vt-c-text-light-1: var(--vt-c-gray-900);
  --vt-c-text-light-2: var(--vt-c-gray-600);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: var(--vt-c-gray-300);
}

/* Semantic color variables for Smart Commissions */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-gray-50);
  --color-background-mute: var(--vt-c-gray-100);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);
  --color-text-muted: var(--vt-c-text-light-2);

  --color-primary: var(--vt-c-primary);
  --color-accent: var(--vt-c-accent);
  --color-success: var(--vt-c-success);
  --color-warning: var(--vt-c-warning);
  --color-error: var(--vt-c-error);

  --section-gap: 120px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-1);
    --color-text-muted: var(--vt-c-text-dark-2);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: #FFFFFF;
  transition:
    color 0.3s ease,
    background-color 0.3s ease;
  line-height: 1.6;
  font-family:
    'Inter',
    'Roboto',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 16px;
  font-weight: 400;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern utility classes */
.card-modern {
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--color-border) !important;
  transition: all 0.2s ease !important;
}

.card-modern:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-2px) !important;
}

.btn-modern {
  border-radius: var(--border-radius) !important;
  font-weight: 500 !important;
  letter-spacing: 0.025em !important;
  transition: all 0.2s ease !important;
}

.text-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}
