export enum PrismaErrorCode {
  // Common Database Errors (P1xxx)
  AuthenticationFailed = 'P1000',
  CantReachDatabase = 'P1001',
  DatabaseTimeout = 'P1002',
  DatabaseNotExist = 'P1003',
  OperationsTimeout = 'P1008',
  DatabaseAlreadyExists = 'P1009',
  AccessDenied = 'P1010',
  TlsConnectionError = 'P1011',
  SchemaValidationError = 'P1012',

  // Query Engine Errors (P2xxx)
  RecordTooLong = 'P2000',
  RecordNotFound = 'P2001',
  UniqueConstraintViolation = 'P2002',
  ForeignKeyConstraintViolation = 'P2003',
  ConstraintViolation = 'P2004',
  InvalidFieldValue = 'P2005',
  InvalidModelField = 'P2006',
  ValidationError = 'P2007',
  QueryParsingError = 'P2008',
  QueryValidationError = 'P2009',
  RawQueryError = 'P2010',
  NullConstraintViolation = 'P2011',
  MissingRequiredValue = 'P2012',
  MissingRequiredArgument = 'P2013',
  RelationViolation = 'P2014',
  RelatedRecordNotFound = 'P2015',
  QueryInterpretationError = 'P2016',
  RecordsNotConnected = 'P2017',
  ConnectedRecordsNotFound = 'P2018',
  InputError = 'P2019',
  ValueOutOfRange = 'P2020',
  TableNotFound = 'P2021',
  ColumnNotFound = 'P2022',
  InconsistentColumnData = 'P2023',
  ConnectionPoolTimeout = 'P2024',
  RecordRequiredButNotFound = 'P2025',
  UnsupportedFeature = 'P2026',
  MultipleErrors = 'P2027',
  TransactionApiError = 'P2028',
  FullTextSearchFailed = 'P2030',
  MongoDbReplicaSetRequired = 'P2031',
  NumberOutOfRange = 'P2033',
  TransactionConflict = 'P2034',

  // Introspection Engine Errors (P4xxx)
  IntrospectionError = 'P4000',
  EmptyDatabase = 'P4001',
  InconsistentSchema = 'P4002',

  // Prisma Pulse Errors (P61xx)
  PulseServerError = 'P6100',
  PulseDatasourceError = 'P6101',
  PulseUnauthorized = 'P6102',
  PulseProjectDisabled = 'P6103',
  PulseAccountHold = 'P6104',
  PulseVersionNotSupported = 'P6105',
}
