import { PrismaClient } from '@prisma/client';
import * as argon2 from 'argon2';

/**
 * User Seed Script
 *
 * This script creates initial system user data for the application.
 * It's designed to be idempotent - safe to run multiple times.
 *
 * Usage:
 * - Development: npm run seed:users
 * - Production: NODE_ENV=production npm run seed:users
 *
 * Features:
 * - Creates sample users with hashed passwords using argon2
 * - Attaches users to the first available tenant
 * - Checks for existing users to avoid duplicates
 * - Proper error handling and logging
 * - Environment-aware seeding
 */

const prisma = new PrismaClient();

/**
 * Sample user data for seeding
 */
const sampleUsers = [
  {
    email: '<EMAIL>',
    fullName: 'B. Sunday',
    password: 'TempPassword123!', // This will be hashed
    isAdministrator: true,
  },
  {
    email: '<EMAIL>',
    fullName: 'A. Galgalo',
    password: 'Chuugalgee@dev', // This will be hashed
    isAdministrator: true,
  },
];

/**
 * Hash password using argon2
 */
async function hashPassword(password: string): Promise<string> {
  try {
    return await argon2.hash(password);
  } catch (error) {
    console.error('Failed to hash password:', error);
    throw error;
  }
}

/**
 * Get the first available tenant
 */
async function getFirstTenant() {
  const tenant = await prisma.tenant.findFirst({
    orderBy: { createdAt: 'asc' },
  });

  if (!tenant) {
    throw new Error('No tenants found. Please run tenant seeding first.');
  }

  return tenant;
}

/**
 * Check if a user exists by email
 */
async function userExists(email: string): Promise<boolean> {
  const user = await prisma.systemUser.findUnique({
    where: { email },
  });
  return !!user;
}

/**
 * Create a single user if it doesn't exist
 */
async function createUserIfNotExists(
  userData: (typeof sampleUsers)[0],
  tenantId: number,
): Promise<void> {
  try {
    const exists = await userExists(userData.email);

    if (exists) {
      console.log(
        `✓ User '${userData.fullName}' (${userData.email}) already exists - skipping`,
      );
      return;
    }

    // Hash the password
    const passwordHash = await hashPassword(userData.password);

    const user = await prisma.systemUser.create({
      data: {
        email: userData.email,
        fullName: userData.fullName,
        passwordHash,
        isAdministrator: userData.isAdministrator,
        tenantId,
        emailVerified: true, // Set to true for seed users
        verified: true, // Set to true for seed users
        accountState: 'ACTIVE',
        isActive: true,
        acceptTerms: true,
      },
    });

    console.log(
      `✓ Created user '${user.fullName}' (${user.email}) - Admin: ${user.isAdministrator}`,
    );
  } catch (error) {
    console.error(`✗ Failed to create user '${userData.fullName}':`, error);
    throw error;
  }
}

/**
 * Main seeding function
 */
async function seedUsers(): Promise<void> {
  console.log('🌱 Starting user seeding...\n');

  try {
    // Check database connection
    await prisma.$connect();
    console.log('✓ Database connection established');

    // Get the first tenant
    const tenant = await getFirstTenant();
    console.log(`✓ Using tenant: ${tenant.name} (ID: ${tenant.id})`);

    // Get current user count
    const existingCount = await prisma.systemUser.count();
    console.log(`📊 Current user count: ${existingCount}\n`);

    // Create users
    console.log('Creating users...');
    for (const userData of sampleUsers) {
      await createUserIfNotExists(userData, tenant.id);
    }

    // Get final count
    const finalCount = await prisma.systemUser.count();
    const created = finalCount - existingCount;

    console.log(`\n🎉 User seeding completed!`);
    console.log(`📊 Final user count: ${finalCount}`);
    console.log(`➕ New users created: ${created}`);

    // List all users
    console.log('\n📋 All users:');
    const allUsers = await prisma.systemUser.findMany({
      include: {
        tenant: {
          select: {
            name: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    allUsers.forEach((user, index) => {
      const adminStatus = user.isAdministrator ? '(Admin)' : '';
      console.log(
        `   ${index + 1}. ${user.fullName} - ${user.email} ${adminStatus} [${user.tenant.name}]`,
      );
    });
  } catch (error) {
    console.error('\n❌ User seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('\n✓ Database connection closed');
  }
}

/**
 * Clean up function for development/testing
 */
async function cleanUsers(): Promise<void> {
  console.log('🧹 Cleaning up users...\n');

  try {
    await prisma.$connect();

    // Only allow cleanup in development
    if (process.env.NODE_ENV === 'production') {
      console.log('❌ Cleanup not allowed in production environment');
      return;
    }

    const count = await prisma.systemUser.count();
    console.log(`📊 Current user count: ${count}`);

    if (count === 0) {
      console.log('✓ No users to clean up');
      return;
    }

    // Delete all users
    await prisma.systemUser.deleteMany({});

    console.log(`✓ Deleted ${count} users`);
    console.log('🎉 Cleanup completed!');
  } catch (error) {
    console.error('\n❌ Cleanup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  const command = process.argv[2];

  switch (command) {
    case 'clean':
      await cleanUsers();
      break;
    case 'seed':
    default:
      await seedUsers();
      break;
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
}

export { seedUsers, cleanUsers };
