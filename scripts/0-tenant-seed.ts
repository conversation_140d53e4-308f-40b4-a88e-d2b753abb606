import { PrismaClient } from '@prisma/client';

/**
 * Tenant Seed Script
 *
 * This script creates initial tenant data for the application.
 * It's designed to be idempotent - safe to run multiple times.
 *
 * Usage:
 * - Development: npm run seed:tenants
 * - Production: NODE_ENV=production npm run seed:tenants
 *
 * Features:
 * - Creates sample tenants with realistic data
 * - Checks for existing tenants to avoid duplicates
 * - Proper error handling and logging
 * - Environment-aware seeding
 */

const prisma = new PrismaClient();

/**
 * Sample tenant data for seeding
 */
const sampleTenants = [
  {
    name: 'platinumkenya',
    subdomain: 'https://platinumkenya.sandbox.mambu.com',
    description: 'Leading technology solutions provider',
  },
  {
    name: 'premierkenya',
    subdomain: 'https://premierkenya.sandbox.mambu.com',
    description: 'Leading technology solutions provider',
  },
];

/**
 * Check if a tenant exists by subdomain
 */
async function tenantExists(subdomain: string): Promise<boolean> {
  const tenant = await prisma.tenant.findUnique({
    where: { subdomain },
  });
  return !!tenant;
}

/**
 * Create a single tenant if it doesn't exist
 */
async function createTenantIfNotExists(
  tenantData: (typeof sampleTenants)[0],
): Promise<void> {
  try {
    const exists = await tenantExists(tenantData.subdomain);

    if (exists) {
      console.log(
        `✓ Tenant '${tenantData.name}' (${tenantData.subdomain}) already exists - skipping`,
      );
      return;
    }

    const tenant = await prisma.tenant.create({
      data: {
        name: tenantData.name,
        subdomain: tenantData.subdomain,
      },
    });

    console.log(
      `✓ Created tenant '${tenant.name}' with subdomain '${tenant.subdomain}'`,
    );
  } catch (error) {
    console.error(`✗ Failed to create tenant '${tenantData.name}':`, error);
    throw error;
  }
}

/**
 * Main seeding function
 */
async function seedTenants(): Promise<void> {
  console.log('🌱 Starting tenant seeding...\n');

  try {
    // Check database connection
    await prisma.$connect();
    console.log('✓ Database connection established');

    // Get current tenant count
    const existingCount = await prisma.tenant.count();
    console.log(`📊 Current tenant count: ${existingCount}\n`);

    // Create tenants
    console.log('Creating tenants...');
    for (const tenantData of sampleTenants) {
      await createTenantIfNotExists(tenantData);
    }

    // Get final count
    const finalCount = await prisma.tenant.count();
    const created = finalCount - existingCount;

    console.log(`\n🎉 Tenant seeding completed!`);
    console.log(`📊 Final tenant count: ${finalCount}`);
    console.log(`➕ New tenants created: ${created}`);

    // List all tenants
    console.log('\n📋 All tenants:');
    const allTenants = await prisma.tenant.findMany({
      orderBy: { createdAt: 'asc' },
    });

    allTenants.forEach((tenant, index) => {
      console.log(`   ${index + 1}. ${tenant.name} (${tenant.subdomain})`);
    });
  } catch (error) {
    console.error('\n❌ Tenant seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('\n✓ Database connection closed');
  }
}

/**
 * Clean up function for development/testing
 */
async function cleanTenants(): Promise<void> {
  console.log('🧹 Cleaning up tenants...\n');

  try {
    await prisma.$connect();

    // Only allow cleanup in development
    if (process.env.NODE_ENV === 'production') {
      console.log('❌ Cleanup not allowed in production environment');
      return;
    }

    const count = await prisma.tenant.count();
    console.log(`📊 Current tenant count: ${count}`);

    if (count === 0) {
      console.log('✓ No tenants to clean up');
      return;
    }

    // Delete all tenants (cascade will handle related records)
    await prisma.tenant.deleteMany({});

    console.log(`✓ Deleted ${count} tenants`);
    console.log('🎉 Cleanup completed!');
  } catch (error) {
    console.error('\n❌ Cleanup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  const command = process.argv[2];

  switch (command) {
    case 'clean':
      await cleanTenants();
      break;
    case 'seed':
    default:
      await seedTenants();
      break;
  }
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
}

export { seedTenants, cleanTenants };
