{
  "compilerOptions": {
    "strict":false,
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["DOM", "ESNext"],
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "types": [
      "vite/client",
      "vite-plugin-vue-layouts-next/client",
      "unplugin-vue-router/client",
      "@vue/runtime-core"
    ],
    "allowJs": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    "noImplicitAny": false
  },
  "include": [
    "src/**/*",
    "src/**/*.vue"
  ],
  "exclude": ["dist", "node_modules", "cypress"],
  "references": [{ "path": "./tsconfig.node.json" }],
}
