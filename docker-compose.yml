version: '3.8'
services:
  dev-db:
    image: postgres:14
    ports:
      - 5434:5432
    environment:
      POSTGRES_USER: sundaypriest
      POSTGRES_PASSWORD: 'belter'
      POSTGRES_DB: db_smart_commission
    volumes:
      - postgres:/var/lib/postgresql/data
    networks:
      - smart_commission_network
  test-db:
    image: postgres:13
    ports:
      - 5435:5432
    environment:
      POSTGRES_USER: sundaypriest
      POSTGRES_PASSWORD: 'belter'
      POSTGRES_DB: db_smart_commission 
    networks:
      - smart_commission_network    
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: 12345678@!
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - smart_commission_network
networks:
  smart_commission_network:
    driver: bridge
volumes:
  postgres:
  rabbitmq_data: