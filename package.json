{"name": "smart-commission", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"scripts/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\" \"scripts/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test,scripts}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test,scripts}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:e2e:tenant": "jest --config ./test/jest-e2e.json --testPathPattern=tenant.e2e-spec.ts", "test:db:setup": "ts-node -r tsconfig-paths/register test/setup-test-db.ts setup", "test:db:cleanup": "ts-node -r tsconfig-paths/register test/setup-test-db.ts cleanup", "seed:tenants": "ts-node -r tsconfig-paths/register scripts/0-tenant-seed.ts seed", "seed:tenants:clean": "ts-node -r tsconfig-paths/register scripts/0-tenant-seed.ts clean", "seed:users": "ts-node -r tsconfig-paths/register scripts/1-user-seed.ts seed", "seed:users:clean": "ts-node -r tsconfig-paths/register scripts/1-user-seed.ts clean", "prepare": "husky", "pre-commit": "lint-staged", "validate": "npm run lint:check && npm run format:check && npm run build && npm test"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.12.0", "@sendgrid/mail": "^8.1.5", "argon2": "^0.43.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "joi": "^17.13.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.12.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"], "*.prisma": ["npx prisma format"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}