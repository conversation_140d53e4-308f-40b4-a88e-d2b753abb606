{"name": "smart-commissions-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && cross-env VITE_APP_STAGE=prod vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "lint:check": "eslint .", "format": "prettier --write src/", "format:check": "prettier --check src/", "test": "echo \"No tests specified yet\" && exit 0", "server": "json-server --watch database/db.json --port 3001", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "prepare": "husky"}, "dependencies": {"@mdi/font": "^7.4.47", "@tailwindcss/vite": "^4.1.11", "@vuepic/vue-datepicker": "^11.0.1", "@vueuse/core": "^13.6.0", "axios": "^1.7.9", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "lodash": "^4.17.21", "lucide-vue-next": "^0.539.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.2.0", "reka-ui": "^2.4.1", "roboto-fontface": "*", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.0", "vuetify": "^3.7.7", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.10.7", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "husky": "^9.1.7", "jiti": "^2.4.2", "json-server": "^0.17.4", "npm-run-all2": "^7.0.2", "postcss": "^8.5.1", "prettier": "^3.4.2", "sass": "^1.83.4", "tailwindcss": "^4.1.11", "typescript": "~5.7.3", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^28.0.0", "unplugin-vue-router": "^0.10.0", "vite": "^6.0.11", "vite-plugin-vue-devtools": "^7.7.0", "vite-plugin-vue-layouts-next": "^1.0.0", "vite-plugin-vuetify": "^2.0.3", "vue-tsc": "^2.2.0"}}