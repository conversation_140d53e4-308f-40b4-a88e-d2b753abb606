--------------------------------------------------------------------------------
3) Vue 3 Guidelines
--------------------------------------------------------------------------------
- Use the Composition API for new features and prefer single-file components (.vue).
- Keep components small and focused. Break down large views into smaller subcomponents.
- Use Vue Router for clear route definitions (e.g., dynamic routing).
- Manage global state with Pinia depending on project needs.
- Securely handle environment variables (e.g., VITE_ variables if using Vite).
- Test critical components with Jest or Vue Test Utils.
- Use Axios for API requests.
- Use Vuetify 3 for UI components.
- Use Vuetify 3 CSS for css utilities.
