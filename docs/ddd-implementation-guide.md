# DDD Implementation Guide for Smart Commission

This guide provides practical steps to implement Domain-Driven Design patterns in the Smart Commission project.

## Current Project Analysis

### Existing Structure
```
src/modules/1_auth/
├── controllers/          # Presentation Layer
├── dto/                 # Presentation Layer
├── services/            # Mixed (needs refactoring)
├── repositories/        # Infrastructure Layer
├── interfaces/          # Domain Layer
├── types/              # Domain Layer
└── providers/          # Infrastructure Layer
```

### Target DDD Structure
```
src/modules/1_auth/
├── domain/                    # Domain Layer
│   ├── entities/
│   │   ├── user.entity.ts
│   │   └── auth-session.entity.ts
│   ├── value-objects/
│   │   ├── email.vo.ts
│   │   ├── password.vo.ts
│   │   └── user-id.vo.ts
│   ├── services/
│   │   ├── password-policy.service.ts
│   │   └── auth-validation.service.ts
│   ├── events/
│   │   ├── user-created.event.ts
│   │   └── password-reset-requested.event.ts
│   └── interfaces/
│       ├── user.repository.interface.ts
│       └── auth.repository.interface.ts
├── application/               # Application Layer
│   ├── commands/
│   │   ├── register-user.command.ts
│   │   └── reset-password.command.ts
│   ├── queries/
│   │   ├── get-user.query.ts
│   │   └── verify-token.query.ts
│   ├── handlers/
│   │   ├── register-user.handler.ts
│   │   └── reset-password.handler.ts
│   └── services/
│       └── auth-application.service.ts
├── infrastructure/            # Infrastructure Layer
│   ├── repositories/
│   │   ├── prisma-user.repository.ts
│   │   └── prisma-auth.repository.ts
│   ├── services/
│   │   ├── bcrypt-password.service.ts
│   │   ├── jwt-token.service.ts
│   │   └── sendgrid-email.service.ts
│   └── adapters/
│       └── auth-provider.adapter.ts
└── presentation/              # Presentation Layer
    ├── controllers/
    │   └── auth.controller.ts
    ├── dto/
    │   ├── register-user.dto.ts
    │   └── login.dto.ts
    └── guards/
        └── jwt-auth.guard.ts
```

## Step-by-Step Migration Plan

### Phase 1: Create Domain Layer

#### 1.1 Create Value Objects

**Email Value Object**
```typescript
// src/modules/1_auth/domain/value-objects/email.vo.ts
export class Email {
  private readonly value: string;

  constructor(email: string) {
    if (!this.isValid(email)) {
      throw new InvalidValueObjectException('Invalid email format');
    }
    this.value = email.toLowerCase().trim();
  }

  private isValid(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  getValue(): string {
    return this.value;
  }

  equals(email: Email): boolean {
    return this.value === email.getValue();
  }

  toString(): string {
    return this.value;
  }
}
```

**User ID Value Object**
```typescript
// src/modules/1_auth/domain/value-objects/user-id.vo.ts
import { v4 as uuidv4 } from 'uuid';

export class UserId {
  private readonly value: string;

  constructor(id: string) {
    if (!this.isValid(id)) {
      throw new InvalidValueObjectException('Invalid user ID format');
    }
    this.value = id;
  }

  static generate(): UserId {
    return new UserId(uuidv4());
  }

  private isValid(id: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  getValue(): string {
    return this.value;
  }

  equals(userId: UserId): boolean {
    return this.value === userId.getValue();
  }

  toString(): string {
    return this.value;
  }
}
```

**Password Value Object**
```typescript
// src/modules/1_auth/domain/value-objects/password.vo.ts
export class Password {
  private readonly value: string;

  constructor(password: string, isHashed: boolean = false) {
    if (!isHashed && !this.isValid(password)) {
      throw new InvalidValueObjectException('Password does not meet requirements');
    }
    this.value = password;
  }

  private isValid(password: string): boolean {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  }

  getValue(): string {
    return this.value;
  }

  // For hashed passwords from database
  static fromHash(hashedPassword: string): Password {
    return new Password(hashedPassword, true);
  }
}
```

#### 1.2 Create Domain Entities

**User Entity**
```typescript
// src/modules/1_auth/domain/entities/user.entity.ts
export class User {
  private constructor(
    private readonly id: UserId,
    private email: Email,
    private passwordHash: Password,
    private readonly createdAt: Date,
    private updatedAt: Date,
    private isActive: boolean = true,
    private isEmailVerified: boolean = false,
    private verificationToken?: string,
  ) {}

  static create(
    email: Email,
    password: Password,
    verificationToken?: string,
  ): User {
    return new User(
      UserId.generate(),
      email,
      password,
      new Date(),
      new Date(),
      true,
      false,
      verificationToken,
    );
  }

  static fromPersistence(data: UserPersistenceData): User {
    return new User(
      new UserId(data.id),
      new Email(data.email),
      Password.fromHash(data.passwordHash),
      data.createdAt,
      data.updatedAt,
      data.isActive,
      data.isEmailVerified,
      data.verificationToken,
    );
  }

  // Business methods
  changeEmail(newEmail: Email): void {
    if (this.email.equals(newEmail)) {
      return;
    }
    
    this.email = newEmail;
    this.isEmailVerified = false; // Require re-verification
    this.updatedAt = new Date();
  }

  verifyEmail(): void {
    if (this.isEmailVerified) {
      throw new DomainException('Email is already verified');
    }
    
    this.isEmailVerified = true;
    this.verificationToken = undefined;
    this.updatedAt = new Date();
  }

  changePassword(newPassword: Password): void {
    this.passwordHash = newPassword;
    this.updatedAt = new Date();
  }

  deactivate(): void {
    if (!this.isActive) {
      throw new DomainException('User is already inactive');
    }
    
    this.isActive = false;
    this.updatedAt = new Date();
  }

  // Getters
  getId(): UserId {
    return this.id;
  }

  getEmail(): Email {
    return this.email;
  }

  getPasswordHash(): Password {
    return this.passwordHash;
  }

  getIsActive(): boolean {
    return this.isActive;
  }

  getIsEmailVerified(): boolean {
    return this.isEmailVerified;
  }

  getVerificationToken(): string | undefined {
    return this.verificationToken;
  }

  getCreatedAt(): Date {
    return this.createdAt;
  }

  getUpdatedAt(): Date {
    return this.updatedAt;
  }
}
```

#### 1.3 Create Domain Events

**User Created Event**
```typescript
// src/modules/1_auth/domain/events/user-created.event.ts
export class UserCreatedEvent {
  constructor(
    public readonly userId: UserId,
    public readonly email: Email,
    public readonly verificationToken?: string,
    public readonly occurredAt: Date = new Date(),
  ) {}
}
```

**Password Reset Requested Event**
```typescript
// src/modules/1_auth/domain/events/password-reset-requested.event.ts
export class PasswordResetRequestedEvent {
  constructor(
    public readonly userId: UserId,
    public readonly email: Email,
    public readonly resetToken: string,
    public readonly occurredAt: Date = new Date(),
  ) {}
}
```

#### 1.4 Create Repository Interfaces

**User Repository Interface**
```typescript
// src/modules/1_auth/domain/interfaces/user.repository.interface.ts
export interface IUserRepository {
  findById(id: UserId): Promise<User | null>;
  findByEmail(email: Email): Promise<User | null>;
  findByVerificationToken(token: string): Promise<User | null>;
  save(user: User): Promise<void>;
  delete(id: UserId): Promise<void>;
}
```

### Phase 2: Create Application Layer

#### 2.1 Create Commands

**Register User Command**
```typescript
// src/modules/1_auth/application/commands/register-user.command.ts
export class RegisterUserCommand {
  constructor(
    public readonly email: string,
    public readonly password: string,
    public readonly origin: string,
  ) {}
}
```

#### 2.2 Create Command Handlers

**Register User Handler**
```typescript
// src/modules/1_auth/application/handlers/register-user.handler.ts
@CommandHandler(RegisterUserCommand)
export class RegisterUserCommandHandler implements ICommandHandler<RegisterUserCommand> {
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly passwordService: IPasswordService,
    private readonly tokenService: ITokenService,
    private readonly eventPublisher: IEventPublisher,
  ) {}

  async execute(command: RegisterUserCommand): Promise<UserRegisteredResponse> {
    const { email, password, origin } = command;

    // Create value objects
    const emailVO = new Email(email);
    const passwordVO = new Password(password);

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(emailVO);
    if (existingUser) {
      throw new DomainException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await this.passwordService.hashPassword(passwordVO);
    
    // Generate verification token
    const verificationToken = this.tokenService.generateVerificationToken();

    // Create user entity
    const user = User.create(emailVO, hashedPassword, verificationToken);
    
    // Save to repository
    await this.userRepository.save(user);

    // Publish domain event
    this.eventPublisher.publish(
      new UserCreatedEvent(user.getId(), emailVO, verificationToken),
    );

    return {
      id: user.getId().getValue(),
      email: user.getEmail().getValue(),
      message: 'User registered successfully. Please check your email for verification.',
    };
  }
}
```

### Phase 3: Refactor Infrastructure Layer

#### 3.1 Update Repository Implementation

**Prisma User Repository**
```typescript
// src/modules/1_auth/infrastructure/repositories/prisma-user.repository.ts
@Injectable()
export class PrismaUserRepository implements IUserRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findById(id: UserId): Promise<User | null> {
    const userData = await this.prisma.systemUser.findUnique({
      where: { id: id.getValue() },
    });

    return userData ? User.fromPersistence(userData) : null;
  }

  async findByEmail(email: Email): Promise<User | null> {
    const userData = await this.prisma.systemUser.findUnique({
      where: { email: email.getValue() },
    });

    return userData ? User.fromPersistence(userData) : null;
  }

  async save(user: User): Promise<void> {
    const data = this.toPersistence(user);
    
    await this.prisma.systemUser.upsert({
      where: { id: data.id },
      update: data,
      create: data,
    });
  }

  private toPersistence(user: User): any {
    return {
      id: user.getId().getValue(),
      email: user.getEmail().getValue(),
      passwordHash: user.getPasswordHash().getValue(),
      isActive: user.getIsActive(),
      isEmailVerified: user.getIsEmailVerified(),
      verificationToken: user.getVerificationToken(),
      createdAt: user.getCreatedAt(),
      updatedAt: user.getUpdatedAt(),
    };
  }
}
```

### Phase 4: Update Module Configuration

**Auth Module with DDD Structure**
```typescript
// src/modules/1_auth/auth.module.ts
@Module({
  imports: [
    CqrsModule,
    JwtModule.registerAsync({
      useFactory: (config: ConfigService) => ({
        secret: config.get('JWT_SECRET'),
        signOptions: { expiresIn: '15m' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    // Command Handlers
    RegisterUserCommandHandler,
    LoginUserCommandHandler,
    ResetPasswordCommandHandler,
    
    // Query Handlers
    GetUserQueryHandler,
    VerifyTokenQueryHandler,
    
    // Domain Services
    PasswordPolicyService,
    AuthValidationService,
    
    // Infrastructure Services
    {
      provide: IUserRepository,
      useClass: PrismaUserRepository,
    },
    {
      provide: IPasswordService,
      useClass: BcryptPasswordService,
    },
    {
      provide: ITokenService,
      useClass: JwtTokenService,
    },
    {
      provide: IEventPublisher,
      useClass: NestEventPublisher,
    },
    
    // Application Services
    AuthApplicationService,
  ],
  exports: [IUserRepository],
})
export class AuthModule {}
```

## Migration Checklist

### Phase 1: Domain Layer ✓
- [ ] Create value objects (Email, UserId, Password)
- [ ] Create domain entities (User)
- [ ] Create domain events
- [ ] Create repository interfaces
- [ ] Create domain services

### Phase 2: Application Layer
- [ ] Create commands and queries
- [ ] Create command/query handlers
- [ ] Implement CQRS pattern
- [ ] Create application services

### Phase 3: Infrastructure Layer
- [ ] Update repository implementations
- [ ] Create infrastructure services
- [ ] Create adapters for external services

### Phase 4: Presentation Layer
- [ ] Update controllers to use commands/queries
- [ ] Update DTOs
- [ ] Update guards and interceptors

### Phase 5: Testing
- [ ] Write unit tests for domain logic
- [ ] Write integration tests for repositories
- [ ] Write e2e tests for API endpoints
- [ ] Update existing tests

## Benefits After Migration

1. **Clear Separation of Concerns**: Each layer has a specific responsibility
2. **Testability**: Domain logic can be tested in isolation
3. **Maintainability**: Changes are localized to specific layers
4. **Scalability**: Easy to add new features following established patterns
5. **Type Safety**: Value objects provide compile-time guarantees
6. **Business Logic Protection**: Domain rules are enforced at the entity level

## Next Steps

1. Start with Phase 1 (Domain Layer) as it has no dependencies
2. Gradually migrate existing services to use domain objects
3. Update tests to cover new domain logic
4. Implement CQRS pattern for better separation
5. Add event-driven architecture for side effects
