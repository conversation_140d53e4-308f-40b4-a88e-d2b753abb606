# DDD Quick Reference Guide

## Core Concepts

### Value Objects
**When to use**: Immutable objects that represent descriptive aspects with no identity
**Characteristics**: Immutable, equality by value, self-validating

```typescript
export class Email {
  private readonly value: string;
  
  constructor(email: string) {
    if (!this.isValid(email)) {
      throw new InvalidValueObjectException('Invalid email');
    }
    this.value = email.toLowerCase().trim();
  }
  
  getValue(): string { return this.value; }
  equals(other: Email): boolean { return this.value === other.value; }
}
```

### Entities
**When to use**: Objects with distinct identity that runs through time
**Characteristics**: Has identity, mutable, contains business logic

```typescript
export class User {
  private constructor(
    private readonly id: UserId,
    private email: Email,
    // ... other properties
  ) {}
  
  static create(email: Email): User {
    return new User(UserId.generate(), email);
  }
  
  changeEmail(newEmail: Email): void {
    this.email = newEmail;
    // Business logic here
  }
}
```

### Domain Services
**When to use**: Business logic that doesn't fit in entities or value objects
**Characteristics**: Stateless, operates on domain objects

```typescript
@Injectable()
export class PasswordResetDomainService {
  async initiateReset(email: Email): Promise<ResetToken> {
    // Complex business logic involving multiple entities
  }
}
```

### Repositories
**When to use**: Abstraction for data access
**Characteristics**: Interface in domain, implementation in infrastructure

```typescript
// Domain layer
export interface IUserRepository {
  findById(id: UserId): Promise<User | null>;
  save(user: User): Promise<void>;
}

// Infrastructure layer
@Injectable()
export class PrismaUserRepository implements IUserRepository {
  // Implementation details
}
```

## CQRS Patterns

### Commands (Write Operations)
```typescript
export class CreateUserCommand {
  constructor(
    public readonly email: string,
    public readonly password: string,
  ) {}
}

@CommandHandler(CreateUserCommand)
export class CreateUserHandler {
  async execute(command: CreateUserCommand): Promise<void> {
    // Handle write operation
  }
}
```

### Queries (Read Operations)
```typescript
export class GetUserQuery {
  constructor(public readonly id: string) {}
}

@QueryHandler(GetUserQuery)
export class GetUserHandler {
  async execute(query: GetUserQuery): Promise<UserDto> {
    // Handle read operation
  }
}
```

## Common Patterns

### Factory Pattern
```typescript
export class UserFactory {
  static createFromRegistration(
    email: string,
    password: string,
  ): User {
    const emailVO = new Email(email);
    const passwordVO = new Password(password);
    return User.create(emailVO, passwordVO);
  }
}
```

### Specification Pattern
```typescript
export class ActiveUserSpecification {
  isSatisfiedBy(user: User): boolean {
    return user.getIsActive() && user.getIsEmailVerified();
  }
}
```

### Domain Events
```typescript
export class UserCreatedEvent {
  constructor(
    public readonly userId: UserId,
    public readonly email: Email,
    public readonly occurredAt: Date = new Date(),
  ) {}
}

// Publishing
this.eventPublisher.publish(new UserCreatedEvent(user.getId(), user.getEmail()));
```

## Error Handling

### Domain Exceptions
```typescript
export class DomainException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DomainException';
  }
}

export class InvalidValueObjectException extends DomainException {
  constructor(message: string) {
    super(`Invalid value object: ${message}`);
  }
}
```

## Testing Patterns

### Unit Testing Value Objects
```typescript
describe('Email', () => {
  it('should create valid email', () => {
    const email = new Email('<EMAIL>');
    expect(email.getValue()).toBe('<EMAIL>');
  });
  
  it('should throw for invalid email', () => {
    expect(() => new Email('invalid')).toThrow(InvalidValueObjectException);
  });
});
```

### Unit Testing Entities
```typescript
describe('User', () => {
  it('should change email', () => {
    const user = User.create(new Email('<EMAIL>'));
    const newEmail = new Email('<EMAIL>');
    
    user.changeEmail(newEmail);
    
    expect(user.getEmail()).toEqual(newEmail);
  });
});
```

### Testing Command Handlers
```typescript
describe('CreateUserHandler', () => {
  it('should create user', async () => {
    const command = new CreateUserCommand('<EMAIL>', 'password');
    const mockRepo = { save: jest.fn() };
    const handler = new CreateUserHandler(mockRepo);
    
    await handler.execute(command);
    
    expect(mockRepo.save).toHaveBeenCalled();
  });
});
```

## Module Structure Template

```
src/modules/[module-name]/
├── domain/
│   ├── entities/
│   ├── value-objects/
│   ├── services/
│   ├── events/
│   └── interfaces/
├── application/
│   ├── commands/
│   ├── queries/
│   ├── handlers/
│   └── services/
├── infrastructure/
│   ├── repositories/
│   ├── services/
│   └── adapters/
└── presentation/
    ├── controllers/
    ├── dto/
    └── guards/
```

## Dependency Rules

1. **Domain Layer**: No dependencies on other layers
2. **Application Layer**: Can depend on Domain only
3. **Infrastructure Layer**: Can depend on Domain and Application
4. **Presentation Layer**: Can depend on Application only

## Best Practices Checklist

### Value Objects
- [ ] Immutable
- [ ] Self-validating
- [ ] Equality by value
- [ ] No identity
- [ ] Side-effect free methods

### Entities
- [ ] Has identity
- [ ] Contains business logic
- [ ] Encapsulates state
- [ ] Factory methods for creation
- [ ] Meaningful business methods

### Domain Services
- [ ] Stateless
- [ ] Contains complex business logic
- [ ] Operates on domain objects
- [ ] No infrastructure dependencies

### Repositories
- [ ] Interface in domain layer
- [ ] Implementation in infrastructure
- [ ] Returns domain objects
- [ ] Hides persistence details

### Commands/Queries
- [ ] Immutable data structures
- [ ] Single responsibility
- [ ] Clear naming
- [ ] Validation in handlers

## Common Anti-Patterns to Avoid

### Anemic Domain Model
❌ **Wrong**: Entities with only getters/setters
```typescript
class User {
  public email: string;
  public isActive: boolean;
}
```

✅ **Right**: Rich domain model with behavior
```typescript
class User {
  private email: Email;
  private isActive: boolean;
  
  activate(): void {
    if (this.isActive) {
      throw new DomainException('User already active');
    }
    this.isActive = true;
  }
}
```

### Primitive Obsession
❌ **Wrong**: Using primitives everywhere
```typescript
function createUser(email: string, age: number): void {
  // email could be invalid, age could be negative
}
```

✅ **Right**: Using value objects
```typescript
function createUser(email: Email, age: Age): void {
  // Validation guaranteed by value objects
}
```

### Leaky Abstractions
❌ **Wrong**: Domain depending on infrastructure
```typescript
class User {
  constructor(private prisma: PrismaService) {} // ❌ Infrastructure dependency
}
```

✅ **Right**: Pure domain objects
```typescript
class User {
  constructor(private id: UserId, private email: Email) {} // ✅ Pure domain
}
```

## Quick Commands

### Generate Value Object
```bash
# Create new value object
mkdir -p src/modules/[module]/domain/value-objects
touch src/modules/[module]/domain/value-objects/[name].vo.ts
```

### Generate Entity
```bash
# Create new entity
mkdir -p src/modules/[module]/domain/entities
touch src/modules/[module]/domain/entities/[name].entity.ts
```

### Generate Command/Handler
```bash
# Create command and handler
mkdir -p src/modules/[module]/application/{commands,handlers}
touch src/modules/[module]/application/commands/[name].command.ts
touch src/modules/[module]/application/handlers/[name].handler.ts
```
