# Domain-Driven Design (DDD) Guide for NestJS Applications

## Table of Contents

1. [Introduction](#introduction)
2. [Core DDD Concepts](#core-ddd-concepts)
3. [Clean Architecture Layers](#clean-architecture-layers)
4. [Implementation Patterns](#implementation-patterns)
5. [Project Structure](#project-structure)
6. [Value Objects](#value-objects)
7. [Domain Entities](#domain-entities)
8. [Domain Services](#domain-services)
9. [Repository Pattern](#repository-pattern)
10. [CQRS Implementation](#cqrs-implementation)
11. [Testing Strategy](#testing-strategy)
12. [Best Practices](#best-practices)

## Introduction

Domain-Driven Design (DDD) is a software development approach that focuses on modeling software to match a domain according to input from domain experts. This guide provides practical implementation patterns for building enterprise-grade NestJS applications using DDD principles.

### Why DDD?

- **Business Logic Separation**: Keep domain logic pure and isolated from infrastructure concerns
- **Maintainability**: Clear separation of concerns makes code easier to understand and modify
- **Scalability**: Architecture that can grow with application complexity
- **Testability**: Pure domain logic is easier to test
- **Team Communication**: Ubiquitous language improves collaboration

## Core DDD Concepts

### Ubiquitous Language
A common language shared by developers and domain experts to describe the business domain.

### Bounded Context
A logical boundary within which a particular domain model is defined and applicable.

### Domain Model
The conceptual model of the domain that incorporates both behavior and data.

### Aggregates
A cluster of domain objects that can be treated as a single unit for data changes.

## Clean Architecture Layers

Our DDD implementation follows Clean Architecture principles with four distinct layers:

```
┌─────────────────────────────────────┐
│         Presentation Layer          │  ← Controllers, DTOs, API endpoints
├─────────────────────────────────────┤
│         Application Layer           │  ← Use cases, Commands/Queries, Services
├─────────────────────────────────────┤
│         Domain Layer (Core)         │  ← Entities, Value Objects, Domain Services
├─────────────────────────────────────┤
│         Infrastructure Layer        │  ← Repositories, External Services, ORM
└─────────────────────────────────────┘
```

### 1. Domain Layer (Core)
- **Purpose**: Contains pure business logic with no external dependencies
- **Components**: Entities, Value Objects, Domain Services, Interfaces
- **Rule**: No dependencies on other layers

### 2. Application Layer
- **Purpose**: Orchestrates use cases using domain objects
- **Components**: Commands, Queries, Application Services, DTOs
- **Rule**: Can depend on Domain layer only

### 3. Infrastructure Layer
- **Purpose**: Implements technical concerns and interfaces defined in the core
- **Components**: Repositories, External APIs, Database implementations
- **Rule**: Can depend on Domain and Application layers

### 4. Presentation Layer
- **Purpose**: Handles HTTP requests and responses
- **Components**: Controllers, Modules, Guards, Interceptors
- **Rule**: Can depend on Application layer

## Implementation Patterns

### Directory Structure

```
src/
├── modules/
│   └── [module-name]/
│       ├── domain/                    # Domain Layer
│       │   ├── entities/
│       │   ├── value-objects/
│       │   ├── services/
│       │   └── interfaces/
│       ├── application/               # Application Layer
│       │   ├── commands/
│       │   ├── queries/
│       │   ├── handlers/
│       │   └── services/
│       ├── infrastructure/            # Infrastructure Layer
│       │   ├── repositories/
│       │   ├── services/
│       │   └── adapters/
│       └── presentation/              # Presentation Layer
│           ├── controllers/
│           ├── dto/
│           └── guards/
└── shared/                           # Shared utilities
    ├── domain/
    ├── infrastructure/
    └── presentation/
```

## Value Objects

Value objects are immutable objects that represent descriptive aspects of the domain with no conceptual identity.

### Characteristics
- **Immutable**: Cannot be changed after creation
- **Equality by value**: Two value objects are equal if their values are equal
- **Self-validating**: Validate data at creation time
- **Side-effect free**: Methods don't change state

### Example: Email Value Object

```typescript
export class Email {
  private readonly value: string;

  constructor(email: string) {
    if (!this.isValid(email)) {
      throw new InvalidValueObjectException('Invalid email format');
    }
    this.value = email.toLowerCase().trim();
  }

  private isValid(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  getValue(): string {
    return this.value;
  }

  equals(email: Email): boolean {
    return this.value === email.getValue();
  }

  toString(): string {
    return this.value;
  }
}
```

### Example: Money Value Object

```typescript
export class Money {
  private readonly amount: number;
  private readonly currency: string;

  constructor(amount: number, currency: string) {
    if (amount < 0) {
      throw new InvalidValueObjectException('Amount cannot be negative');
    }
    if (!currency || currency.length !== 3) {
      throw new InvalidValueObjectException('Invalid currency code');
    }
    
    this.amount = Math.round(amount * 100) / 100; // Round to 2 decimal places
    this.currency = currency.toUpperCase();
  }

  getAmount(): number {
    return this.amount;
  }

  getCurrency(): string {
    return this.currency;
  }

  add(money: Money): Money {
    if (this.currency !== money.currency) {
      throw new DomainException('Cannot add different currencies');
    }
    return new Money(this.amount + money.amount, this.currency);
  }

  equals(money: Money): boolean {
    return this.amount === money.amount && this.currency === money.currency;
  }
}
```

## Domain Entities

Entities are objects that have a distinct identity that runs through time and different representations.

### Characteristics
- **Identity**: Has a unique identifier
- **Mutable**: Can change state over time
- **Lifecycle**: Can be created, modified, and destroyed
- **Business Logic**: Contains domain behavior

### Example: User Entity

```typescript
export class User {
  private constructor(
    private readonly id: UserId,
    private email: Email,
    private profile: UserProfile,
    private readonly createdAt: Date,
    private updatedAt: Date,
    private isActive: boolean = true,
  ) {}

  static create(
    email: Email,
    profile: UserProfile,
  ): User {
    return new User(
      UserId.generate(),
      email,
      profile,
      new Date(),
      new Date(),
    );
  }

  static fromPersistence(data: UserPersistenceData): User {
    return new User(
      new UserId(data.id),
      new Email(data.email),
      UserProfile.fromPersistence(data.profile),
      data.createdAt,
      data.updatedAt,
      data.isActive,
    );
  }

  // Business methods
  changeEmail(newEmail: Email): void {
    if (this.email.equals(newEmail)) {
      return;
    }
    
    this.email = newEmail;
    this.updatedAt = new Date();
  }

  deactivate(): void {
    if (!this.isActive) {
      throw new DomainException('User is already inactive');
    }
    
    this.isActive = false;
    this.updatedAt = new Date();
  }

  activate(): void {
    if (this.isActive) {
      throw new DomainException('User is already active');
    }
    
    this.isActive = true;
    this.updatedAt = new Date();
  }

  // Getters
  getId(): UserId {
    return this.id;
  }

  getEmail(): Email {
    return this.email;
  }

  getProfile(): UserProfile {
    return this.profile;
  }

  getIsActive(): boolean {
    return this.isActive;
  }

  getCreatedAt(): Date {
    return this.createdAt;
  }

  getUpdatedAt(): Date {
    return this.updatedAt;
  }
}
```

## Domain Services

Domain services contain domain logic that doesn't naturally fit within an entity or value object.

### When to Use Domain Services
- Logic involves multiple entities
- Complex business rules
- Domain calculations
- Cross-aggregate operations

### Example: Password Reset Domain Service

```typescript
@Injectable()
export class PasswordResetDomainService {
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly tokenGenerator: ITokenGenerator,
    private readonly eventPublisher: IEventPublisher,
  ) {}

  async initiatePasswordReset(email: Email): Promise<PasswordResetToken> {
    const user = await this.userRepository.findByEmail(email);

    if (!user) {
      // Don't reveal if user exists for security
      throw new DomainException('Password reset initiated');
    }

    if (!user.getIsActive()) {
      throw new DomainException('Cannot reset password for inactive user');
    }

    const token = this.tokenGenerator.generatePasswordResetToken();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    const resetToken = PasswordResetToken.create(
      user.getId(),
      token,
      expiresAt,
    );

    // Publish domain event
    this.eventPublisher.publish(
      new PasswordResetInitiatedEvent(user.getId(), email, token),
    );

    return resetToken;
  }

  async completePasswordReset(
    token: string,
    newPassword: Password,
  ): Promise<void> {
    const resetToken = await this.userRepository.findPasswordResetToken(token);

    if (!resetToken || resetToken.isExpired()) {
      throw new DomainException('Invalid or expired reset token');
    }

    const user = await this.userRepository.findById(resetToken.getUserId());

    if (!user) {
      throw new DomainException('User not found');
    }

    user.changePassword(newPassword);
    resetToken.markAsUsed();

    await this.userRepository.save(user);
    await this.userRepository.savePasswordResetToken(resetToken);

    // Publish domain event
    this.eventPublisher.publish(
      new PasswordResetCompletedEvent(user.getId()),
    );
  }
}
```

## Repository Pattern

Repositories provide an abstraction layer between the domain and data mapping layers.

### Interface Definition

```typescript
export interface IUserRepository {
  findById(id: UserId): Promise<User | null>;
  findByEmail(email: Email): Promise<User | null>;
  save(user: User): Promise<void>;
  delete(id: UserId): Promise<void>;
  findPasswordResetToken(token: string): Promise<PasswordResetToken | null>;
  savePasswordResetToken(token: PasswordResetToken): Promise<void>;
}
```

### Implementation

```typescript
@Injectable()
export class PrismaUserRepository implements IUserRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findById(id: UserId): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { id: id.getValue() },
      include: { profile: true },
    });

    return userData ? User.fromPersistence(userData) : null;
  }

  async findByEmail(email: Email): Promise<User | null> {
    const userData = await this.prisma.user.findUnique({
      where: { email: email.getValue() },
      include: { profile: true },
    });

    return userData ? User.fromPersistence(userData) : null;
  }

  async save(user: User): Promise<void> {
    const data = this.toPersistence(user);

    await this.prisma.user.upsert({
      where: { id: data.id },
      update: data,
      create: data,
    });
  }

  async delete(id: UserId): Promise<void> {
    await this.prisma.user.delete({
      where: { id: id.getValue() },
    });
  }

  private toPersistence(user: User): any {
    return {
      id: user.getId().getValue(),
      email: user.getEmail().getValue(),
      isActive: user.getIsActive(),
      createdAt: user.getCreatedAt(),
      updatedAt: user.getUpdatedAt(),
      profile: {
        firstName: user.getProfile().getFirstName(),
        lastName: user.getProfile().getLastName(),
      },
    };
  }
}
```

## CQRS Implementation

Command Query Responsibility Segregation (CQRS) separates read and write operations.

### Commands (Write Operations)

```typescript
export class CreateUserCommand {
  constructor(
    public readonly email: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly password: string,
  ) {}
}

@CommandHandler(CreateUserCommand)
export class CreateUserCommandHandler implements ICommandHandler<CreateUserCommand> {
  constructor(
    private readonly userRepository: IUserRepository,
    private readonly passwordService: IPasswordService,
    private readonly eventPublisher: IEventPublisher,
  ) {}

  async execute(command: CreateUserCommand): Promise<UserCreatedResponse> {
    const { email, firstName, lastName, password } = command;

    // Create value objects
    const emailVO = new Email(email);
    const profile = new UserProfile(firstName, lastName);
    const passwordVO = await this.passwordService.hashPassword(password);

    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(emailVO);
    if (existingUser) {
      throw new DomainException('User with this email already exists');
    }

    // Create user entity
    const user = User.create(emailVO, profile);

    // Save to repository
    await this.userRepository.save(user);

    // Publish domain event
    this.eventPublisher.publish(
      new UserCreatedEvent(user.getId(), emailVO),
    );

    return {
      id: user.getId().getValue(),
      email: user.getEmail().getValue(),
      createdAt: user.getCreatedAt(),
    };
  }
}
```

### Queries (Read Operations)

```typescript
export class GetUserQuery {
  constructor(public readonly id: string) {}
}

@QueryHandler(GetUserQuery)
export class GetUserQueryHandler implements IQueryHandler<GetUserQuery> {
  constructor(private readonly userRepository: IUserRepository) {}

  async execute(query: GetUserQuery): Promise<UserDetailResponse> {
    const userId = new UserId(query.id);
    const user = await this.userRepository.findById(userId);

    if (!user) {
      throw new EntityNotFoundException('User', query.id);
    }

    return {
      id: user.getId().getValue(),
      email: user.getEmail().getValue(),
      profile: {
        firstName: user.getProfile().getFirstName(),
        lastName: user.getProfile().getLastName(),
      },
      isActive: user.getIsActive(),
      createdAt: user.getCreatedAt(),
      updatedAt: user.getUpdatedAt(),
    };
  }
}
```

## Testing Strategy

### Unit Testing Domain Logic

```typescript
describe('User Entity', () => {
  describe('create', () => {
    it('should create a new user with valid data', () => {
      // Arrange
      const email = new Email('<EMAIL>');
      const profile = new UserProfile('John', 'Doe');

      // Act
      const user = User.create(email, profile);

      // Assert
      expect(user.getEmail()).toEqual(email);
      expect(user.getProfile()).toEqual(profile);
      expect(user.getIsActive()).toBe(true);
      expect(user.getId()).toBeDefined();
    });
  });

  describe('changeEmail', () => {
    it('should change email when new email is different', () => {
      // Arrange
      const user = User.create(
        new Email('<EMAIL>'),
        new UserProfile('John', 'Doe'),
      );
      const newEmail = new Email('<EMAIL>');

      // Act
      user.changeEmail(newEmail);

      // Assert
      expect(user.getEmail()).toEqual(newEmail);
    });

    it('should not change email when new email is the same', () => {
      // Arrange
      const email = new Email('<EMAIL>');
      const user = User.create(email, new UserProfile('John', 'Doe'));
      const originalUpdatedAt = user.getUpdatedAt();

      // Act
      user.changeEmail(email);

      // Assert
      expect(user.getEmail()).toEqual(email);
      expect(user.getUpdatedAt()).toEqual(originalUpdatedAt);
    });
  });
});
```

### Testing Value Objects

```typescript
describe('Email Value Object', () => {
  describe('constructor', () => {
    it('should create valid email', () => {
      const email = new Email('<EMAIL>');
      expect(email.getValue()).toBe('<EMAIL>');
    });

    it('should normalize email to lowercase', () => {
      const email = new Email('<EMAIL>');
      expect(email.getValue()).toBe('<EMAIL>');
    });

    it('should trim whitespace', () => {
      const email = new Email('  <EMAIL>  ');
      expect(email.getValue()).toBe('<EMAIL>');
    });

    it('should throw for invalid email format', () => {
      expect(() => new Email('invalid-email')).toThrow(InvalidValueObjectException);
    });
  });

  describe('equals', () => {
    it('should return true for equal emails', () => {
      const email1 = new Email('<EMAIL>');
      const email2 = new Email('<EMAIL>');
      expect(email1.equals(email2)).toBe(true);
    });

    it('should return false for different emails', () => {
      const email1 = new Email('<EMAIL>');
      const email2 = new Email('<EMAIL>');
      expect(email1.equals(email2)).toBe(false);
    });
  });
});
```

### Testing Commands and Queries

```typescript
describe('CreateUserCommandHandler', () => {
  let handler: CreateUserCommandHandler;
  let userRepository: jest.Mocked<IUserRepository>;
  let passwordService: jest.Mocked<IPasswordService>;
  let eventPublisher: jest.Mocked<IEventPublisher>;

  beforeEach(() => {
    userRepository = {
      findByEmail: jest.fn(),
      save: jest.fn(),
    } as any;

    passwordService = {
      hashPassword: jest.fn(),
    } as any;

    eventPublisher = {
      publish: jest.fn(),
    } as any;

    handler = new CreateUserCommandHandler(
      userRepository,
      passwordService,
      eventPublisher,
    );
  });

  it('should create user when email is unique', async () => {
    // Arrange
    const command = new CreateUserCommand(
      '<EMAIL>',
      'John',
      'Doe',
      'password123',
    );

    userRepository.findByEmail.mockResolvedValue(null);
    passwordService.hashPassword.mockResolvedValue(new Password('hashed'));

    // Act
    const result = await handler.execute(command);

    // Assert
    expect(result).toEqual({
      id: expect.any(String),
      email: '<EMAIL>',
      createdAt: expect.any(Date),
    });
    expect(userRepository.save).toHaveBeenCalledWith(expect.any(User));
    expect(eventPublisher.publish).toHaveBeenCalledWith(
      expect.any(UserCreatedEvent),
    );
  });

  it('should throw when user already exists', async () => {
    // Arrange
    const command = new CreateUserCommand(
      '<EMAIL>',
      'John',
      'Doe',
      'password123',
    );

    const existingUser = User.create(
      new Email('<EMAIL>'),
      new UserProfile('Jane', 'Doe'),
    );
    userRepository.findByEmail.mockResolvedValue(existingUser);

    // Act & Assert
    await expect(handler.execute(command)).rejects.toThrow(
      'User with this email already exists',
    );
  });
});
```

## Best Practices

### 1. Keep Domain Logic Pure
- No external dependencies in domain layer
- No infrastructure concerns in entities
- Use dependency inversion for external services

### 2. Use Value Objects Extensively
- Wrap primitives in value objects
- Validate at creation time
- Make them immutable
- Implement equality by value

### 3. Aggregate Design
- Keep aggregates small
- One repository per aggregate
- Maintain consistency within aggregates
- Use eventual consistency between aggregates

### 4. Event-Driven Architecture
- Use domain events for side effects
- Publish events after successful persistence
- Keep event handlers idempotent
- Use events for integration between bounded contexts

### 5. Testing Strategy
- Test domain logic in isolation
- Use test doubles for dependencies
- Test edge cases and error conditions
- Maintain high test coverage for domain layer

### 6. Error Handling
- Use domain-specific exceptions
- Validate early and fail fast
- Provide meaningful error messages
- Handle errors at appropriate layers

### 7. Performance Considerations
- Use read models for queries
- Implement caching at infrastructure layer
- Consider CQRS for read/write separation
- Optimize database queries in repositories

## Conclusion

Domain-Driven Design provides a powerful approach for building maintainable, scalable applications. By following these patterns and practices, you can create a codebase that:

- Reflects the business domain accurately
- Is easy to understand and modify
- Has clear separation of concerns
- Is highly testable
- Can evolve with changing requirements

Remember that DDD is not just about code structure—it's about creating a shared understanding between developers and domain experts to build software that truly serves the business needs.
```
