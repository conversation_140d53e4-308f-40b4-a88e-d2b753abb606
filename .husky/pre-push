# #!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"

# echo "🚀 Running pre-push checks..."

# # Run full linting check (without auto-fix)
# echo "🔧 Running ESLint check..."
# npm run lint:check

# # Run formatting check (without auto-fix)
# echo "📝 Running Prettier check..."
# npm run format:check

# # Run type checking
# echo "🔍 Running TypeScript type checking..."
# npm run build

# # Run all tests including e2e
# echo "🧪 Running all tests..."
# npm test

# # Optional: Run e2e tests (uncomment if needed)
# # echo "🌐 Running e2e tests..."
# # npm run test:e2e

# echo "✅ Pre-push checks completed successfully!"
