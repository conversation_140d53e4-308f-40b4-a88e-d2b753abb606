# #!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"

echo "🔄 Running pre-merge checks..."

# Run full formatting on entire codebase
echo "📝 Formatting entire codebase..."
npm run format

# Run linting with auto-fix
echo "🔧 Running ESLint with auto-fix..."
npm run lint

# Run type checking
echo "🔍 Running TypeScript type checking..."
npm run build

# Run all tests including unit tests
echo "🧪 Running all tests..."
npm test

# Optional: Run e2e tests (uncomment if you want full test suite before merges)
# echo "🌐 Running e2e tests..."
# npm run test:e2e

# Check for any uncommitted changes after formatting/linting
if ! git diff --quiet; then
    echo "⚠️  Auto-formatting/linting made changes. Please review and commit them."
    echo "📋 Files that were modified:"
    git diff --name-only
    echo ""
    echo "💡 You can add these changes with: git add ."
    exit 1
fi

echo "✅ Pre-merge checks completed successfully!"
