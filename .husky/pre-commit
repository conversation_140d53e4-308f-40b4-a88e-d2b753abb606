# #!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# Run linting with auto-fix
echo "🔧 Running ESLint with auto-fix..."
# npm run lint

# Run formatting
echo "📝 Running Prettier formatting..."
npm run format

# Run type checking
echo "🔍 Running TypeScript type checking..."
npm run build

# Run unit tests (excluding e2e for speed)S
# echo "🧪 Running unit tests..."
# npm test

echo "✅ Pre-commit checks completed successfully!"
