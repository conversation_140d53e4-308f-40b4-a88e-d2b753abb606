

#!/bin/sh

echo "🔍 Running pre-commit checks..."

# Get list of staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)

if [ -z "$STAGED_FILES" ]; then
  echo "ℹ️  No files staged for commit."
  exit 0
fi

# Check if there are any JS/TS/Vue files
JS_FILES=$(echo "$STAGED_FILES" | grep -E '\.(js|jsx|ts|tsx|vue)$' || true)

if [ -n "$JS_FILES" ]; then
  echo "� Found JavaScript/TypeScript/Vue files to check"

  # Run Prettier on staged files
  echo "🎨 Running Prettier..."
  echo "$JS_FILES" | xargs npx prettier --write --ignore-unknown

  # Add the formatted files back to staging
  echo "$JS_FILES" | xargs git add

  echo "✅ Prettier formatting completed!"
fi

echo "✅ All pre-commit checks passed!"
