# #!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"

echo "🎉 Post-merge cleanup and setup..."

# Check if package.json or package-lock.json was changed and reinstall dependencies
if git diff HEAD@{1} --name-only | grep -q "package.json\|package-lock.json"; then
  echo "📦 Package files changed, reinstalling dependencies..."
  npm ci
fi

# Check if Prisma schema was changed and regenerate client
if git diff HEAD@{1} --name-only | grep -q "prisma/schema.prisma"; then
  echo "🗄️  Prisma schema changed, regenerating client..."
  npx prisma generate
fi

# Check if there are new migrations and prompt to run them
if git diff HEAD@{1} --name-only | grep -q "prisma/migrations/"; then
  echo "🗄️  New Prisma migrations detected."
  echo "💡 You may want to run: npx prisma migrate deploy"
fi

# Format the entire codebase to ensure consistency after merge
echo "📝 Formatting codebase for consistency..."
npm run format

# Check if any files were formatted and stage them
if ! git diff --quiet; then
    echo "📋 Auto-formatted files after merge:"
    git diff --name-only
    echo ""
    echo "💡 Consider committing these formatting changes: git add . && git commit -m 'style: auto-format after merge'"
fi

echo "✅ Post-merge setup completed!"
