

echo "🔄 Running post-merge tasks..."

# Check if package.json or package-lock.json changed
changed_files="$(git diff-tree -r --name-only --no-commit-id ORIG_HEAD HEAD)"

check_run() {
  echo "$changed_files" | grep --quiet "$1" && eval "$2"
}

# Install dependencies if package files changed
check_run "package.json\|package-lock.json" "
  echo '📦 Package files changed, installing dependencies...'
  npm install
  if [ \$? -eq 0 ]; then
    echo '✅ Dependencies installed successfully!'
  else
    echo '❌ Failed to install dependencies!'
    exit 1
  fi
"

# Run type checking if TypeScript files changed
check_run "\.ts$\|\.vue$\|tsconfig\.json" "
  echo '🔧 TypeScript files changed, running type check...'
  npm run type-check
  if [ \$? -eq 0 ]; then
    echo '✅ Type check passed!'
  else
    echo '⚠️  Type check failed. Please review the changes.'
  fi
"

echo "✅ Post-merge tasks completed!"
