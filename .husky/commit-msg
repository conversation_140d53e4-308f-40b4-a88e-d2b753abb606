# #!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"

echo "📝 Validating commit message..."

# Read the commit message
commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ Invalid commit message format!"
    echo ""
    echo "Commit message should follow conventional commits format:"
    echo "  <type>[optional scope]: <description>"
    echo ""
    echo "Types: feat, fix, docs, style, refactor, test, chore, perf, ci, build, revert"
    echo ""
    echo "Examples:"
    echo "  feat: add user authentication"
    echo "  fix(auth): resolve login validation issue"
    echo "  docs: update API documentation"
    echo "  refactor(services): simplify auth service structure"
    echo ""
    exit 1
fi

echo "✅ Commit message format is valid!"
