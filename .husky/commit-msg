#!/bin/sh

# Conventional Commits validation
# This hook validates that commit messages follow the conventional commits format
# Format: type(scope): description
# Types: feat, fix, docs, style, refactor, test, chore, ci, build, perf

commit_regex='^(feat|fix|docs|style|refactor|test|chore|ci|build|perf)(\(.+\))?: .{1,50}'

error_msg="❌ Invalid commit message format!

Your commit message should follow the Conventional Commits format:
  type(scope): description

Examples:
  feat: add new user authentication
  fix(auth): resolve login redirect issue
  docs: update API documentation
  style: format code with prettier
  refactor(components): simplify user card logic
  test: add unit tests for auth service
  chore: update dependencies

Valid types: feat, fix, docs, style, refactor, test, chore, ci, build, perf
Description should be 1-50 characters long."

if ! grep -qE "$commit_regex" "$1"; then
  echo "$error_msg"
  exit 1
fi

echo "✅ Commit message format is valid!"
