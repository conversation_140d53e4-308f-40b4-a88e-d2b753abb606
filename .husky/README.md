# Husky Git Hooks

This project uses [<PERSON><PERSON>](https://typicode.github.io/husky/) to manage Git hooks for maintaining code quality and consistency.

## Available Hooks

### Pre-commit Hook
**File**: `.husky/pre-commit`

Runs before each commit to ensure code quality:

1. **Prettier**: Formats staged JavaScript/TypeScript/Vue files according to project style guidelines
2. **Auto-staging**: Automatically stages formatted files

**What it checks**: Only staged `.js`, `.jsx`, `.ts`, `.tsx`, and `.vue` files for performance.

### Commit Message Hook
**File**: `.husky/commit-msg`

Validates commit messages follow the Conventional Commits format:

- **Format**: `type(scope): description`
- **Valid types**: feat, fix, docs, style, refactor, test, chore, ci, build, perf
- **Examples**:
  - `feat: add user authentication`
  - `fix(auth): resolve login redirect issue`
  - `docs: update API documentation`

### Post-merge Hook
**File**: `.husky/post-merge`

Runs after a successful merge to keep the development environment up-to-date:

1. **Dependencies**: Installs npm packages if `package.json` or `package-lock.json` changed
2. **Type Check**: Runs TypeScript type checking if TypeScript files changed

## How to Use

### Installation
Hooks are automatically installed when you run:
```bash
npm install
```

### Testing Hooks
You can test if hooks are working by making a commit. All hooks should run automatically.

### Bypassing Hooks (Not Recommended)
If you need to bypass hooks in an emergency:
```bash
git commit --no-verify -m "Emergency commit"
```

### Manual Hook Execution
You can manually run hooks for testing:
```bash
# Test pre-commit hook
./.husky/pre-commit

# Test post-merge hook
./.husky/post-merge
```

## Troubleshooting

### Hook Fails
1. Fix the reported issues
2. Stage your changes: `git add .`
3. Try committing again

### Permission Issues
Make hooks executable:
```bash
chmod +x .husky/pre-commit .husky/post-merge
```

### Disable Hooks Temporarily
```bash
# Disable all hooks
git config core.hooksPath /dev/null

# Re-enable hooks
git config core.hooksPath .husky
```

## Scripts Used

- `npm run lint` - ESLint with auto-fix
- `npm run format` - Prettier formatting
- `npm run type-check` - TypeScript type checking
- `npm install` - Install dependencies
