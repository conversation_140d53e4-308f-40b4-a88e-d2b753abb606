/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACol: typeof import('ant-design-vue/es')['Col']
    ActionButton: typeof import('./src/components/common/ActionButton.vue')['default']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AlertDialog: typeof import('./src/components/ui/alert-dialog/AlertDialog.vue')['default']
    AlertDialogAction: typeof import('./src/components/ui/alert-dialog/AlertDialogAction.vue')['default']
    AlertDialogCancel: typeof import('./src/components/ui/alert-dialog/AlertDialogCancel.vue')['default']
    AlertDialogContent: typeof import('./src/components/ui/alert-dialog/AlertDialogContent.vue')['default']
    AlertDialogDescription: typeof import('./src/components/ui/alert-dialog/AlertDialogDescription.vue')['default']
    AlertDialogFooter: typeof import('./src/components/ui/alert-dialog/AlertDialogFooter.vue')['default']
    AlertDialogHeader: typeof import('./src/components/ui/alert-dialog/AlertDialogHeader.vue')['default']
    AlertDialogTitle: typeof import('./src/components/ui/alert-dialog/AlertDialogTitle.vue')['default']
    AlertDialogTrigger: typeof import('./src/components/ui/alert-dialog/AlertDialogTrigger.vue')['default']
    AppDrawer: typeof import('./src/components/AppDrawer.vue')['default']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpace: typeof import('ant-design-vue/es')['Space']
    AStep: typeof import('ant-design-vue/es')['Step']
    ASteps: typeof import('ant-design-vue/es')['Steps']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATag: typeof import('ant-design-vue/es')['Tag']
    BaseConfirmDialog: typeof import('./src/components/BaseConfirmDialog.vue')['default']
    Button: typeof import('./src/components/ui/button/Button.vue')['default']
    ChangeEmailDialog: typeof import('./src/components/users/ChangeEmailDialog.vue')['default']
    ChangePasswordDialog: typeof import('./src/components/users/ChangePasswordDialog.vue')['default']
    CommissionTrendsChart: typeof import('./src/components/charts/CommissionTrendsChart.vue')['default']
    CreateTenantDialog: typeof import('./src/components/tenants/CreateTenantDialog.vue')['default']
    CreateUserDialog: typeof import('./src/components/users/CreateUserDialog.vue')['default']
    DataTable: typeof import('./src/components/common/DataTable.vue')['default']
    DonutChart: typeof import('./src/components/charts/DonutChart.vue')['default']
    GoogleTable: typeof import('./src/components/GoogleTable.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    LoadingOverlay: typeof import('./src/components/common/LoadingOverlay.vue')['default']
    MiniChart: typeof import('./src/components/charts/MiniChart.vue')['default']
    NavigationItem: typeof import('./src/components/sidebar/NavigationItem.vue')['default']
    NavigationMenu: typeof import('./src/components/sidebar/NavigationMenu.vue')['default']
    NotificationBadge: typeof import('./src/components/common/NotificationBadge.vue')['default']
    Paginations: typeof import('./src/components/Paginations.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StateChangeDialog: typeof import('./src/components/users/StateChangeDialog.vue')['default']
    StatusBadge: typeof import('./src/components/common/StatusBadge.vue')['default']
    TenantFilters: typeof import('./src/components/tenants/TenantFilters.vue')['default']
    TenantsTable: typeof import('./src/components/tenants/TenantsTable.vue')['default']
    TheBack: typeof import('./src/components/TheBack.vue')['default']
    TheHeader: typeof import('./src/components/TheHeader.vue')['default']
    ThePageHeader: typeof import('./src/components/ThePageHeader.vue')['default']
    TheRefresh: typeof import('./src/components/TheRefresh.vue')['default']
    TheSidebar: typeof import('./src/components/TheSidebar.vue')['default']
    TheStatus: typeof import('./src/components/TheStatus.vue')['default']
    TheWelcome: typeof import('./src/components/TheWelcome.vue')['default']
    UserActions: typeof import('./src/components/users/UserActions.vue')['default']
    UserFilters: typeof import('./src/components/users/UserFilters.vue')['default']
    UserProfileSection: typeof import('./src/components/sidebar/UserProfileSection.vue')['default']
    UsersTable: typeof import('./src/components/users/UsersTable.vue')['default']
    UserStateConfirmDialog: typeof import('./src/components/users/UserStateConfirmDialog.vue')['default']
    UserStatusChip: typeof import('./src/components/users/UserStatusChip.vue')['default']
    WelcomeItem: typeof import('./src/components/WelcomeItem.vue')['default']
  }
}
