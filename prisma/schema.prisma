generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id        Int    @id @default(autoincrement())
  name      String
  subdomain String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  systemUsers        SystemUser[]
  EmployerDetail     EmployerDetail[]
  Role               Role[]
  CustomFieldMapping CustomFieldMapping[]
  Agent              Agent[]
  CommissionTypes    CommissionTypes[]

  @@map("tenant")
}

enum UserLifeCycle {
  PARTIAL_APPLICATION
  PENDING_APPROVAL
  ACTIVE
  INACTIVE
  REJECTED
  EXITED
  BLACKLISTED
}

model SystemUser {
  id                    Int           @id @default(autoincrement())
  passwordHash          String
  fullName              String
  accountState          UserLifeCycle @default(ACTIVE)
  isActive              Boolean       @default(true)
  email                 String?       @unique
  emailVerified         Boolean       @default(false)
  verified              Boolean       @default(false)
  passwordReset         DateTime?     @db.Timestamptz()
  resetToken            String?
  resetTokenExpires     DateTime?
  isAdministrator       <PERSON><PERSON><PERSON>       @default(false)
  verificationToken     String?
  lastPasswordResetDate DateTime?
  refreshHashedToken    String?
  createbyId            Int?

  // Relations
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  lastLoginDate DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  acceptTerms   Boolean   @default(true)

  @@map("system_user")
}

model EmployerDetail {
  id       Int    @id @default(autoincrement())
  name     String
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("employer_detail")
}

enum CustomFields {
  LOAN_TYPE
}

model CustomFieldMapping {
  id               Int          @id @default(autoincrement())
  name             CustomFields
  tenantId         Int
  tenant           Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  customfieldId    String
  customfieldSetId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("custom_field_mapping")
}

model Role {
  // this references the organograms roles not application roles
  id       Int    @id @default(autoincrement())
  name     String
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  AgentRole AgentRole[]

  @@map("role")
}

model CommissionTypes {
  id       Int    @id @default(autoincrement())
  name     String
  tenantId Int
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("commission_types")
}

model Agent {
  id              Int    @id @default(autoincrement())
  mambuEncodedKey String
  fullname        String
  tenantId        Int
  tenant          Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  AgentRole AgentRole[]

  @@map("agent")
}

model AgentRole {
  agentId  Int
  roleId   Int
  tenantId Int
  agent    Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  role     Role  @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@id([agentId, roleId])
  @@map("agent_role")
}
