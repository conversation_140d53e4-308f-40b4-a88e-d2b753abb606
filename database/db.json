{"roles": [{"id": "role1", "name": "SALE_AGENT", "description": "SALES AGENT ROLE", "rulesCounts": 6, "rules": [{"id": "collo_sav_portfolio_cat1"}, {"id": "<PERSON><PERSON><PERSON>"}, {"id": "sa_end_month_cat1"}, {"id": "sa_end_month_cat2"}, {"id": "sa_end_month_cat3"}, {"id": "sa_portfolio_cat1"}, {"id": "sa_portfolio_cat2"}, {"id": "sa_weekly_loan_type"}, {"id": "sa_daily_loan_type"}, {"id": "sa_weekly_target"}, {"id": "sa_weekly_target_achievement"}, {"id": "sa_daily_target"}, {"id": "sa_daily_target_achievement"}]}, {"id": "role2", "name": "TEAM_LEADERS", "description": "TEAM LEADERS ROLE", "rules": [{"id": "collo_sav_portfolio_cat1"}, {"id": "sa_end_month_cat1"}, {"id": "sa_end_month_cat2"}]}], "rules": [{"id": "collo_sav_portfolio_cat1", "name": "cool sav PAR-Based Portfolio Commissiono", "description": "Fixed amount based on PAR and portfolio size", "enabled": true, "category": "Portfolio Commission", "frequency": "monthly", "conditions": [{"field": "par", "operator": "lte", "value": 98}, {"field": "portfolio", "operator": "gt", "value": 50000}, {"field": "netSales", "operator": "gt", "value": 1000000}, {"field": "netSales", "operator": "lt", "value": 0}], "calculation": {"type": "tiered", "baseField": "portfolio", "comparisonField": "portfolio", "tierValueType": "fixed", "tiers": [{"min": 50000, "max": 100000, "value": 2000}, {"min": 100000, "max": 200000, "value": 3000}, {"min": 200000, "max": 500000, "value": 5000}, {"min": 500000, "max": null, "value": 8000}]}, "priority": 4}, {"id": "<PERSON><PERSON><PERSON>", "name": "Airtimebyomondi", "description": "Airtimebyomondi", "enabled": true, "category": "End Month Sales", "frequency": "monthly", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}], "calculation": {"type": "percentage", "baseField": "netSales", "value": 5}, "priority": 1}, {"id": "sa_end_month_cat1", "name": "Installment-Based Rate Commission", "description": "Commission based on loan term (number of installments)", "enabled": true, "category": "End Month Sales", "frequency": "monthly", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}], "calculation": {"type": "tiered", "baseField": "netSales", "comparisonField": "loanTerm", "tierValueType": "percentage", "tiers": [{"min": 0, "max": 12, "value": 1.5}, {"min": 13, "max": 24, "value": 2}, {"min": 25, "max": 36, "value": 2.5}, {"min": 36, "max": 100036, "value": 0}]}, "priority": 1}, {"id": "sa_end_month_cat2", "name": "Business Type Rate Commission", "description": "Different rates for new vs repeat business", "enabled": true, "category": "End Month Sales", "frequency": "monthly", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}], "calculation": {"type": "formula", "baseField": "netSales", "formula": "data.loanType === \"New\" ? data.netSales * 0.025 : data.netSales * 0.015"}, "priority": 2}, {"id": "sa_end_month_cat3", "name": "Graduated Fixed Amount", "description": "Fixed amount based on sales tiers", "enabled": true, "category": "End Month Sales", "frequency": "monthly", "conditions": [{"field": "monthlyNetSales", "operator": "gt", "value": 0}], "calculation": {"type": "tiered", "baseField": "monthlyNetSales", "comparisonField": "monthlyNetSales", "tierValueType": "fixed", "tiers": [{"min": 0, "max": 100000, "value": 5000}, {"min": 100000, "max": 200000, "value": 10000}, {"min": 200000, "max": 300000, "value": 15000}, {"min": 300000, "max": null, "value": 20000}]}, "priority": 3}, {"id": "sa_portfolio_cat1", "name": "PAR-Based Portfolio Commission", "description": "Fixed amount based on PAR and portfolio size", "enabled": true, "category": "Portfolio Commission", "frequency": "monthly", "conditions": [{"field": "par", "operator": "lte", "value": 5}, {"field": "portfolio", "operator": "gt", "value": 50000}, {"field": "netSales", "operator": "gt", "value": 0}], "calculation": {"type": "tiered", "baseField": "portfolio", "comparisonField": "portfolio", "tierValueType": "fixed", "tiers": [{"min": 50000, "max": 100000, "value": 2000}, {"min": 100000, "max": 200000, "value": 3000}, {"min": 200000, "max": 500000, "value": 5000}, {"min": 500000, "max": null, "value": 8000}]}, "priority": 4}, {"id": "sa_portfolio_cat2", "name": "Portfolio Percentage Commission", "description": "Percentage of portfolio based on quality", "enabled": false, "category": "Portfolio Commission", "frequency": "monthly", "conditions": [{"field": "portfolio", "operator": "gt", "value": 100000}], "calculation": {"type": "formula", "baseField": "portfolio", "formula": "data.par <= 2 ? data.portfolio * 0.01 : (data.par <= 5 ? data.portfolio * 0.0075 : 0)"}, "priority": 5}, {"id": "sa_weekly_loan_type", "name": "Weekly Loan Type Incentive", "description": "Incentive based on weekly sales tiers", "enabled": true, "category": "Weekly Sales", "frequency": "weekly", "conditions": [{"field": "weeklyNetSales", "operator": "gt", "value": 0}], "calculation": {"type": "tiered", "baseField": "weeklyNetSales", "comparisonField": "weeklyNetSales", "tierValueType": "fixed", "tiers": [{"min": 0, "max": 100000, "value": 0}, {"min": 100000, "max": 200000, "value": 2000}, {"min": 200000, "max": 300000, "value": 4000}, {"min": 300000, "max": null, "value": 6000}]}, "priority": 6}, {"id": "sa_weekly_target", "name": "Weekly Target Achievement", "description": "Percentage bonus for reaching weekly target", "enabled": true, "category": "Weekly Sales", "frequency": "daily", "conditions": [{"field": "weeklyNetSales", "operator": "gte", "value": 150000}], "calculation": {"type": "formula", "baseField": "weeklyNetSales", "formula": "data.loanType === \"Buyoff\" ? data.weeklyNetSales * 0.015 : (data.loanType === \"Top-up\" ? data.weeklyNetSales * 0.01 : data.weeklyNetSales * 0.02)"}, "priority": 7}, {"id": "sa_daily_loan_type", "name": "Daily Loan Type Commission", "description": "Fixed amount per loan type daily", "enabled": true, "category": "Daily Sales", "frequency": "daily", "conditions": [{"field": "dailyNetSales", "operator": "gt", "value": 0}], "calculation": {"type": "formula", "baseField": "numberOfLoans", "formula": "data.loanType === \"Buyoff\" ? 300 : (data.loanType === \"Top-up\" ? 250 : 200)"}, "priority": 8}, {"id": "sa_daily_sales_rate", "name": "Daily Sales Rate Commission", "description": "Rate commission based on daily sales brackets", "enabled": false, "category": "Daily Sales", "conditions": [{"field": "dailyNetSales", "operator": "gte", "value": 50000}], "calculation": {"type": "tiered", "baseField": "dailyNetSales", "comparisonField": "dailyNetSales", "tierValueType": "percentage", "tiers": [{"min": 50000, "max": 100000, "value": 1.5}, {"min": 100000, "max": 200000, "value": 2}, {"min": 200000, "max": null, "value": 2.5}]}, "priority": 9, "frequency": "daily"}, {"id": "client_type_rate_formula", "name": "Client Type Rate Commission (Formula)", "description": "Different percentage rates for New vs Repeat clients using formula", "enabled": true, "category": "End Month Sales", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}], "calculation": {"type": "formula", "baseField": "netSales", "formula": "data.clientTyp === \"New\" ? data.netSales * 0.025 : data.netSales * 0.015"}, "priority": 10, "frequency": "monthly"}, {"id": "client_type_fixed_formula", "name": "Client Type Fixed Commission (Formula)", "description": "Fixed amounts for New vs Repeat clients using formula", "enabled": true, "category": "End Month Sales", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}], "calculation": {"type": "formula", "baseField": "netSales", "formula": "data."}, "priority": 11, "frequency": "monthly"}, {"id": "new_client_rate_condition", "name": "New Client Rate Commission (Condition)", "description": "Higher percentage rate for new clients using conditions", "enabled": false, "category": "End Month Sales", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}, {"field": "clientType", "operator": "eq", "value": "New"}], "calculation": {"type": "percentage", "baseField": "netSales", "value": 2.5}, "priority": 12, "frequency": "monthly"}, {"id": "repeat_client_rate_condition", "name": "Repeat Client Rate Commission (Condition)", "description": "Lower percentage rate for repeat clients using conditions", "enabled": false, "category": "End Month Sales", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}, {"field": "clientType", "operator": "eq", "value": "Repeat"}], "calculation": {"type": "percentage", "baseField": "netSales", "value": 1.5}, "priority": 13, "frequency": "monthly"}, {"id": "new_client_fixed_condition", "name": "New Client Fixed Commission (Condition)", "description": "Fixed amount for new clients using conditions", "enabled": false, "category": "End Month Sales", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}, {"field": "clientType", "operator": "eq", "value": "New"}], "calculation": {"type": "fixed", "baseField": "netSales", "value": 5000}, "priority": 14, "frequency": "monthly"}, {"id": "repeat_client_fixed_condition", "name": "Repeat Client Fixed Commission (Condition)", "description": "Fixed amount for repeat clients using conditions", "enabled": false, "category": "End Month Sales", "conditions": [{"field": "netSales", "operator": "gt", "value": 0}, {"field": "clientType", "operator": "eq", "value": "Repeat"}], "calculation": {"type": "fixed", "baseField": "netSales", "value": 3000}, "priority": 15, "frequency": "monthly"}, {"id": "par_portfolio_commission", "name": "PAR-Based Portfolio Commission", "description": "Commission based on PAR percentage and days threshold", "enabled": true, "category": "Portfolio Commission", "conditions": [{"field": "portfolio", "operator": "gt", "value": 50000}, {"field": "par", "operator": "lt", "value": 5}], "calculation": {"type": "formula", "baseField": "portfolio", "formula": "\n        // Adjust commission based on PAR and days threshold\n        const parPenalty = data.parDays <= 30 ? data.par * 0.2 :\n                          data.parDays <= 60 ? data.par * 0.3 :\n                          data.parDays <= 90 ? data.par * 0.4 :\n                          data.par * 0.5;\n\n        const baseCommission = data.portfolio >= 500000 ? 10000 :\n                              data.portfolio >= 200000 ? 6000 :\n                              data.portfolio >= 100000 ? 4000 : 2000;\n\n        // Apply PAR penalty (higher penalty for longer days past due)\n        Math.max(0, baseCommission * (1 - parPenalty / 100))\n      "}, "priority": 16, "frequency": "monthly"}, {"id": "par_threshold_bonus", "name": "PAR Threshold Performance Bonus", "description": "Bonus for maintaining low PAR within specific days threshold", "enabled": false, "category": "Portfolio Commission", "conditions": [{"field": "portfolio", "operator": "gt", "value": 100000}], "calculation": {"type": "formula", "baseField": "portfolio", "formula": "\n        // Bonus based on PAR performance relative to days threshold\n        const parThreshold = data.parDays <= 30 ? 2 :\n                            data.parDays <= 60 ? 3 :\n                            data.parDays <= 90 ? 4 : 5;\n\n        // Bonus for staying below threshold\n        data.par < parThreshold ? (\n          data.par < parThreshold * 0.5 ? 5000 : // Excellent: 50% below threshold\n          data.par < parThreshold * 0.75 ? 3000 : // Good: 25% below threshold\n          1000 // Acceptable: just below threshold\n        ) : 0 // No bonus if above threshold\n      "}, "priority": 17, "frequency": "monthly"}, {"id": "new_loan_par_commission", "name": "New Loan PAR-Based Commission", "description": "KES 1,000 per new loan if PAR < 5% (Class/Category 3 example)", "enabled": true, "category": "Loan Commission", "conditions": [{"field": "par", "operator": "lt", "value": 5}, {"field": "numberOfNewLoans", "operator": "gt", "value": 0}], "calculation": {"type": "per-unit", "baseField": "numberOfNewLoans", "unitAmount": 1000}, "priority": 18, "frequency": "monthly"}, {"id": "weekly_target_loan_type_commission", "name": "Weekly Target Loan Type Commission", "description": "Different rates per loan type when weekly target is met", "enabled": false, "category": "Loan Commission", "conditions": [{"field": "weeklyNetSales", "operator": "gte", "value": 100000}], "calculation": {"type": "formula", "baseField": "numberOfNewLoans", "formula": "\n        data.weeklyNetSales >= data.weeklyTarget ? (\n          data.loanType === \"New\" ? data.numberOfNewLoans * 2500 :\n          data.loanType === \"Repeat\" ? data.numberOfRepeatLoans * 1500 :\n          data.loanType === \"Buyoff\" ? data.numberOfLoans * 2000 :\n          data.numberOfLoans * 1800\n        ) : 0\n      "}, "priority": 19, "frequency": "weekly"}], "fieldConfig": [{"field": "netSales", "type": "number", "label": "Net Sales", "description": "Net sales amount for the period", "required": false, "placeholder": "250000", "validation": {"min": 0, "max": 999999999}}, {"field": "loanTerm", "type": "number", "label": "<PERSON>an <PERSON> (months)", "description": "Loan term in months", "required": false, "placeholder": "24", "validation": {"min": 1, "max": 120}}, {"field": "loanType", "type": "select", "label": "Loan Type", "description": "Type of loan: New, Repeat, Buyoff, Top-up, Partnership", "required": false, "options": ["New", "Repeat", "Buyoff", "Top-up", "Partnership"]}, {"field": "clientType", "type": "select", "label": "Client Type", "description": "Client type: New or Repeat", "required": false, "options": ["New", "Repeat"]}, {"field": "portfolio", "type": "number", "label": "Portfolio <PERSON>", "description": "Portfolio size under management", "required": false, "placeholder": "150000", "validation": {"min": 0, "max": 999999999}}, {"field": "par", "type": "number", "label": "PAR %", "description": "PAR percentage", "required": false, "placeholder": "3.5", "validation": {"min": 0, "max": 100, "step": 0.1}}, {"field": "parDays", "type": "number", "label": "PAR Days Threshold", "description": "Days past due threshold for PAR calculation", "required": false, "placeholder": "30", "validation": {"min": 1, "max": 365}}, {"field": "weeklyTarget", "type": "number", "label": "Weekly Target", "description": "Weekly sales target", "required": false, "placeholder": "100000", "validation": {"min": 0, "max": 999999999}}, {"field": "dailyNetSales", "type": "number", "label": "Daily Net Sales", "description": "Daily net sales amount", "required": false, "placeholder": "35000", "validation": {"min": 0, "max": 999999999}}, {"field": "weeklyNetSales", "type": "number", "label": "Weekly Net Sales", "description": "Weekly net sales amount", "required": false, "placeholder": "180000", "validation": {"min": 0, "max": 999999999}}, {"field": "monthlyNetSales", "type": "number", "label": "Monthly Net Sales", "description": "Monthly net sales amount", "required": false, "placeholder": "750000", "validation": {"min": 0, "max": 999999999}}, {"field": "numberOfLoans", "type": "number", "label": "Total Number of Loans", "description": "Total number of loans", "required": false, "placeholder": "5", "validation": {"min": 0, "max": 9999}}, {"field": "numberOfNewLoans", "type": "number", "label": "Number of New Loans", "description": "Number of new loans only", "required": false, "placeholder": "3", "validation": {"min": 0, "max": 9999}}, {"field": "numberOfRepeatLoans", "type": "number", "label": "Number of Repeat Loans", "description": "Number of repeat loans only", "required": false, "placeholder": "2", "validation": {"min": 0, "max": 9999}}, {"field": "employerType", "type": "select", "label": "Employer Type", "description": "Type of employer", "required": false, "options": ["Government", "Private", "NGO", "Self-Employed"]}], "formulaVariables": [{"name": "data.netSales", "type": "number", "description": "Net sales amount for the period", "category": "Sales Performance"}, {"name": "data.loanTerm", "type": "number", "description": "Loan term in months", "category": "Loan Information"}, {"name": "data.loanType", "type": "string", "description": "Loan type: \"New\", \"Repeat\", \"Buyoff\", \"Top-up\", \"Partnership\"", "category": "Loan Information"}, {"name": "data.clientType", "type": "string", "description": "Client type: \"New\" or \"Repeat\"", "category": "Client & Risk"}, {"name": "data.portfolio", "type": "number", "description": "Portfolio size under management", "category": "Client & Risk"}, {"name": "data.par", "type": "number", "description": "PAR percentage", "category": "Client & Risk"}, {"name": "data.parDays", "type": "number", "description": "Days past due threshold for PAR calculation", "category": "Client & Risk"}, {"name": "data.weeklyTarget", "type": "number", "description": "Weekly sales target", "category": "Sales Performance"}, {"name": "data.dailyNetSales", "type": "number", "description": "Daily net sales amount", "category": "Sales Performance"}, {"name": "data.weeklyNetSales", "type": "number", "description": "Weekly net sales amount", "category": "Sales Performance"}, {"name": "data.monthlyNetSales", "type": "number", "description": "Monthly net sales amount", "category": "Sales Performance"}, {"name": "data.numberOfLoans", "type": "number", "description": "Total number of loans", "category": "Loan Information"}, {"name": "data.numberOfNewLoans", "type": "number", "description": "Number of new loans only", "category": "Loan Information"}, {"name": "data.numberOfRepeatLoans", "type": "number", "description": "Number of repeat loans only", "category": "Loan Information"}, {"name": "data.employerType", "type": "string", "description": "Employer type", "category": "Other"}], "dropdownOptions": {"loanTypes": ["New", "Repeat", "Buyoff", "Top-up", "Partnership"], "clientTypes": ["New", "Repeat"], "employerTypes": ["Government", "Private", "NGO", "Self-Employed"], "frequencies": ["daily", "weekly", "monthly"], "categories": ["End Month Sales", "Weekly Sales", "Daily Sales", "Portfolio Commission", "Additional Commission"], "operators": {"number": [{"value": "eq", "label": "Equals"}, {"value": "gt", "label": "Greater than"}, {"value": "gte", "label": "Greater than or equal"}, {"value": "lt", "label": "Less than"}, {"value": "lte", "label": "Less than or equal"}], "select": [{"value": "eq", "label": "Equals"}]}}, "calculationTypes": [{"type": "percentage", "label": "Percentage", "description": "Apply a percentage rate to a base field", "fields": ["baseField", "value"], "validation": {"value": {"min": 0, "max": 100, "step": 0.01}}}, {"type": "fixed", "label": "Fixed Amount", "description": "Fixed commission amount", "fields": ["value"], "validation": {"value": {"min": 0, "max": 999999999}}}, {"type": "tiered", "label": "Tiered", "description": "Different rates/amounts based on value ranges", "fields": ["baseField", "comparisonField", "tierValueType", "tiers"], "tierValueTypes": ["percentage", "fixed"]}, {"type": "formula", "label": "Formula", "description": "Custom JavaScript formula", "fields": ["baseField", "formula"]}, {"type": "per-unit", "label": "Per Unit", "description": "Fixed amount per unit/item", "fields": ["baseField", "unitAmount"], "validation": {"unitAmount": {"min": 0, "max": 999999999}}}], "formulaExamples": [{"title": "Basic Percentage", "formula": "data.netSales * 0.025", "description": "2.5% of net sales"}, {"title": "Conditional Commission", "formula": "data.par < 5 ? data.numberOfNewLoans * 1000 : 0", "description": "KES 1,000 per new loan if PAR < 5%"}, {"title": "Target Achievement Bonus", "formula": "data.weeklyNetSales >= data.weeklyTarget ? 5000 : 0", "description": "KES 5,000 if weekly target met"}, {"title": "Loan Type Based Rate", "formula": "data.loanType === \"New\" ? data.netSales * 0.025 : data.netSales * 0.015", "description": "Different rates for new vs repeat loans"}, {"title": "Multi-Condition Logic", "formula": "data.par < 5 && data.weeklyNetSales >= data.weeklyTarget ? data.numberOfNewLoans * 1000 + data.numberOfRepeatLoans * 500 : 0", "description": "Bonus for both PAR and target achievement"}], "users": [{"id": "2", "firstName": "A.", "lastName": "Galgalo", "email": "<EMAIL>", "phone": "+************", "employeeId": "EMP001", "department": "Sales", "position": "Sales Agent", "branch": "Nairobi Central", "dateOfBirth": "1990-05-15", "nationalId": "********", "address": "123 Main Street, Nairobi", "emergencyContact": {"name": "<PERSON>", "phone": "+************", "relationship": "Spouse"}, "bankDetails": {"bankName": "KCB Bank", "accountNumber": "**********", "branchCode": "001"}, "lifeCycle": "ACTIVE", "isActive": true, "isAdministrator": true, "createdAt": "2024-01-15T08:00:00Z", "updatedAt": "2024-08-14T10:30:00Z", "roles": ["ADMINISTRATOR"], "permissions": ["VIEW_COMMISSIONS", "EDIT_PROFILE", "MANAGE_USERS", "ADMIN_ACCESS"]}]}