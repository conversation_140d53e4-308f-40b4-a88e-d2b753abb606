dev:
	npm run start:dev
amka:
	sudo pg_ctlcluster 14 main start
restart:
	sudo service postgresql restart	
migrate:
	npx prisma migrate deploy && make generate
migdev:
	npx prisma migrate dev && make generate
undo:
	typeorm migration:revert
generate:
	npx prisma generate
kill:
	fuser -n tcp -k 9000
serve:
	yarn run server		
build:
	rm -rf dist && npm run build	
dock:
	sudo dockerd	
dbrun:
	docker compose up dev-db -d
miniorun:
	docker compose up minio -d
send:
	git push origin 1-dev
js:
	find ./src -name "*.js" -type f	
jsdel:
	find ./src -name "*.js" -type f -delete
seed:
	npx prisma db seed
migrations:
	npx prisma migrate dev
migratedev:
	make migrations && make generate
send:
	git push origin master
dev:
	npm run start:dev			
tst:
	npm run test:e2e	
studio:
	npx prisma studio	
pm2:
	npx prisma migrate deploy && make generate && npm run build && make generate && pm2 start ecosystem.config.js			
del:
	find /home/<USER>/projects/ticketing/ -type f -name '*:Zone.Identifier' -exec rm -f {} +
prodmigrate:
	DATABASE_URL=$(PROD_DATABASE_URL) npx prisma migrate deploy
# Docker commands
docker-up:
	docker compose up -d

docker-down:
	docker compose down

# Start all services
start-all: docker-up
	npm run start:dev

# Stop all services
stop-all: docker-down
	make kill

		
